name: tasks
description: 极简记物，资产管理应用

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.9.9+2099

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: any
  provider: ^6.0.5
  dio: ^5.4.0
  shared_preferences: ^2.2.0
  #  image_picker: ^1.0.7
  path_provider: ^2.1.2
  json_annotation: ^4.9.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  mobx: ^2.5.0
  flutter_mobx: ^2.0.7
  tobias: ^5.1.1
  go_router: ^14.6.2
  flutter_colorpicker: 1.1.0
  package_info_plus: ^8.1.2
  device_info_plus: ^11.2.0
  cached_network_image: ^3.4.1
  fluwx: ^5.4.2
  url_launcher: ^6.1.11
  # 日期时间选择器
  bottom_picker: 2.11.0
  # 渠道
  flutter_flavor: ^3.1.4
  loader_overlay: ^4.0.4+1
  flutter_reorderable_grid_view: 5.5.0
  oktoast: ^3.4.0
  in_app_purchase: ^3.2.1
  # 图片选择器
  image_picker: ^1.1.2
  uuid: ^4.5.1
  chinese_font_library: ^1.2.0
  debounce_throttle: ^2.0.0
  in_date_utils: ^1.2.3
  webdav_client: ^1.2.2
  flutter_slidable: 3.1.2
  flutter_image_compress: ^2.4.0
  # Home widget for native widget support
  home_widget: ^0.6.0
  image_cropper: ^9.1.0
  pull_to_refresh: ^2.0.0
  intl_utils: ^2.8.8
  kt_dart: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0
  build_runner: 2.4.15
  json_serializable: 6.9.0
  mobx_codegen: 2.7.1
  pigeon: ^22.7.0
  flutter_gen_runner: 5.10.0
  injectable_generator: ^2.6.2
  intl_utils: ^2.8.7
flutter:
  generate: true
  assets:
    - assets/images/
    - assets/cache/
  uses-material-design: true
#支付宝支付相关配置
tobias:
  url_scheme: guiwu
# 微信相关初始数据
fluwx:
  app_id: 'wx6d7916b8019763ab'
  debug_logging: true
  android:
    no_pay: true
  ios:
    no_pay: true
# 多语言
flutter_intl:
  enabled: true
# assets生成
flutter_gen:
  output: lib/generated

