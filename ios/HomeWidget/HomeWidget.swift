//
//  HomeWidget.swift
//  HomeWidget
//
//  Created by 郭青山 on 2025/6/17.
//

import WidgetKit
import UIKit
import SwiftUI

struct Provider: TimelineProvider {
    typealias Entry = SimpleEntry

    func placeholder(in context: Context) -> SimpleEntry {
        let data = WidgetDataManager.getWidgetData()
        return SimpleEntry(
            date: Date(),
            appName: data.appName,
            totalPrice: data.totalPrice,
            dailyPrice: data.dailyPrice,
            totalAssets: data.totalAssets,
            maxDailyPrice: data.maxDailyPrice,
            minDailyPrice: data.minDailyPrice,
            isDarkMode: data.isDarkMode
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let data = WidgetDataManager.getWidgetData()
        let entry = SimpleEntry(
            date: Date(),
            appName: data.appName,
            totalPrice: data.totalPrice,
            dailyPrice: data.dailyPrice,
            totalAssets: data.totalAssets,
            maxDailyPrice: data.maxDailyPrice,
            minDailyPrice: data.minDailyPrice,
            isDarkMode: data.isDarkMode
        )
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<SimpleEntry>) -> ()) {
        print("Provider: getTimeline called")
        let data = WidgetDataManager.getWidgetData()
        var entries: [SimpleEntry] = []

        // Generate a timeline with real data, updating every hour
        let currentDate = Date()
        NSLog("Provider: Creating timeline entries starting from: \(currentDate)")

        for hourOffset in 0 ..< 5 {
            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
            let entry = SimpleEntry(
                date: entryDate,
                appName: data.appName,
                totalPrice: data.totalPrice,
                dailyPrice: data.dailyPrice,
                totalAssets: data.totalAssets,
                maxDailyPrice: data.maxDailyPrice,
                minDailyPrice: data.minDailyPrice,
                isDarkMode: data.isDarkMode
            )
            entries.append(entry)
        }

        // 设置下次更新时间为1小时后
        let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: currentDate)!
        let timeline = Timeline(entries: entries, policy: .after(nextUpdate))
        NSLog("Provider: Timeline created with \(entries.count) entries, next update: \(nextUpdate)")
        completion(timeline)
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let appName: String
    let totalPrice: String
    let dailyPrice: String
    let totalAssets: String
    let maxDailyPrice: String
    let minDailyPrice: String
    let isDarkMode: Bool

    init(date: Date,
         appName: String,
         totalPrice: String = "￥0.0",
         dailyPrice: String = "￥0.00",
         totalAssets: String = "0",
         maxDailyPrice: String = "￥0.00",
         minDailyPrice: String = "￥0.00",
         isDarkMode: Bool = false) {
        self.date = date
        self.appName = appName
        self.totalPrice = totalPrice
        self.dailyPrice = dailyPrice
        self.totalAssets = totalAssets
        self.maxDailyPrice = maxDailyPrice
        self.minDailyPrice = minDailyPrice
        self.isDarkMode = isDarkMode

        print("SimpleEntry created at: \(date)")
        print("SimpleEntry data - Total: \(totalPrice), Daily: \(dailyPrice), Assets: \(totalAssets)")
    }
}

struct HomeWidgetEntryView : View {
    var entry: Provider.Entry
    @Environment(\.widgetFamily) var family

    // 系统适配背景
    @Environment(\.colorScheme) var colorScheme

    private var backgroundColor: Color {
        // 优先使用系统颜色方案，回退到entry中的设置
        if colorScheme == .dark {
            return Color(.systemBackground)
        } else {
            return Color(.systemBackground)
        }
    }

    private var solidBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(backgroundColor)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        colorScheme == .dark
                        ? Color.white.opacity(0.1)
                        : Color.black.opacity(0.05),
                        lineWidth: 1
                    )
            )
    }

    private var textColor: Color {
        colorScheme == .dark ? Color.white : Color.black
    }

    private var secondaryTextColor: Color {
        colorScheme == .dark ? Color.white.opacity(0.8) : Color.black.opacity(0.8)
    }

    private var dividerColor: Color {
        colorScheme == .dark ? Color.white.opacity(0.15) : Color.black.opacity(0.1)
    }

    var body: some View {
        Group {
            if family == .systemSmall {
                // 1x1 小组件布局：只显示总资产和总日均
                smallWidgetView
            } else {
                // 中等尺寸小组件布局：显示完整信息
                mediumWidgetView
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(solidBackground)
        .widgetURL(URL(string: "recollect://widget-refresh"))
    }

    // 1x1 小组件视图
    private var smallWidgetView: some View {
        VStack(spacing: 0) {
            // 顶部信息行
            HStack {
                Text(entry.appName)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(textColor)

                Spacer()

                Text(entry.date, style: .time)
                    .font(.system(size: 10))
                    .foregroundColor(secondaryTextColor)
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)
            .padding(.bottom, 6)

            // 分割线
            Rectangle()
                .fill(dividerColor)
                .frame(height: 1)
                .padding(.horizontal, 16)
                .padding(.bottom, 8)

            // 数据区域：总价值和总日均分两排显示
            VStack(spacing: 6) {
                // 第一排：总价值
                SmallStatisticItemView(
                    label: "总价值",
                    value: entry.totalPrice,
                    textColor: textColor,
                    secondaryTextColor: secondaryTextColor
                )

                // 分割线
                Rectangle()
                    .fill(dividerColor)
                    .frame(height: 0.5)
                    .padding(.horizontal, 16)

                // 第二排：总日均
                SmallStatisticItemView(
                    label: "总日均",
                    value: entry.dailyPrice,
                    textColor: textColor,
                    secondaryTextColor: secondaryTextColor
                )
            }
            .frame(maxHeight: .infinity)
            .padding(.horizontal, 16)
            .padding(.bottom, 12)
        }
    }

    // 中等尺寸小组件视图
    private var mediumWidgetView: some View {
        VStack(spacing: 0) {
            // 顶部信息行 - 增加 padding
            HStack {
                Text(entry.appName)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(textColor)

                Spacer()

                Text(entry.date, style: .time)
                    .font(.system(size: 12))
                    .foregroundColor(secondaryTextColor)
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 8)

            // 分割线
            Rectangle()
                .fill(dividerColor)
                .frame(height: 1)
                .padding(.horizontal, 20)
                .padding(.bottom, 8)

            // 数据区域 - 平分剩余空间
            VStack(spacing: 0) {
                // 第一排：总价值和总日均
                HStack(spacing: 0) {
                    StatisticItemView(
                        label: "总价值",
                        value: entry.totalPrice,
                        textColor: textColor,
                        secondaryTextColor: secondaryTextColor
                    )

                    VerticalDivider(color: dividerColor, height: 25)

                    StatisticItemView(
                        label: "总日均",
                        value: entry.dailyPrice,
                        textColor: textColor,
                        secondaryTextColor: secondaryTextColor
                    )
                }
                .frame(maxHeight: .infinity)

                // 第二排：物品数量、最高日均和最低日均
                HStack(spacing: 0) {
                    StatisticItemView(
                        label: "物品数量",
                        value: entry.totalAssets,
                        textColor: textColor,
                        secondaryTextColor: secondaryTextColor
                    )

                    VerticalDivider(color: dividerColor, height: 25)

                    StatisticItemView(
                        label: "最高日均",
                        value: entry.maxDailyPrice,
                        textColor: textColor,
                        secondaryTextColor: secondaryTextColor
                    )

                    VerticalDivider(color: dividerColor, height: 25)

                    StatisticItemView(
                        label: "最低日均",
                        value: entry.minDailyPrice,
                        textColor: textColor,
                        secondaryTextColor: secondaryTextColor
                    )
                }
                .frame(maxHeight: .infinity)
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
    }
}

// 统计项目组件 - 统一字体大小
struct StatisticItemView: View {
    let label: String
    let value: String
    let textColor: Color
    let secondaryTextColor: Color

    var body: some View {
        VStack(spacing: 4) {
            Text(label)
                .font(.system(size: 11))
                .foregroundColor(secondaryTextColor)
                .lineLimit(1)
                .minimumScaleFactor(0.8)

            Text(value)
                .font(.system(size: 15, weight: .bold))
                .foregroundColor(textColor)
                .lineLimit(1)
                .minimumScaleFactor(0.7)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, 8)
    }
}

// 1x1小组件专用统计项目组件 - 优化长文本显示
struct SmallStatisticItemView: View {
    let label: String
    let value: String
    let textColor: Color
    let secondaryTextColor: Color

    var body: some View {
        VStack(spacing: 2) {
            Text(label)
                .font(.system(size: 10))
                .foregroundColor(secondaryTextColor)
                .lineLimit(1)
                .minimumScaleFactor(0.8)

            Text(value)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(textColor)
                .lineLimit(2)  // 允许两行显示
                .minimumScaleFactor(0.6)  // 更小的缩放因子
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 4)
    }
}

// 垂直分割线组件
struct VerticalDivider: View {
    let color: Color
    let height: CGFloat

    var body: some View {
        Rectangle()
            .fill(color)
            .frame(width: 1, height: height)
    }
}

struct HomeWidget: Widget {
    let kind: String = "com.looptry.jiwu.HomeWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            HomeWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("极简记物")
        .description("显示资产统计信息")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

#if DEBUG
struct HomeWidget_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 小尺寸预览
            HomeWidgetEntryView(entry: SimpleEntry(
                date: Date(),
                appName: "极简记物",
                totalPrice: "￥12,345",
                dailyPrice: "￥45.67",
                totalAssets: "123",
                maxDailyPrice: "￥89.12",
                minDailyPrice: "￥12.34"
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))

            // 中等尺寸预览
            HomeWidgetEntryView(entry: SimpleEntry(
                date: Date(),
                appName: "极简记物",
                totalPrice: "￥12,345",
                dailyPrice: "￥45.67",
                totalAssets: "123",
                maxDailyPrice: "￥89.12",
                minDailyPrice: "￥12.34"
            ))
            .previewContext(WidgetPreviewContext(family: .systemMedium))

            // 深色模式预览
            HomeWidgetEntryView(entry: SimpleEntry(
                date: Date(),
                appName: "极简记物",
                totalPrice: "￥12,345",
                dailyPrice: "￥45.67",
                totalAssets: "123",
                maxDailyPrice: "￥89.12",
                minDailyPrice: "￥12.34",
                isDarkMode: true
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            .environment(\.colorScheme, .dark)
        }
    }
}
#endif
