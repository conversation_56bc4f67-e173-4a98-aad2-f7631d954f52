import Foundation
import UIKit

class WidgetDataManager {

    // MARK: - HomeWidget Keys (与Flutter HomeWidgetManager保持一致)
    private enum HomeWidgetKeys {
        static let totalPrice = "total_price"
        static let dailyPrice = "daily_price"
        static let totalAssets = "total_assets"
        static let maxDailyPrice = "max_daily_price"
        static let minDailyPrice = "min_daily_price"
        static let currencySymbol = "currency_symbol"
        static let currencyCode = "currency_code"
        static let appName = "app_name"
        static let lastUpdate = "last_update"
        static let isDarkMode = "is_dark_mode"
    }

    /// 获取小组件数据（优先使用home_widget数据，回退到默认值）
    static func getWidgetData() -> (
        totalPrice: String,
        dailyPrice: String,
        totalAssets: String,
        appName: String,
        maxDailyPrice: String,
        minDailyPrice: String,
        isDarkMode: Bool
    ) {
        guard let userDefaults = UserDefaults(suiteName: "group.com.looptry.jiwu") else {
            print("Error: Failed to access App Group UserDefaults")
            return  (
                totalPrice: "¥0.00",
                dailyPrice: "¥0.00",
                totalAssets: "0",
                appName: "极简记物",
                maxDailyPrice: "¥0.00",
                minDailyPrice: "¥0.00",
                isDarkMode: false
            )// 或者 throw error/fatalError
        }
        // 尝试从home_widget数据读取
        if let homeWidgetData = getHomeWidgetData(from: userDefaults) {
            return homeWidgetData
        }

        // 如果没有home_widget数据，显示默认值
        return (
            totalPrice: "¥0.00",
            dailyPrice: "¥0.00",
            totalAssets: "0",
            appName: "极简记物",
            maxDailyPrice: "¥0.00",
            minDailyPrice: "¥0.00",
            isDarkMode: false
        )
    }

    private static func getHomeWidgetData(from userDefaults: UserDefaults) -> (
        totalPrice: String,
        dailyPrice: String,
        totalAssets: String,
        appName: String,
        maxDailyPrice: String,
        minDailyPrice: String,
        isDarkMode: Bool
    )? {
        // 从UserDefaults获取JSON字符串
        guard let jsonString = userDefaults.string(forKey: "widget_data"),
              let jsonData = jsonString.data(using: .utf8) else {
            return nil
        }
        
        do {
            // 解析JSON数据
            let json = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any]
            
            // 检查必要字段
            guard let totalPrice = json?["totalPrice"] as? Double,
                  let dailyPrice = json?["dailyPrice"] as? Double,
                  let totalAssets = json?["totalAssets"] as? Int else {
                return nil
            }
            
            // 获取其他字段，提供默认值
            let maxDailyPrice = json?["maxDailyPrice"] as? Double ?? 0.0
            let minDailyPrice = json?["minDailyPrice"] as? Double ?? 0.0
            let currencySymbol = json?["currencySymbol"] as? String ?? "¥"
            let appName = json?["appName"] as? String ?? "极简记物"
            let isDarkMode = json?["isDarkMode"] as? Bool ?? false
            
            // 格式化数据
            let formatter = NumberFormatter()
            formatter.numberStyle = .decimal
            formatter.minimumFractionDigits = 2
            formatter.maximumFractionDigits = 2
            
            return (
                totalPrice: "\(currencySymbol)\(formatter.string(from: NSNumber(value: totalPrice)) ?? "0.00")",
                dailyPrice: "\(currencySymbol)\(formatter.string(from: NSNumber(value: dailyPrice)) ?? "0.00")",
                totalAssets: "\(totalAssets)",
                appName: appName,
                maxDailyPrice: "\(currencySymbol)\(formatter.string(from: NSNumber(value: maxDailyPrice)) ?? "0.00")",
                minDailyPrice: "\(currencySymbol)\(formatter.string(from: NSNumber(value: minDailyPrice)) ?? "0.00")",
                isDarkMode: isDarkMode
            )
        } catch {
            print("Error parsing widget data: \(error)")
            return nil
        }
    }


}

// MARK: - 数据结构

/// 资产统计信息
struct AssetStatistics {
    let totalPrice: String
    let dailyPrice: String
    let totalAssets: String
    let maxDailyPrice: String
    let minDailyPrice: String

    static func empty() -> AssetStatistics {
        return AssetStatistics(
            totalPrice: "¥0.00",
            dailyPrice: "¥0.00",
            totalAssets: "0",
            maxDailyPrice: "¥0.00",
            minDailyPrice: "¥0.00"
        )
    }
}
