// Autogenerated from <PERSON>eon (v22.7.2), do not edit directly.
// See also: https://pub.dev/packages/pigeon

import Foundation

#if os(iOS)
  import Flutter
#elseif os(macOS)
  import FlutterMacOS
#else
  #error("Unsupported platform.")
#endif

/// Error class for passing custom error details to Dart side.
final class PigeonError: Error {
  let code: String
  let message: String?
  let details: Any?

  init(code: String, message: String?, details: Any?) {
    self.code = code
    self.message = message
    self.details = details
  }

  var localizedDescription: String {
    return
      "PigeonError(code: \(code), message: \(message ?? "<nil>"), details: \(details ?? "<nil>")"
      }
}

private func wrapResult(_ result: Any?) -> [Any?] {
  return [result]
}

private func wrapError(_ error: Any) -> [Any?] {
  if let pigeonError = error as? PigeonError {
    return [
      pigeonError.code,
      pigeonError.message,
      pigeonError.details,
    ]
  }
  if let flutterError = error as? FlutterError {
    return [
      flutterError.code,
      flutterError.message,
      flutterError.details,
    ]
  }
  return [
    "\(error)",
    "\(type(of: error))",
    "Stacktrace: \(Thread.callStackSymbols)",
  ]
}

private func createConnectionError(withChannelName channelName: String) -> PigeonError {
  return PigeonError(code: "channel-error", message: "Unable to establish connection on channel: '\(channelName)'.", details: "")
}

private func isNullish(_ value: Any?) -> Bool {
  return value is NSNull || value == nil
}

private func nilOrValue<T>(_ value: Any?) -> T? {
  if value is NSNull { return nil }
  return value as! T?
}

/// Generated class from Pigeon that represents data sent in messages.
struct WidgetData {
  /// 总资产价值
  var totalPrice: Double
  /// 日均价值
  var dailyPrice: Double
  /// 总资产数量
  var totalAssets: Int64
  /// 最高日均价值
  var maxDailyPrice: Double
  /// 最低日均价值
  var minDailyPrice: Double
  /// 货币符号
  var currencySymbol: String
  /// 货币代码
  var currencyCode: String
  /// 应用名称
  var appName: String
  /// 最后更新时间
  var lastUpdate: Int64


  // swift-format-ignore: AlwaysUseLowerCamelCase
  static func fromList(_ pigeonVar_list: [Any?]) -> WidgetData? {
    let totalPrice = pigeonVar_list[0] as! Double
    let dailyPrice = pigeonVar_list[1] as! Double
    let totalAssets = pigeonVar_list[2] as! Int64
    let maxDailyPrice = pigeonVar_list[3] as! Double
    let minDailyPrice = pigeonVar_list[4] as! Double
    let currencySymbol = pigeonVar_list[5] as! String
    let currencyCode = pigeonVar_list[6] as! String
    let appName = pigeonVar_list[7] as! String
    let lastUpdate = pigeonVar_list[8] as! Int64

    return WidgetData(
      totalPrice: totalPrice,
      dailyPrice: dailyPrice,
      totalAssets: totalAssets,
      maxDailyPrice: maxDailyPrice,
      minDailyPrice: minDailyPrice,
      currencySymbol: currencySymbol,
      currencyCode: currencyCode,
      appName: appName,
      lastUpdate: lastUpdate
    )
  }
  func toList() -> [Any?] {
    return [
      totalPrice,
      dailyPrice,
      totalAssets,
      maxDailyPrice,
      minDailyPrice,
      currencySymbol,
      currencyCode,
      appName,
      lastUpdate,
    ]
  }
}

/// 小组件主题配置
///
/// Generated class from Pigeon that represents data sent in messages.
struct WidgetTheme {
  /// 是否为深色模式
  var isDarkMode: Bool
  /// 主色调
  var primaryColor: Int64
  /// 次要色调
  var secondaryColor: Int64
  /// 背景色
  var backgroundColor: Int64
  /// 文字颜色
  var textColor: Int64


  // swift-format-ignore: AlwaysUseLowerCamelCase
  static func fromList(_ pigeonVar_list: [Any?]) -> WidgetTheme? {
    let isDarkMode = pigeonVar_list[0] as! Bool
    let primaryColor = pigeonVar_list[1] as! Int64
    let secondaryColor = pigeonVar_list[2] as! Int64
    let backgroundColor = pigeonVar_list[3] as! Int64
    let textColor = pigeonVar_list[4] as! Int64

    return WidgetTheme(
      isDarkMode: isDarkMode,
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      backgroundColor: backgroundColor,
      textColor: textColor
    )
  }
  func toList() -> [Any?] {
    return [
      isDarkMode,
      primaryColor,
      secondaryColor,
      backgroundColor,
      textColor,
    ]
  }
}

private class WidgetApiPigeonCodecReader: FlutterStandardReader {
  override func readValue(ofType type: UInt8) -> Any? {
    switch type {
    case 129:
      return WidgetData.fromList(self.readValue() as! [Any?])
    case 130:
      return WidgetTheme.fromList(self.readValue() as! [Any?])
    default:
      return super.readValue(ofType: type)
    }
  }
}

private class WidgetApiPigeonCodecWriter: FlutterStandardWriter {
  override func writeValue(_ value: Any) {
    if let value = value as? WidgetData {
      super.writeByte(129)
      super.writeValue(value.toList())
    } else if let value = value as? WidgetTheme {
      super.writeByte(130)
      super.writeValue(value.toList())
    } else {
      super.writeValue(value)
    }
  }
}

private class WidgetApiPigeonCodecReaderWriter: FlutterStandardReaderWriter {
  override func reader(with data: Data) -> FlutterStandardReader {
    return WidgetApiPigeonCodecReader(data: data)
  }

  override func writer(with data: NSMutableData) -> FlutterStandardWriter {
    return WidgetApiPigeonCodecWriter(data: data)
  }
}

class WidgetApiPigeonCodec: FlutterStandardMessageCodec, @unchecked Sendable {
  static let shared = WidgetApiPigeonCodec(readerWriter: WidgetApiPigeonCodecReaderWriter())
}

/// Flutter调用原生的接口
///
/// Generated protocol from Pigeon that represents a handler of messages from Flutter.
protocol WidgetHostApi {
  /// 更新小组件数据
  func updateWidget(data: WidgetData, theme: WidgetTheme) throws
  /// 检查小组件是否已添加
  func isWidgetAdded() throws -> Bool
  /// 打开系统小组件设置页面
  func openWidgetSettings() throws
}

/// Generated setup class from Pigeon to handle messages through the `binaryMessenger`.
class WidgetHostApiSetup {
  static var codec: FlutterStandardMessageCodec { WidgetApiPigeonCodec.shared }
  /// Sets up an instance of `WidgetHostApi` to handle messages through the `binaryMessenger`.
  static func setUp(binaryMessenger: FlutterBinaryMessenger, api: WidgetHostApi?, messageChannelSuffix: String = "") {
    let channelSuffix = messageChannelSuffix.count > 0 ? ".\(messageChannelSuffix)" : ""
    /// 更新小组件数据
    let updateWidgetChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.tasks.WidgetHostApi.updateWidget\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      updateWidgetChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let dataArg = args[0] as! WidgetData
        let themeArg = args[1] as! WidgetTheme
        do {
          try api.updateWidget(data: dataArg, theme: themeArg)
          reply(wrapResult(nil))
        } catch {
          reply(wrapError(error))
        }
      }
    } else {
      updateWidgetChannel.setMessageHandler(nil)
    }
    /// 检查小组件是否已添加
    let isWidgetAddedChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.tasks.WidgetHostApi.isWidgetAdded\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      isWidgetAddedChannel.setMessageHandler { _, reply in
        do {
          let result = try api.isWidgetAdded()
          reply(wrapResult(result))
        } catch {
          reply(wrapError(error))
        }
      }
    } else {
      isWidgetAddedChannel.setMessageHandler(nil)
    }
    /// 打开系统小组件设置页面
    let openWidgetSettingsChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.tasks.WidgetHostApi.openWidgetSettings\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      openWidgetSettingsChannel.setMessageHandler { _, reply in
        do {
          try api.openWidgetSettings()
          reply(wrapResult(nil))
        } catch {
          reply(wrapError(error))
        }
      }
    } else {
      openWidgetSettingsChannel.setMessageHandler(nil)
    }
  }
}
/// 原生调用Flutter的接口
///
/// Generated protocol from Pigeon that represents Flutter messages that can be called from Swift.
protocol WidgetFlutterApiProtocol {
  func requestWidgetUpdate(completion: @escaping (Result<WidgetData?, PigeonError>) -> Void)
  /// 小组件被点击
  func onWidgetClicked(action actionArg: String, completion: @escaping (Result<Void, PigeonError>) -> Void)
}
class WidgetFlutterApi: WidgetFlutterApiProtocol {
  private let binaryMessenger: FlutterBinaryMessenger
  private let messageChannelSuffix: String
  init(binaryMessenger: FlutterBinaryMessenger, messageChannelSuffix: String = "") {
    self.binaryMessenger = binaryMessenger
    self.messageChannelSuffix = messageChannelSuffix.count > 0 ? ".\(messageChannelSuffix)" : ""
  }
  var codec: WidgetApiPigeonCodec {
    return WidgetApiPigeonCodec.shared
  }
  func requestWidgetUpdate(completion: @escaping (Result<WidgetData?, PigeonError>) -> Void) {
    let channelName: String = "dev.flutter.pigeon.tasks.WidgetFlutterApi.requestWidgetUpdate\(messageChannelSuffix)"
    let channel = FlutterBasicMessageChannel(name: channelName, binaryMessenger: binaryMessenger, codec: codec)
    channel.sendMessage(nil) { response in
      guard let listResponse = response as? [Any?] else {
        completion(.failure(createConnectionError(withChannelName: channelName)))
        return
      }
      if listResponse.count > 1 {
        let code: String = listResponse[0] as! String
        let message: String? = nilOrValue(listResponse[1])
        let details: String? = nilOrValue(listResponse[2])
        completion(.failure(PigeonError(code: code, message: message, details: details)))
      } else {
        let result: WidgetData? = nilOrValue(listResponse[0])
        completion(.success(result))
      }
    }
  }
  /// 小组件被点击
  func onWidgetClicked(action actionArg: String, completion: @escaping (Result<Void, PigeonError>) -> Void) {
    let channelName: String = "dev.flutter.pigeon.tasks.WidgetFlutterApi.onWidgetClicked\(messageChannelSuffix)"
    let channel = FlutterBasicMessageChannel(name: channelName, binaryMessenger: binaryMessenger, codec: codec)
    channel.sendMessage([actionArg] as [Any?]) { response in
      guard let listResponse = response as? [Any?] else {
        completion(.failure(createConnectionError(withChannelName: channelName)))
        return
      }
      if listResponse.count > 1 {
        let code: String = listResponse[0] as! String
        let message: String? = nilOrValue(listResponse[1])
        let details: String? = nilOrValue(listResponse[2])
        completion(.failure(PigeonError(code: code, message: message, details: details)))
      } else {
        completion(.success(Void()))
      }
    }
  }
}
