import Foundation
import WidgetKit

class IOSWidgetManager: WidgetHostApi {

    // MARK: - SharedPreferences Keys (与Flutter保持一致)
    private enum Keys {
        static let assets = "flutter.assets"
        static let userPreferences = "flutter.user_preferences"
        static let excludeRetiredFromTotal = "flutter.excludeRetiredFromTotal"
        static let widgetStatistics = "flutter.widget_statistics"
        static let lastUpdate = "flutter.widget_last_update"
    }

    // MARK: - Shared UserDefaults
    private let userDefaults: UserDefaults

    // MARK: - Flutter API
    private var flutterApi: WidgetFlutterApi?
    
    init() {
        // 使用 App Group 来在主应用和小组件之间共享数据
        if let sharedDefaults = UserDefaults(suiteName: "group.com.looptry.jiwu") {
            self.userDefaults = sharedDefaults
        } else {
            // 如果 App Group 未配置，回退到标准 UserDefaults
            self.userDefaults = UserDefaults.standard
        }
    }



    // MARK: - Flutter API Setup
    func setFlutterApi(_ api: WidgetFlutterApi) {
        self.flutterApi = api
        print("IOSWidgetManager: Flutter API set successfully")
    }
    
    // MARK: - WidgetHostApi Implementation
    
    func updateWidget(data: WidgetData, theme: WidgetTheme) throws {
        // 记录当前时间戳
        let timestamp = Date().timeIntervalSince1970
        userDefaults.set(timestamp, forKey: Keys.lastUpdate)

        // 保存小组件统计数据到缓存（用于小组件显示）
        let statisticsData: [String: Any] = [
            "totalPrice": data.totalPrice,
            "dailyPrice": data.dailyPrice,
            "totalAssets": data.totalAssets,
            "maxDailyPrice": data.maxDailyPrice,
            "minDailyPrice": data.minDailyPrice,
            "currencySymbol": data.currencySymbol,
            "currencyCode": data.currencyCode,
            "appName": data.appName,
            "lastUpdate": data.lastUpdate
        ]

        if let jsonData = try? JSONSerialization.data(withJSONObject: statisticsData),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            userDefaults.set(jsonString, forKey: Keys.widgetStatistics)
        }

        userDefaults.synchronize()

        // 刷新小组件显示
        WidgetCenter.shared.reloadAllTimelines()
    }
    
    func isWidgetAdded() throws -> Bool {
        // iOS 无法直接检测小组件是否已添加到主屏幕
        // 这里返回 true，表示小组件功能可用
        return true
    }
    
    func openWidgetSettings() throws {
        // iOS 无法直接打开小组件设置页面
        // 可以考虑打开应用设置页面或显示提示
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            DispatchQueue.main.async {
                UIApplication.shared.open(settingsUrl)
            }
        }
    }
    
    // MARK: - Data Access Methods for Widget
    
    /// 获取小组件数据（直接从Flutter SharedPreferences读取）
    static func getWidgetData() -> (
        totalPrice: String,
        dailyPrice: String,
        totalAssets: String,
        appName: String,
        maxDailyPrice: String,
        minDailyPrice: String,
        isDarkMode: Bool
    ) {
        let userDefaults: UserDefaults
        if let sharedDefaults = UserDefaults(suiteName: "group.com.looptry.jiwu") {
            userDefaults = sharedDefaults
        } else {
            userDefaults = UserDefaults.standard
        }

        // 尝试从缓存读取预计算的统计数据，如果失败则实时计算
        let statistics = getAssetStatisticsFromCache(from: userDefaults)

        return (
            totalPrice: statistics.totalPrice,
            dailyPrice: statistics.dailyPrice,
            totalAssets: statistics.totalAssets,
            appName: "极简记物",
            maxDailyPrice: statistics.maxDailyPrice,
            minDailyPrice: statistics.minDailyPrice,
            isDarkMode: isDarkMode(from: userDefaults)
        )
    }
    
    /// 从缓存获取预计算的统计数据
    private static func getAssetStatisticsFromCache(from userDefaults: UserDefaults) -> (
        totalPrice: String,
        dailyPrice: String,
        totalAssets: String,
        maxDailyPrice: String,
        minDailyPrice: String
    ) {
        // 尝试从缓存读取预计算的统计数据
        if let statisticsJsonString = userDefaults.string(forKey: Keys.widgetStatistics),
           let statisticsData = statisticsJsonString.data(using: .utf8),
           let statisticsDict = try? JSONSerialization.jsonObject(with: statisticsData) as? [String: Any] {

            let currencySymbol = statisticsDict["currencySymbol"] as? String ?? "¥"
            let totalPrice = statisticsDict["totalPrice"] as? Double ?? 0.0
            let dailyPrice = statisticsDict["dailyPrice"] as? Double ?? 0.0
            let totalAssets = statisticsDict["totalAssets"] as? Int ?? 0
            let maxDailyPrice = statisticsDict["maxDailyPrice"] as? Double ?? 0.0
            let minDailyPrice = statisticsDict["minDailyPrice"] as? Double ?? 0.0

            return (
                totalPrice: formatCurrency(totalPrice, symbol: currencySymbol),
                dailyPrice: formatCurrency(dailyPrice, symbol: currencySymbol),
                totalAssets: String(totalAssets),
                maxDailyPrice: formatCurrency(maxDailyPrice, symbol: currencySymbol),
                minDailyPrice: formatCurrency(minDailyPrice, symbol: currencySymbol)
            )
        }

        // 如果缓存不存在，返回默认值
        return (
            totalPrice: "¥0.00",
            dailyPrice: "¥0.00",
            totalAssets: "0",
            maxDailyPrice: "¥0.00",
            minDailyPrice: "¥0.00"
        )
    }



    /// 格式化货币
    private static func formatCurrency(_ amount: Double, symbol: String) -> String {
        return String(format: "%@%.2f", symbol, amount)
    }

    /// 获取最后更新时间
    static func getLastUpdateTime() -> Date {
        let userDefaults: UserDefaults
        if let sharedDefaults = UserDefaults(suiteName: "group.com.looptry.jiwu") {
            userDefaults = sharedDefaults
        } else {
            userDefaults = UserDefaults.standard
        }

        let timestamp = userDefaults.double(forKey: Keys.lastUpdate)
        return timestamp > 0 ? Date(timeIntervalSince1970: timestamp) : Date()
    }
}
