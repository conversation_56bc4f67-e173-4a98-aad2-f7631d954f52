#!/bin/bash

# 配置参数
BASE_DIR="lib/l10n"
MODULES=("common" "home" "setting" "asset" "ucenter" "account" "activity")  # 需要合并的模块目录
LANGUAGES=("en" "zh_CN" "zh_TW")       # 支持的语言

# 清理旧文件
rm -f $BASE_DIR/app_*.arb

# 按语言合并
for lang in "${LANGUAGES[@]}"; do
  OUTPUT_FILE="$BASE_DIR/intl_${lang}.arb"
  TEMP_FILE="${OUTPUT_FILE}.tmp"

  # 初始化合并文件
  echo "{}" > $TEMP_FILE

  # 合并模块文件
  for module in "${MODULES[@]}"; do
    MODULE_FILE="$BASE_DIR/$module/${module}_${lang}.arb"
    if [ -f "$MODULE_FILE" ]; then
      jq -s '.[0] * .[1]' $TEMP_FILE $MODULE_FILE > $TEMP_FILE.tmp
      mv $TEMP_FILE.tmp $TEMP_FILE
    fi
  done

  # 格式化输出
  jq --sort-keys . $TEMP_FILE > $OUTPUT_FILE
  rm $TEMP_FILE
done

echo "ARB files merged successfully!"

# 执行flutter_intl生成
echo "Generating l10n files with flutter_intl..."
if flutter packages pub run intl_utils:generate; then
    echo "L10n files generated successfully!"
else
    echo "Failed to generate l10n files, trying alternative method..."
    # 如果intl_utils不可用，尝试使用flutter pub run
    if flutter pub run intl_utils:generate; then
        echo "L10n files generated successfully with alternative method!"
    else
        echo "Warning: Could not generate l10n files automatically"
        echo "Please run 'flutter packages pub run intl_utils:generate' manually"
    fi
fi