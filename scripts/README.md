# Flutter 多渠道构建脚本

本目录包含了 Flutter 项目的多渠道构建脚本，支持自动化的预处理和多渠道APK打包。

## 脚本文件说明

### 主要脚本

- **`build.sh`** - 主入口脚本，提供统一的构建接口
- **`build_channels.sh`** - 多渠道批量打包脚本
- **`build_single_channel.sh`** - 单渠道打包脚本
- **`preprocess.sh`** - 预处理脚本，包含多语言合并和代码生成
- **`merge_arb.sh`** - 多语言文件合并脚本
- **`check_android_env.sh`** - Android构建环境检查脚本

## 使用方法

### 1. 打包所有渠道
```bash
./scripts/build.sh
# 或者
./scripts/build.sh all
```

### 2. 打包单个渠道
```bash
./scripts/build.sh single [渠道名]

# 示例
./scripts/build.sh single kuan      # 打包酷安渠道
./scripts/build.sh single huawei    # 打包华为渠道
./scripts/build.sh single default   # 打包默认渠道
```

### 3. 仅执行预处理步骤
```bash
./scripts/build.sh preprocess
```

### 4. 检查Android构建环境
```bash
./scripts/build.sh check-env
```

### 5. 显示帮助信息
```bash
./scripts/build.sh help
```

### 6. 直接调用具体脚本
```bash
# 多渠道打包
./scripts/build_channels.sh

# 单渠道打包
./scripts/build_single_channel.sh kuan
```

## 支持的渠道

| 渠道代码 | 渠道名称 | 说明 |
|---------|---------|------|
| default | 默认 | 默认渠道，不添加 CHANNEL 参数 |
| kuan | 酷安 | 酷安应用市场 |
| huawei | 华为应用商店 | 华为应用市场 |
| qq | 应用宝 | 腾讯应用宝 |
| meizu | 魅族 | 魅族应用商店 |
| xiaomi | 小米 | 小米应用商店 |
| vivo | Vivo | Vivo应用商店 |
| oppo | Oppo | Oppo应用商店 |
| honor | Honor | 荣耀应用市场 |
| alibaba | 阿里巴巴 | 阿里巴巴应用分发 |

## 构建流程

每次构建都会自动执行以下预处理步骤：

1. **多语言文件合并** - 执行 `merge_arb.sh` 合并各模块的多语言文件
2. **国际化文件生成** - 执行 `flutter packages pub run intl_utils:generate` 生成多语言代码（保持S.current兼容性）
3. **依赖获取** - 执行 `flutter pub get` 获取最新依赖
4. **代码生成** - 执行 `flutter packages pub run build_runner build --delete-conflicting-outputs`
5. **资源文件生成** - 生成 assets.gen.dart 等资源文件
6. **APK构建** - 根据渠道执行相应的构建命令

## 输出目录

构建完成后，APK文件会按渠道分类存储：

```
build/channels/
├── default/          # 默认渠道APK
├── kuan/            # 酷安渠道APK
├── huawei/          # 华为渠道APK
├── qq/              # 应用宝渠道APK
├── meizu/           # 魅族渠道APK
├── xiaomi/          # 小米渠道APK
├── vivo/            # Vivo渠道APK
├── oppo/            # Oppo渠道APK
├── honor/           # Honor渠道APK
└── alibaba/         # 阿里巴巴渠道APK
```

## 构建参数

所有渠道使用统一的构建参数：
```bash
--release --split-per-abi --obfuscate --split-debug-info=./build
```

## CI/CD 集成

GitHub Actions 工作流已配置为使用这些脚本：
- 当推送到 `release/google` 分支且提交信息包含 `version:` 时自动触发
- 自动安装必要的依赖工具（如 jq）
- 自动配置Android构建环境（SDK Platform 33、NDK 27.0.12077973、Build Tools 33.0.2）
- 自动接受Android SDK许可证
- 自动执行多渠道构建并上传构建产物
- 使用最新版本的GitHub Actions（checkout@v4, setup-java@v4, flutter-action@v2, upload-artifact@v4）

### 依赖要求

构建脚本需要以下工具：
- **Flutter SDK 3.29.2** - 用于构建应用
- **Java 21** - Android构建环境
- **jq** - 用于处理JSON格式的ARB文件合并
- **intl_utils** - 用于生成多语言文件

### Android构建环境

CI/CD环境会自动安装以下Android组件：
- **Android SDK Platform 33** - 目标API级别
- **NDK 27.0.12077973** - 原生开发工具包
- **Build Tools 33.0.2** - 构建工具

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   chmod +x scripts/*.sh
   ```

2. **路径问题**
   - 确保在项目根目录执行脚本
   - 脚本会自动切换到正确的工作目录

3. **依赖问题**
   - 确保已安装 Flutter SDK
   - 确保已安装 jq 工具（用于多语言合并）

### 日志说明

脚本使用彩色日志输出：
- 🔵 **[INFO]** - 信息提示
- 🟢 **[SUCCESS]** - 成功状态
- 🟡 **[WARNING]** - 警告信息
- 🔴 **[ERROR]** - 错误信息
