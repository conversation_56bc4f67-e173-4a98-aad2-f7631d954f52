#!/bin/bash

# 多渠道打包脚本
# 支持同时打包多个渠道的APK文件

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的日志
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 渠道配置
CHANNELS=(
    "default:默认"
    "kuan:酷安"
    "huawei:华为应用商店"
    "qq:应用宝"
    "meizu:魅族"
    "xiaomi:小米"
    "vivo:Vivo"
    "oppo:Oppo"
    "honor:Honor"
    "alibaba:阿里巴巴"
)

# 构建参数
BUILD_ARGS="--release --split-per-abi --obfuscate --split-debug-info=./build"

# 输出目录
OUTPUT_DIR="build/channels"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 统计变量
TOTAL_CHANNELS=${#CHANNELS[@]}
SUCCESS_COUNT=0
FAILED_CHANNELS=()

log_info "开始多渠道打包，共 $TOTAL_CHANNELS 个渠道"
log_info "构建参数: $BUILD_ARGS"
echo "----------------------------------------"

# 执行预处理步骤
log_info "执行预处理步骤..."
if [ -f "./scripts/preprocess.sh" ]; then
    if ./scripts/preprocess.sh; then
        log_success "预处理步骤完成"
    else
        log_error "预处理步骤失败"
        exit 1
    fi
else
    log_error "未找到预处理脚本"
    exit 1
fi

echo "----------------------------------------"
log_success "预处理完成，开始渠道打包..."
echo "----------------------------------------"

# 遍历所有渠道进行打包
for channel_info in "${CHANNELS[@]}"; do
    IFS=':' read -r channel_name channel_desc <<< "$channel_info"
    
    log_info "正在打包渠道: $channel_desc ($channel_name)"
    
    # 构建命令
    if [ "$channel_name" = "default" ]; then
        BUILD_CMD="flutter build apk $BUILD_ARGS"
    else
        BUILD_CMD="flutter build apk --dart-define=CHANNEL=$channel_name $BUILD_ARGS"
    fi
    
    log_info "执行命令: $BUILD_CMD"
    
    # 执行构建
    if eval "$BUILD_CMD"; then
        log_success "渠道 $channel_desc 打包成功"
        
        # 创建渠道目录
        CHANNEL_DIR="$OUTPUT_DIR/$channel_name"
        mkdir -p "$CHANNEL_DIR"
        
        # 复制APK文件到对应渠道目录
        if [ -d "build/app/outputs/flutter-apk" ]; then
            cp build/app/outputs/flutter-apk/*.apk "$CHANNEL_DIR/"
            log_info "APK文件已复制到: $CHANNEL_DIR"
        fi
        
        ((SUCCESS_COUNT++))
    else
        log_error "渠道 $channel_desc 打包失败"
        FAILED_CHANNELS+=("$channel_desc")
    fi
    
    echo "----------------------------------------"
done

# 打包结果统计
echo ""
log_info "打包完成统计:"
log_success "成功: $SUCCESS_COUNT/$TOTAL_CHANNELS"

if [ ${#FAILED_CHANNELS[@]} -gt 0 ]; then
    log_error "失败的渠道:"
    for failed in "${FAILED_CHANNELS[@]}"; do
        echo "  - $failed"
    done
    exit 1
else
    log_success "所有渠道打包成功！"
    log_info "输出目录: $OUTPUT_DIR"
fi
