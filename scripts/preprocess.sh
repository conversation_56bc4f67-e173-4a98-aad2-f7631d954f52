#!/bin/bash

# Flutter 项目预处理脚本
# 包含多语言合并、代码生成、依赖获取等通用步骤

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 切换到项目根目录
cd "$PROJECT_ROOT"

log_step "开始 Flutter 项目预处理"

# 步骤1: 多语言文件合并
log_info "步骤 1/4: 合并多语言文件"
if [ -f "$SCRIPT_DIR/merge_arb.sh" ]; then
    if "$SCRIPT_DIR/merge_arb.sh"; then
        log_success "多语言文件合并完成"
    else
        log_error "多语言文件合并失败"
        exit 1
    fi
else
    log_warning "未找到多语言合并脚本，跳过此步骤"
fi

# 步骤2: 获取项目依赖
log_info "步骤 2/4: 获取项目依赖"
if flutter pub get; then
    log_success "依赖获取完成"
else
    log_error "依赖获取失败"
    exit 1
fi

# 步骤3: 执行代码生成 (build_runner)
log_info "步骤 3/4: 执行代码生成"
if flutter packages pub run build_runner build --delete-conflicting-outputs; then
    log_success "代码生成完成"
else
    log_error "代码生成失败"
    exit 1
fi

# 步骤4: 生成资源文件 (flutter_gen)
log_info "步骤 4/4: 生成资源文件"
if flutter packages pub run build_runner build --build-filter="lib/generated/assets.gen.dart"; then
    log_success "资源文件生成完成"
else
    log_warning "资源文件生成失败，但继续执行"
fi

log_step "预处理完成"
log_success "所有预处理步骤已完成，项目已准备好进行构建"
