#!/bin/bash

# 单渠道打包脚本
# 用法: ./build_single_channel.sh [channel_name]
# 示例: ./build_single_channel.sh kuan

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取渠道参数
CHANNEL=${1:-"default"}

# 获取渠道描述
get_channel_desc() {
    case "$1" in
        "default") echo "默认" ;;
        "kuan") echo "酷安" ;;
        "huawei") echo "华为应用商店" ;;
        "qq") echo "应用宝" ;;
        "meizu") echo "魅族" ;;
        "xiaomi") echo "小米" ;;
        "vivo") echo "Vivo" ;;
        "oppo") echo "Oppo" ;;
        "honor") echo "Honor" ;;
        "alibaba") echo "阿里巴巴" ;;
        *) echo "" ;;
    esac
}

# 检查渠道是否支持
DESC=$(get_channel_desc "$CHANNEL")
if [ -z "$DESC" ]; then
    log_error "不支持的渠道: $CHANNEL"
    echo "支持的渠道: default kuan huawei qq meizu xiaomi vivo oppo honor alibaba"
    exit 1
fi
BUILD_ARGS="--release --split-per-abi --obfuscate --split-debug-info=./build"

log_info "开始打包渠道: $DESC ($CHANNEL)"
echo "----------------------------------------"

# 执行预处理步骤
log_info "执行预处理步骤..."
if [ -f "./scripts/preprocess.sh" ]; then
    if ./scripts/preprocess.sh; then
        log_success "预处理步骤完成"
    else
        log_error "预处理步骤失败"
        exit 1
    fi
else
    log_error "未找到预处理脚本"
    exit 1
fi

echo "----------------------------------------"
log_success "预处理完成，开始构建..."
echo "----------------------------------------"

# 构建命令
if [ "$CHANNEL" = "default" ]; then
    BUILD_CMD="flutter build apk $BUILD_ARGS"
else
    BUILD_CMD="flutter build apk --dart-define=CHANNEL=$CHANNEL $BUILD_ARGS"
fi

log_info "执行命令: $BUILD_CMD"

# 执行构建
if eval "$BUILD_CMD"; then
    log_success "渠道 $DESC 打包成功"
    
    # 创建输出目录
    OUTPUT_DIR="build/channels/$CHANNEL"
    mkdir -p "$OUTPUT_DIR"
    
    # 复制APK文件
    if [ -d "build/app/outputs/apk/release" ]; then
        cp build/app/outputs/apk/release/*.* "$OUTPUT_DIR/"
        log_success "APK文件已复制到: $OUTPUT_DIR"
        
        # 列出生成的文件
        echo ""
        log_info "生成的APK文件:"
        ls -la "$OUTPUT_DIR"/*.apk
    fi
else
    log_error "渠道 $DESC 打包失败"
    exit 1
fi
