#!/bin/bash

# Flutter 项目构建主入口脚本
# 用法: 
#   ./scripts/build.sh                    # 打包所有渠道
#   ./scripts/build.sh single [channel]   # 打包单个渠道
#   ./scripts/build.sh help              # 显示帮助信息

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN} $1${NC}"
    echo -e "${CYAN}========================================${NC}"
}

# 显示帮助信息
show_help() {
    log_header "Flutter 多渠道构建工具"
    echo ""
    echo "用法:"
    echo "  ./scripts/build.sh                    # 打包所有渠道"
    echo "  ./scripts/build.sh single [channel]   # 打包单个渠道"
    echo "  ./scripts/build.sh preprocess         # 仅执行预处理步骤"
    echo "  ./scripts/build.sh check-env          # 检查Android构建环境"
    echo "  ./scripts/build.sh help              # 显示此帮助信息"
    echo ""
    echo "支持的渠道:"
    echo "  default   - 默认渠道"
    echo "  kuan      - 酷安"
    echo "  huawei    - 华为应用商店"
    echo "  qq        - 应用宝"
    echo "  meizu     - 魅族"
    echo "  xiaomi    - 小米"
    echo "  vivo      - Vivo"
    echo "  oppo      - Oppo"
    echo "  honor     - Honor"
    echo "  alibaba   - 阿里巴巴"
    echo ""
    echo "示例:"
    echo "  ./scripts/build.sh single kuan       # 只打包酷安渠道"
    echo "  ./scripts/build.sh single huawei     # 只打包华为渠道"
    echo "  ./scripts/build.sh preprocess        # 执行多语言合并和代码生成"
    echo "  ./scripts/build.sh check-env         # 检查本地Android构建环境"
    echo ""
}

# 检查脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 解析命令行参数
case "${1:-all}" in
    "help"|"-h"|"--help")
        show_help
        exit 0
        ;;
    "single")
        CHANNEL=${2:-"default"}
        log_header "单渠道构建模式"
        log_info "目标渠道: $CHANNEL"
        
        if [ ! -f "$SCRIPT_DIR/build_single_channel.sh" ]; then
            log_error "单渠道构建脚本不存在: $SCRIPT_DIR/build_single_channel.sh"
            exit 1
        fi
        
        exec "$SCRIPT_DIR/build_single_channel.sh" "$CHANNEL"
        ;;
    "all"|"")
        log_header "多渠道构建模式"
        log_info "将构建所有支持的渠道"
        
        if [ ! -f "$SCRIPT_DIR/build_channels.sh" ]; then
            log_error "多渠道构建脚本不存在: $SCRIPT_DIR/build_channels.sh"
            exit 1
        fi
        
        exec "$SCRIPT_DIR/build_channels.sh"
        ;;
    "preprocess")
        log_header "预处理模式"
        log_info "执行多语言合并和代码生成"

        if [ ! -f "$SCRIPT_DIR/preprocess.sh" ]; then
            log_error "预处理脚本不存在: $SCRIPT_DIR/preprocess.sh"
            exit 1
        fi

        exec "$SCRIPT_DIR/preprocess.sh"
        ;;
    "check-env")
        log_header "Android环境检查模式"
        log_info "检查本地Android构建环境"

        if [ ! -f "$SCRIPT_DIR/check_android_env.sh" ]; then
            log_error "环境检查脚本不存在: $SCRIPT_DIR/check_android_env.sh"
            exit 1
        fi

        exec "$SCRIPT_DIR/check_android_env.sh"
        ;;
    *)
        log_error "未知的命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
