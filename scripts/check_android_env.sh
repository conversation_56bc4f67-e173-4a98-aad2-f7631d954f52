#!/bin/bash

# Android 构建环境检查脚本
# 用于验证本地Android开发环境是否正确配置

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# 检查环境变量
check_env_vars() {
    log_header "检查环境变量"
    
    if [ -z "$ANDROID_HOME" ]; then
        log_error "ANDROID_HOME 环境变量未设置"
        return 1
    else
        log_success "ANDROID_HOME: $ANDROID_HOME"
    fi
    
    if [ -z "$JAVA_HOME" ]; then
        log_warning "JAVA_HOME 环境变量未设置"
    else
        log_success "JAVA_HOME: $JAVA_HOME"
    fi
}

# 检查Java版本
check_java() {
    log_header "检查Java环境"
    
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        log_success "Java版本: $JAVA_VERSION"
        
        # 检查是否为Java 21
        if [[ $JAVA_VERSION == 21.* ]]; then
            log_success "Java版本符合要求 (21.x)"
        else
            log_warning "推荐使用Java 21，当前版本: $JAVA_VERSION"
        fi
    else
        log_error "Java未安装或不在PATH中"
        return 1
    fi
}

# 检查Flutter环境
check_flutter() {
    log_header "检查Flutter环境"
    
    if command -v flutter &> /dev/null; then
        FLUTTER_VERSION=$(flutter --version | head -n 1 | cut -d' ' -f2)
        log_success "Flutter版本: $FLUTTER_VERSION"
        
        # 检查Flutter doctor
        log_info "运行 flutter doctor..."
        flutter doctor -v
    else
        log_error "Flutter未安装或不在PATH中"
        return 1
    fi
}

# 检查Android SDK组件
check_android_sdk() {
    log_header "检查Android SDK组件"
    
    if [ ! -d "$ANDROID_HOME" ]; then
        log_error "Android SDK目录不存在: $ANDROID_HOME"
        return 1
    fi
    
    # 检查cmdline-tools
    if [ -f "$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager" ]; then
        log_success "Android cmdline-tools 已安装"
        
        # 列出已安装的组件
        log_info "已安装的SDK组件:"
        $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --list_installed | grep -E "(platforms|ndk|build-tools)"
        
        # 检查特定组件
        check_component "platforms;android-33" "Android SDK Platform 33"
        check_component "ndk;27.0.12077973" "NDK 27.0.12077973"
        check_component "build-tools;33.0.2" "Build Tools 33.0.2"
        
    else
        log_error "Android cmdline-tools 未找到"
        log_info "请安装Android Studio或手动安装cmdline-tools"
        return 1
    fi
}

# 检查特定SDK组件
check_component() {
    local component=$1
    local name=$2
    
    if $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --list_installed | grep -q "$component"; then
        log_success "$name 已安装"
    else
        log_warning "$name 未安装"
        log_info "可以运行以下命令安装: sdkmanager \"$component\""
    fi
}

# 检查许可证
check_licenses() {
    log_header "检查Android SDK许可证"
    
    if [ -d "$ANDROID_HOME/licenses" ]; then
        log_success "许可证目录存在"
        
        if [ -f "$ANDROID_HOME/licenses/android-sdk-license" ]; then
            log_success "Android SDK许可证已接受"
        else
            log_warning "Android SDK许可证未接受"
            log_info "运行 'flutter doctor --android-licenses' 来接受许可证"
        fi
    else
        log_warning "许可证目录不存在"
        log_info "运行 'flutter doctor --android-licenses' 来接受许可证"
    fi
}

# 主函数
main() {
    log_header "Android 构建环境检查"
    
    local exit_code=0
    
    check_env_vars || exit_code=1
    echo ""
    
    check_java || exit_code=1
    echo ""
    
    check_flutter || exit_code=1
    echo ""
    
    check_android_sdk || exit_code=1
    echo ""
    
    check_licenses
    echo ""
    
    if [ $exit_code -eq 0 ]; then
        log_header "环境检查完成"
        log_success "Android构建环境配置正确！"
    else
        log_header "环境检查完成"
        log_error "发现环境配置问题，请根据上述提示进行修复"
    fi
    
    return $exit_code
}

# 执行主函数
main "$@"
