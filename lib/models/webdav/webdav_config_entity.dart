class WebdavConfigEntity {
  final String? serverUrl;
  final String? username;
  final String? password;
  final String? directory;
  final bool isEnabled;

  WebdavConfigEntity({
    this.serverUrl,
    this.username,
    this.password,
    this.directory = "recollect",
    this.isEnabled = false,
  });

  factory WebdavConfigEntity.fromJson(Map<String, dynamic> json) {
    return WebdavConfigEntity(
      serverUrl: json['serverUrl'],
      username: json['username'],
      password: json['password'],
      directory: json['directory'] ?? "recollect",
      isEnabled: json['isEnabled'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'serverUrl': serverUrl,
      'username': username,
      'password': password,
      'directory': directory,
      'isEnabled': isEnabled,
    };
  }
}
