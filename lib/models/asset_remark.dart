import 'package:json_annotation/json_annotation.dart';

part 'asset_remark.g.dart';

@JsonSerializable()
class AssetRemarkEntity {
  final String id;
  final String content;
  final DateTime createdAt;
  final DateTime? updatedAt;

  AssetRemarkEntity({
    required this.id,
    required this.content,
    required this.createdAt,
    this.updatedAt,
  });

  factory AssetRemarkEntity.fromJson(Map<String, dynamic> json) =>
      _$AssetRemarkEntityFromJson(json);

  Map<String, dynamic> toJson() => _$AssetRemarkEntityToJson(this);

  AssetRemarkEntity copyWith({
    String? id,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AssetRemarkEntity(
      id: id ?? this.id,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // 从旧的字符串格式创建备注实体
  factory AssetRemarkEntity.fromLegacyString(String content) {
    final now = DateTime.now();
    return AssetRemarkEntity(
      id: now.millisecondsSinceEpoch.toString(),
      content: content,
      createdAt: now,
    );
  }
}
