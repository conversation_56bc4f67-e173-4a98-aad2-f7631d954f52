import 'package:json_annotation/json_annotation.dart';

part 'storage_location.g.dart';

@JsonSerializable()
class StorageLocationEntity {
  final String id;
  final String name;
  int order;

  StorageLocationEntity({
    required this.id,
    required this.name,
    required this.order,
  });

  factory StorageLocationEntity.fromJson(Map<String, dynamic> json) =>
      _$StorageLocationEntityFromJson(json);

  Map<String, dynamic> toJson() => _$StorageLocationEntityToJson(this);
}
