import 'package:json_annotation/json_annotation.dart';

part 'activity_info.g.dart';

@JsonSerializable()
class ActivityResponse {
  final List<ActivityInfo>? lists;

  ActivityResponse({
    this.lists,
  });

  factory ActivityResponse.fromJson(Map<String, dynamic> json) =>
      _$ActivityResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ActivityResponseToJson(this);
}

@JsonSerializable()
class ActivityInfo {
  final String id;
  final String title;
  final String content;
  final String createTime;
  final String award;
  final int? awardNum;
  final String? submitTips;

  ActivityInfo({
    required this.id,
    required this.title,
    required this.content,
    required this.createTime,
    required this.award,
    this.submitTips,
    this.awardNum,
  });

  factory ActivityInfo.fromJson(Map<String, dynamic> json) =>
      _$ActivityInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ActivityInfoToJson(this);
}
