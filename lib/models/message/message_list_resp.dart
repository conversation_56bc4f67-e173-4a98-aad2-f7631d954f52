import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/models/message/message_info.dart';

part 'message_list_resp.g.dart';

@JsonSerializable()
class MessageListResp {
  List<MessageInfo?>? lists;

  MessageListResp({this.lists});

  factory MessageListResp.fromJson(Map<String, dynamic> json) =>
      _$MessageListRespFromJson(json);

  Map<String, dynamic> toJson() => _$MessageListRespToJson(this);
}
