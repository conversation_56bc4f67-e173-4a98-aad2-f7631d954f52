

/// id : "1"
/// title : "测试消息数据"
/// content : "这里是一条测试数据"
/// createTime : "2025-02-03T15:06:54.710139Z"
/// imageUrl : null
/// actionType : "INNER"
/// action : "https://www.baidu.com"
///

class MessageInfo {
  MessageInfo({
    String? id,
    String? title,
    String? content,
    String? createTime,
    String? imageUrl,
    String? actionType,
    String? action,
  }) {
    _id = id;
    _title = title;
    _content = content;
    _createTime = createTime;
    _imageUrl = imageUrl;
    _actionType = actionType;
    _action = action;
  }

  MessageInfo.fromJson(dynamic json) {
    _id = json['id'];
    _title = json['title'];
    _content = json['content'];
    _createTime = json['createTime'];
    _imageUrl = json['imageUrl'];
    _actionType = json['actionType'];
    _action = json['action'];
  }

  String? _id;
  String? _title;
  String? _content;
  String? _createTime;
  String? _imageUrl;
  String? _actionType;
  String? _action;

  MessageInfo copyWith({
    String? id,
    String? title,
    String? content,
    String? createTime,
    String? imageUrl,
    String? actionType,
    String? action,
  }) =>
      MessageInfo(
        id: id ?? _id,
        title: title ?? _title,
        content: content ?? _content,
        createTime: createTime ?? _createTime,
        imageUrl: imageUrl ?? _imageUrl,
        actionType: actionType ?? _actionType,
        action: action ?? _action,
      );

  String? get id => _id;

  String? get title => _title;

  String? get content => _content;

  String? get createTime => _createTime;

  String? get imageUrl => _imageUrl;

  String? get actionType => _actionType;

  String? get action => _action;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['title'] = _title;
    map['content'] = _content;
    map['createTime'] = _createTime;
    map['imageUrl'] = _imageUrl;
    map['actionType'] = _actionType;
    map['action'] = _action;
    return map;
  }
}


