class ConfigEntity {
  final int maxAssetNumUnLogin;

  // 登录时最大资产数量
  final int maxAssetNumLogin;

  final String shareUrl;

  // 配置捐款链接
  final String donateUrl;
  final String feedbackEmail;
  final String defaultIcon;
  final String moneyFormat;
  final String payMeUrl;
  final String privacyPolicy;
  final String servicePolicy;
  final String redBookLink;
  final String devWechat;
  final String discountAmount;
  final String originAmount;
  final bool showMembershipActivity;

  ConfigEntity({
    required this.maxAssetNumUnLogin,
    required this.maxAssetNumLogin,
    required this.donateUrl,
    required this.shareUrl,
    required this.feedbackEmail,
    required this.defaultIcon,
    required this.moneyFormat,
    required this.payMeUrl,
    required this.privacyPolicy,
    required this.servicePolicy,
    required this.redBookLink,
    required this.devWechat,
    required this.discountAmount,
    required this.originAmount,
    required this.showMembershipActivity,
  });
}
