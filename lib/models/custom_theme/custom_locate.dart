import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/utils/string_ext.dart';

part 'custom_locate.g.dart';

@JsonSerializable()
class CustomLocate {
  final String? countryCode;
  final String? languageCode;
  final String? moneySymbol;

  CustomLocate({
    this.countryCode,
    this.languageCode,
    this.moneySymbol,
  });

  static CustomLocate instance() {
    return CustomLocate(
      countryCode: 'CN',
      languageCode: 'zh',
      moneySymbol: '¥',
    );
  }

  // 判断是否是有效的自定义语言
  bool valid() {
    return !countryCode.isNullOrEmpty && !languageCode.isNullOrEmpty;
  }

  factory CustomLocate.fromJson(Map<String, dynamic> json) =>
      _$CustomLocateFromJson(json);

  Map<String, dynamic> toJson() => _$CustomLocateToJson(this);

  // 复制当前对象
  CustomLocate copyWith({
    String? countryCode,
    String? languageCode,
    String? moneySymbol,
  }) {
    return CustomLocate(
      countryCode: countryCode ?? this.countryCode,
      languageCode: languageCode ?? this.languageCode,
      moneySymbol: moneySymbol ?? this.moneySymbol,
    );
  }
}
