import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/generated/l10n.dart';

import 'custom_card_theme.dart';

part 'custom_theme.g.dart'; // 添加这一行以支持 json_serializable

@JsonSerializable()
class CustomTheme {
  /// 主题模式
  final ThemeMode themeMode;

  /// 种子色
  @JsonKey(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color seedColor;

  /// 是否启用动态色
  final bool enableDynamicColor;

  final CustomCardTheme topCard;
  final CustomCardTheme normalCard;
  final CustomCardTheme favoriteCard;
  final CustomCardTheme retiredCard;

  CustomTheme({
    // 初始化渐变颜色属性
    this.themeMode = ThemeMode.system,
    this.enableDynamicColor = false,
    required this.seedColor,
    required this.topCard,
    required this.normalCard,
    required this.favoriteCard,
    required this.retiredCard,
  });

  static CustomTheme defaultCustomTheme() {
    return CustomTheme(
        topCard: CustomCardTheme(
          startColor: Color(0xFFFFFFFF),
          endColor: Color(0xFFFFFFFF),
        ),
        normalCard: CustomCardTheme(
          startColor: Color(0xFFFFFFFF),
          endColor: Color(0xFFFFFFFF),
        ),
        favoriteCard: CustomCardTheme(
          startColor: Color(0xFFFFFFFF),
          endColor: Color(0xFFFFFFFF),
        ),
        retiredCard: CustomCardTheme(
          startColor: Color(0xFFFFFFFF),
          endColor: Color(0xFFFFFFFF),
        ),
        seedColor: Colors.red);
  }

  static CustomTheme defaultBlackTheme() {
    return CustomTheme(
        topCard: CustomCardTheme(
          startColor: Colors.black,
          endColor: Colors.black,
          onContainerDarkColor: Colors.white,
          onContainerLightColor: Colors.white,
        ),
        normalCard: CustomCardTheme(
          startColor: Colors.black,
          endColor: Colors.black,
          onContainerDarkColor: Colors.white,
          onContainerLightColor: Colors.white,
        ),
        favoriteCard: CustomCardTheme(
          startColor: Colors.black,
          endColor: Colors.black,
          onContainerDarkColor: Colors.white,
          onContainerLightColor: Colors.white,
        ),
        retiredCard: CustomCardTheme(
          startColor: Colors.black,
          endColor: Colors.black,
          onContainerDarkColor: Colors.white,
          onContainerLightColor: Colors.white,
        ),
        seedColor: Colors.red);
  }

  static Color classicNormalColor() => Color(0xFF70BC73);

  static Color classicFavoriteColor() => Color(0xFFC79B45);

  static Color classicRetiredColor() => Color(0xFF8E9E99);

  static CustomTheme classicCustomTheme() {
    return CustomTheme(
      topCard: CustomCardTheme(
        startColor: Color(0xFF2c64f4),
        endColor: Color(0xff3c81e3),
        onContainerDarkColor: Color(0xFFFFFFFF),
        onContainerLightColor: Color(0xFFFFFFFF),
      ),
      normalCard: CustomCardTheme(
        startColor: Color(0xFF70BC73),
        endColor: Color(0xFF5CC861),
        onContainerDarkColor: Color(0xFFFFFFFF),
        onContainerLightColor: Color(0xFFFFFFFF),
      ),
      favoriteCard: CustomCardTheme(
        startColor: Color(0xFFC79B45),
        endColor: Color(0xFFD5B05B),
        onContainerDarkColor: Color(0xFFFFFFFF),
        onContainerLightColor: Color(0xFFFFFFFF),
      ),
      retiredCard: CustomCardTheme(
        startColor: Color(0xFF8E9E99),
        endColor: Color(0xFFA5B4A8),
        onContainerDarkColor: Color(0xFFFFFFFF),
        onContainerLightColor: Color(0xFFFFFFFF),
      ),
      seedColor: Colors.red,
    );
  }

  CustomTheme copyWith({
    ThemeMode? themeMode,
    Color? seedColor,
    bool? enableDynamicColor,
    CustomCardTheme? topCard,
    CustomCardTheme? normalCard,
    CustomCardTheme? favoriteCard,
    CustomCardTheme? retiredCard,
  }) {
    return CustomTheme(
      themeMode: themeMode ?? this.themeMode,
      seedColor: seedColor ?? this.seedColor,
      enableDynamicColor: enableDynamicColor ?? this.enableDynamicColor,
      topCard: topCard ?? this.topCard,
      normalCard: normalCard ?? this.normalCard,
      favoriteCard: favoriteCard ?? this.favoriteCard,
      retiredCard: retiredCard ?? this.retiredCard,
    );
  }

  /// 主题模式文字
  String get themeModeText {
    switch (themeMode) {
      case ThemeMode.system:
        return S.current.settingSystemAppearance;
      case ThemeMode.light:
        return S.current.settingAppearanceLight;
      case ThemeMode.dark:
        return S.current.settingAppearanceDark;
    }
  }

  factory CustomTheme.fromJson(Map<String, dynamic> json) =>
      _$CustomThemeFromJson(json);

  Map<String, dynamic> toJson() => _$CustomThemeToJson(this);

  // 自定义 Color 的序列化方法
  static Color _colorFromJson(int value) => Color(value);

  // 自定义 Color 的反序列化方法
  static int _colorToJson(Color color) => color.value;
}
