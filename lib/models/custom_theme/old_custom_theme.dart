import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'old_custom_theme.g.dart';

@JsonSerializable()
class OldCustomTheme {
  // 渐变颜色属性
  @<PERSON><PERSON><PERSON><PERSON>(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color normalCardGradientStart;
  @Json<PERSON>ey(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color normalCardGradientEnd;
  @<PERSON><PERSON><PERSON><PERSON>(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color favoriteCardGradientStart;
  @Json<PERSON>ey(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color favoriteCardGradientEnd;
  @J<PERSON><PERSON><PERSON>(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color retiredCardGradientStart;
  @Json<PERSON>ey(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color retiredCardGradientEnd;
  @Json<PERSON><PERSON>(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color homeTopInfoGradientStart;
  @JsonKey(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color homeTopInfoGradientEnd;

  OldCustomTheme({
    required this.normalCardGradientStart,
    required this.normalCardGradientEnd,
    required this.favoriteCardGradientStart,
    required this.favoriteCardGradientEnd,
    required this.retiredCardGradientStart,
    required this.retiredCardGradientEnd,
    required this.homeTopInfoGradientStart,
    required this.homeTopInfoGradientEnd,
  });

  factory OldCustomTheme.fromJson(Map<String, dynamic> json) =>
      _$OldCustomThemeFromJson(json);

  Map<String, dynamic> toJson() => _$OldCustomThemeToJson(this);

  // 自定义 Color 的序列化方法
  static Color _colorFromJson(int value) => Color(value);

  // 自定义 Color 的反序列化方法
  static int _colorToJson(Color color) => color.value;
}
