import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'custom_card_theme.g.dart';

@JsonSerializable()
class CustomCardTheme {
  // 渐变颜色属性
  @J<PERSON><PERSON><PERSON>(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color startColor;
  @J<PERSON><PERSON>ey(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color endColor;
  @JsonKey(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color onContainerLightColor;
  @JsonKey(fromJson: _colorFromJson, toJson: _colorToJson)
  final Color onContainerDarkColor;

  Color onContainerColor(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    if (brightness == Brightness.light) {
      return onContainerLightColor;
    } else {
      return onContainerDarkColor;
    }
  }

  CustomCardTheme({
    required this.startColor,
    required this.endColor,
    this.onContainerLightColor = Colors.black,
    this.onContainerDarkColor = Colors.black,
  });

  factory CustomCardTheme.fromJson(Map<String, dynamic> json) =>
      _$CustomCardThemeFromJson(json);

  Map<String, dynamic> toJson() => _$CustomCardThemeToJson(this);

  CustomCardTheme copyWith({
    Color? startColor,
    Color? endColor,
    Color? onContainerLightColor,
    Color? onContainerDarkColor,
  }){
    return CustomCardTheme(
      startColor: startColor ?? this.startColor,
      endColor: endColor ?? this.endColor,
      onContainerLightColor: onContainerLightColor ?? this.onContainerLightColor,
      onContainerDarkColor: onContainerDarkColor ?? this.onContainerDarkColor,
    );
  }

  // 自定义 Color 的序列化方法
  static Color _colorFromJson(int value) => Color(value);

  // 自定义 Color 的反序列化方法
  static int _colorToJson(Color color) => color.value;
}
