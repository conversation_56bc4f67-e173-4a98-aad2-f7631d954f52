import 'package:json_annotation/json_annotation.dart';

part 'cache_history_resp.g.dart';

@JsonSerializable()
class CacheHistoryResp {
  List<CacheHistoryEntity>? list;

  CacheHistoryResp({this.list});

  factory CacheHistoryResp.fromJson(Map<String, dynamic> json) =>
      _$CacheHistoryRespFromJson(json);

  Map<String, dynamic> toJson() => _$CacheHistoryRespToJson(this);
}

@JsonSerializable()
class CacheHistoryEntity {
  String? id;
  String? createdAt;
  String? type;
  String? fileId;

  CacheHistoryEntity({this.id, this.createdAt, this.type, this.fileId});

  factory CacheHistoryEntity.fromJson(Map<String, dynamic> json) =>
      _$CacheHistoryEntityFromJson(json);

  Map<String, dynamic> toJson() => _$CacheHistoryEntityToJson(this);
}
