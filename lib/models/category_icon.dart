import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/data/config/config_repo.dart';

part 'category_icon.g.dart';

enum IconType {
  system,
  remote,
  local;

  /// 自定义反序列化逻辑
  static IconType fromJson(dynamic json) {
    if (json is String) {
      return IconType.values.firstWhere(
        (e) => e.toString().split('.').last == json,
        orElse: () => IconType.system, // 默认值
      );
    }
    // 如果 json 不是字符串，返回默认值
    return IconType.system;
  }

  /// 自定义序列化逻辑
  static String toJson(IconType type) => type.toString().split('.').last;
}

@JsonSerializable()
class AppIcon {
  final String? value;
  final String? key;
  @JsonKey(fromJson: IconType.fromJson, toJson: IconType.toJson)
  final IconType type;

  AppIcon({
    this.value,
    this.key,
    required this.type,
  });

  factory AppIcon.defaultIcon() {
    return AppIcon(
        type: IconType.remote, value: ConfigRepo.getAllConfig().defaultIcon);
  }

  // 使用工厂方法创建本地图标实例
  factory AppIcon.local(String path) {
    return AppIcon(
      value: path,
      type: IconType.local,
    );
  }

  factory AppIcon.remote(String path) {
    return AppIcon(
      value: path,
      type: IconType.remote,
    );
  }

  factory AppIcon.fromJson(Map<String, dynamic> json) =>
      _$AppIconFromJson(json);

  Map<String, dynamic> toJson() => _$AppIconToJson(this);
}
