import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/buy_channel.dart';
import 'package:tasks/models/storage_location.dart';

part 'sync_data.g.dart';

@JsonSerializable()
class SyncData {
  final List<Asset> assets;
  final List<CategoryEntity> categories;
  final List<BuyChannelEntity>? buyChannels;
  final List<StorageLocationEntity>? storageLocations;

  SyncData({
    required this.assets,
    required this.categories,
    this.buyChannels,
    this.storageLocations,
  });

  factory SyncData.fromJson(Map<String, dynamic> json) =>
      _$SyncDataFromJson(json);

  Map<String, dynamic> toJson() => _$SyncDataToJson(this);
}
