import 'package:json_annotation/json_annotation.dart';

part 'usage_price_value.g.dart';

@JsonSerializable()
class UsagePriceValue {
  final int? count;
  final int? allCount;

  UsagePriceValue({
    this.count,
    this.allCount,
  });

  factory UsagePriceValue.fromJson(Map<String, dynamic> json) =>
      _$UsagePriceValueFromJson(json);

  Map<String, dynamic> toJson() => _$UsagePriceValueToJson(this);

  UsagePriceValue copyWith({
    int? count,
    int? allCount,
  }) {
    return UsagePriceValue(
      count: count ?? this.count,
      allCount: allCount,
    );
  }

  String displayCount() {
    if (allCount == null) {
      return "${count ?? 0}";
    }
    return "${count ?? 0}/${allCount ?? 0}";
  }
}
