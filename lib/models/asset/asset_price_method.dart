import 'package:tasks/generated/l10n.dart';

enum PriceMethod {
  // 不计价,
  noPrice,
  // 默认为日均
  defaultPrice,
  // 使用次数
  useCount,
  // 循环计价
  cyclePrice,
}

extension PriceMethodExtension on PriceMethod {
  String get description {
    switch (this) {
      case PriceMethod.noPrice:
        return S.current.assetPriceMethodNoPrice;
      case PriceMethod.defaultPrice:
        return S.current.assetPriceMethodDefault;
      case PriceMethod.useCount:
        return S.current.assetPriceMethodUseCount;
      case PriceMethod.cyclePrice:
        return S.current.assetPriceMethodCycle;
    }
  }
}

/// 用于显示选择项
class PriceMethodEntity {
  final PriceMethod method;
  final String label;

  PriceMethodEntity(this.method, this.label);

  static List<PriceMethodEntity> get values => [
        PriceMethodEntity(
            PriceMethod.noPrice, S.current.assetPriceMethodNoPrice),
        PriceMethodEntity(
            PriceMethod.defaultPrice, S.current.assetPriceMethodDefault),
        PriceMethodEntity(
            PriceMethod.useCount, S.current.assetPriceMethodUseCount),
        PriceMethodEntity(
            PriceMethod.cyclePrice, S.current.assetPriceMethodCycle),
      ];
}
