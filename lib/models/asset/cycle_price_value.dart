import 'package:in_date_utils/in_date_utils.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/utils/date_utils.dart';

part 'cycle_price_value.g.dart';

enum CyclePriceType {
  day,
  week,
  month,
  year,
}

@JsonSerializable()
class CyclePriceValue {
  final int count;
  final CyclePriceType type;

  CyclePriceValue({
    required this.count,
    required this.type,
  });

  String get label {
    switch (type) {
      case CyclePriceType.day:
        return '${count}天';
      case CyclePriceType.week:
        return '${count}周';
      case CyclePriceType.month:
        return '${count}月';
      case CyclePriceType.year:
        return '${count}年';
    }
  }

  CyclePriceValue copyWith({
    int? count,
    CyclePriceType? type,
  }) {
    return CyclePriceValue(
      count: count ?? this.count,
      type: type ?? this.type,
    );
  }

  factory CyclePriceValue.fromJson(Map<String, dynamic> json) =>
      _$CyclePriceValueFromJson(json);

  Map<String, dynamic> toJson() => _$CyclePriceValueToJson(this);

  /// 计算循环计价次数
  static int measureCyclePriceCount(
      CyclePriceValue cyclePriceValue, DateTime startDate, DateTime endDate,
      {bool inclusive = false, bool absolute = true}) {
    final type = cyclePriceValue.type;
    final count = cyclePriceValue.count;
    if (count == 0) {
      return 0;
    }

    switch (type) {
      case CyclePriceType.day:
        final int days = AppDateUtils.daysLength(startDate, endDate,
            inclusive: inclusive, absolute: absolute);
        return (days / count).ceil(); // 按天计算，向上取整
      case CyclePriceType.week:
        final int days = AppDateUtils.daysLength(startDate, endDate,
            inclusive: inclusive, absolute: absolute);
        return (days / 7 / count).ceil(); // 按周计算，向上取整
      case CyclePriceType.month:
        final int months =
            DateTimeUtils.getMonthsDifference(startDate, endDate);
        final bool isInMonth = startDate.day >= endDate.day;
        return (months / count).ceil() + (isInMonth ? 0 : 1);
      case CyclePriceType.year:
        final int months =
            DateTimeUtils.getMonthsDifference(startDate, endDate);
        final bool isInMonth = startDate.day >= endDate.day;
        return ((months + (isInMonth ? 0 : 1)) / 12 / count).ceil();
    }
  }

  /// 计算下次付费时间（无循环）
  static DateTime nextPaymentDate(
      CyclePriceValue cyclePriceValue, DateTime start, int curCycle) {
    final type = cyclePriceValue.type;
    final count = cyclePriceValue.count;
    switch (type) {
      case CyclePriceType.day:
        return DateTimeUtils.addDays(start, curCycle * count - 1);

      case CyclePriceType.week:
        return start.add(Duration(days: curCycle * count * 7));

      case CyclePriceType.month:
        return DateTimeUtils.addMonths(start, curCycle * count);

      case CyclePriceType.year:
        return DateTimeUtils.addYears(start, curCycle * count);
    }
  }
}

/// 用于显示选择项
class CyclePriceTypeEntity {
  final CyclePriceType value;
  final String label;

  CyclePriceTypeEntity(this.value, this.label);

  static List<CyclePriceTypeEntity> get values => [
        CyclePriceTypeEntity(
            CyclePriceType.day, S.current.assetCycleValueTypeDay),
        CyclePriceTypeEntity(
            CyclePriceType.week, S.current.assetCycleValueTypeWeek),
        CyclePriceTypeEntity(
            CyclePriceType.month, S.current.assetCycleValueTypeMonth),
        CyclePriceTypeEntity(
            CyclePriceType.year, S.current.assetCycleValueTypeYear),
      ];
}
