import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/category_icon.dart';

/// Asset类的扩展方法
extension AssetExtensions on Asset {
  /// 获取当前资产的所有图片文件路径
  /// 返回本地存在的图片文件路径列表
  Future<List<String>> getImagePaths() async {
    final List<String> imagePaths = [];

    // 检查资产的icon是否为本地图片
    if (icon != null && icon!.type == IconType.local && icon!.value != null) {
      final imagePath = icon!.value!;
      imagePaths.add(imagePath);
    }

    return imagePaths;
  }

  /// 获取当前资产的所有图片文件信息
  /// 返回包含文件路径和文件名的Map列表
  Future<List<Map<String, String>>> getImageFiles() async {
    final List<Map<String, String>> imageFiles = [];
    final imagePaths = await getImagePaths();

    for (final path in imagePaths) {
      final filename = path.split('/').last;
      imageFiles.add({
        'path': path,
        'filename': filename,
      });
    }

    return imageFiles;
  }

  /// 检查资产是否有本地图片
  bool hasLocalImages() {
    return icon != null &&
        icon!.type == IconType.local &&
        icon!.value != null &&
        icon!.value!.isNotEmpty;
  }
}
