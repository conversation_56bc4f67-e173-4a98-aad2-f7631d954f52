enum AssetSortType {
  defaultSort,
  defaultSortRevert,
  typeSort,
  typeSortRevert,

  /// 时间降序
  createTimeDescending,

  /// 时间升序
  createTime,

  /// 价格升序
  price,

  /// 价格降序
  priceDescending,

  /// 每日价格降序
  dailyPriceDescending,

  /// 每日价格升序
  dailyPrice,

  /// 使用天数降序
  keepDaysDescending,

  /// 使用天数升序
  keepDays,
}

extension AssetSortTypeExtension on AssetSortType {
  String get description {
    switch (this) {
      case AssetSortType.defaultSort:
        return "默认排序";
      case AssetSortType.defaultSortRevert:
        return "默认排序反序";
      case AssetSortType.typeSort:
        return "类型排序";
      case AssetSortType.typeSortRevert:
        return "类型排序反序";
      case AssetSortType.createTime:
        return "购买时间";
      case AssetSortType.createTimeDescending:
        return "购买时间反序";
      case AssetSortType.price:
        return "价格从低到高";
      case AssetSortType.priceDescending:
        return "价格从高到低";
      case AssetSortType.dailyPrice:
        return "日均从低到高";
      case AssetSortType.dailyPriceDescending:
        return "日均从高到低";
      case AssetSortType.keepDays:
        return "使用天数从少到多";
      case AssetSortType.keepDaysDescending:
        return "使用天数从多到少";
      default:
        return "默认排序";
    }
  }
}
