import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/asset/asset_price_method.dart';
import 'package:tasks/models/asset/asset_sort.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/utils/date_utils.dart';

class AssetCardEntity {
  Asset asset;

  String id;
  AppIcon? icon;
  String name;
  String? categoryName;
  bool isFavorite;
  bool isInService;

  final double price;
  final double? resalePrice;

  AssetCardEntity({
    required this.asset,
    required this.id,
    this.icon,
    required this.name,
    this.categoryName,
    required this.price,
    this.resalePrice,
    required this.isFavorite,
    required this.isInService,
  });

  // 通过资产信息计算实际价格
  double get measureAmount {
    return asset.measureAmount;
  }

  // 判断是否展示残值
  bool get showRemain {
    return asset.remainAmount > 0;
  }

  // 资产残值
  double get remainAmount {
    return asset.remainAmount;
  }

  get dailyPrice {
    return asset.measureDailyPrice;
  }

  get showCategoryName {
    return categoryName != null && categoryName!.isNotEmpty;
  }

  get title {
    return name;
  }

  // 是否在保
  get showWarrantyTag {
    return asset.warrantyDate != null;
  }

  // 距离保修到期天数
  get warrantyCountdown {
    return AppDateUtils.daysLength(DateTime.now(), asset.warrantyDate!,
        inclusive: true, absolute: false);
  }

  get inWarranty {
    if (asset.warrantyDate == null) return false;
    if (warrantyCountdown > 0) {
      return true;
    }
    return false;
  }

  get timeFormat {
    return DateFormatUtils.formatDateYMD(asset.purchaseDate);
  }

  /// 展示是否以日计费
  get showDailyPrice {
    if (asset.priceMethod == PriceMethod.defaultPrice ||
        asset.priceMethod == null) {
      return true;
    }
    return false;
  }

  /// 不计价
  get showNoPrice {
    return asset.priceMethod == PriceMethod.noPrice;
  }

  /// 展示是否订阅计费
  get showCyclePrice {
    return asset.priceMethod == PriceMethod.cyclePrice;
  }

  /// 展示是否使用次数计费
  get showUsageCountPrice {
    return asset.priceMethod == PriceMethod.useCount;
  }

  get showUsageCount {
    return asset.priceMethod == PriceMethod.useCount &&
        asset.usagePriceValue?.count != null;
  }

  get holdingDays {
    return asset.holdingDays;
  }

  get retireDays {
    return asset.retireDays;
  }

  get validRetireDay {
    if (retireDays > 0) {
      return true;
    }
    return false;
  }

  // 处理排序
  int compare(AssetSortType sortType, AssetCardEntity other) {
    switch (sortType) {
      case AssetSortType.defaultSort:
        return 1;
      case AssetSortType.defaultSortRevert:
        return -1;
      case AssetSortType.typeSort:
        return typeValue().compareTo(other.typeValue());
      case AssetSortType.typeSortRevert:
        return other.typeValue().compareTo(typeValue());
      case AssetSortType.createTime:
        return asset.purchaseDate.compareTo(other.asset.purchaseDate);
      case AssetSortType.createTimeDescending:
        return other.asset.purchaseDate.compareTo(asset.purchaseDate);
      case AssetSortType.price:
        return price.compareTo(other.price);
      case AssetSortType.priceDescending:
        return other.price.compareTo(price);
      case AssetSortType.dailyPrice:
        return dailyPrice.compareTo(other.dailyPrice);
      case AssetSortType.dailyPriceDescending:
        return other.dailyPrice.compareTo(dailyPrice);
      case AssetSortType.keepDays:
        return holdingDays.compareTo(other.holdingDays);
      case AssetSortType.keepDaysDescending:
        return other.holdingDays.compareTo(holdingDays);
    }
  }

  int typeValue() {
    // 在役且收藏
    if (asset.isInService && asset.isFavorite) {
      return 1;
    }
    // 在役且非收藏
    if (asset.isInService && !asset.isFavorite) {
      return 2;
    }
    // 非在役且收藏
    if (!asset.isInService && asset.isFavorite) {
      return 3;
    }
    // 非在役且非收藏
    return 4;
  }
}
