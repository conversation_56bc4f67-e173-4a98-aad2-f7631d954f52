import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/utils/string_ext.dart';

part 'extra_fees.g.dart';

@JsonSerializable()
class ExtraFeesEntity {
  final String id;
  final bool paid;
  final String name;
  final String value;
  final DateTime createAt;
  final String note;
  final bool includeInTotal;

  ExtraFeesEntity({
    this.paid = true,
    this.name = "",
    this.value = "",
    this.note = "",
    this.includeInTotal = true,
    DateTime? createAt,
    String? id,
  })  : id = id ?? DateTime.now().toString(),
        createAt = createAt ?? DateTime.now();

  ExtraFeesEntity copyWith(
      {bool? paid,
      String? value,
      String? note,
      String? name,
      bool? includeInTotal,
      DateTime? createAt}) {
    return ExtraFeesEntity(
      paid: paid ?? this.paid,
      name: name ?? this.name,
      value: value ?? this.value,
      note: note ?? this.note,
      includeInTotal: includeInTotal ?? this.includeInTotal,
      id: id,
      createAt: createAt ?? this.createAt,
    );
  }

  bool get valid => name.isNotEmpty;

  // 当前费用金额值
  double get amount {
    final amount = value.toDoubleOrNull ?? 0;
    if (paid) {
      return amount;
    }
    return -amount;
  }

  // 计入总价值的金额
  double get totalAmount {
    if (!includeInTotal) {
      return 0.0;
    }
    return amount;
  }

  factory ExtraFeesEntity.fromJson(Map<String, dynamic> json) =>
      _$ExtraFeesEntityFromJson(json);

  Map<String, dynamic> toJson() => _$ExtraFeesEntityToJson(this);

  /// 判断附加费用数据是否相等
  bool isValueEqual(ExtraFeesEntity entity) {
    if (entity.name != name) {
      return false;
    }
    if (entity.value != value) {
      return false;
    }
    if (entity.note != note) {
      return false;
    }
    if (entity.paid != paid) {
      return false;
    }
    if (entity.includeInTotal != includeInTotal) {
      return false;
    }
    return true;
  }
}
