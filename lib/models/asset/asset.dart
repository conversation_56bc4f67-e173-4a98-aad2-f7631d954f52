import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/models/asset/asset_price_method.dart';
import 'package:tasks/models/asset/cycle_price_value.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/models/asset/usage_price_value.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/utils/date_utils.dart';
import 'package:uuid/uuid.dart';
import 'package:tasks/models/buy_channel.dart';
import 'package:tasks/models/storage_location.dart';
import 'package:tasks/models/asset_remark.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/data/buy_channel/buy_channel_local_data_source.dart';
import 'package:tasks/data/storage_location/storage_location_local_data_source.dart';

part 'asset.g.dart';

@JsonSerializable()
class Asset {
  final String id;
  final String name;
  final String? categoryId;
  final double price;

  // 购买时间
  final DateTime purchaseDate;
  DateTime? retireDate;

  // 保修截止日期
  DateTime? warrantyDate;

  // 计价方式
  final PriceMethod? priceMethod;
  double? dailyPrice;
  int? usageCount;

  // 使用次数计价
  UsagePriceValue? usagePriceValue;

  // 循环计价
  CyclePriceValue? cyclePriceValue;

  bool isInService;
  final double? resalePrice;

  final DateTime createdAt;
  final bool isPinned;
  final bool isFavorite;
  final AppIcon? icon;

  final List<ExtraFeesEntity> extraFees;

  final String? buyChannel;
  final String? storageLocation;
  final String? remark; // 保留用于向后兼容
  final List<String>? remarkHistory; // 保留用于向后兼容
  final AssetRemarkEntity? remarkEntity;
  final List<AssetRemarkEntity>? remarkHistoryEntities;

  // 构造函数
  Asset({
    required this.name,
    this.categoryId,
    required this.price,
    required this.purchaseDate,
    this.priceMethod = PriceMethod.defaultPrice,
    this.dailyPrice,
    this.cyclePriceValue,
    this.warrantyDate,
    this.retireDate,
    this.resalePrice,
    this.isPinned = false,
    this.isFavorite = false,
    int? usageCount,
    UsagePriceValue? usagePriceValue,
    bool? isInService,
    String? id,
    AppIcon? icon,
    DateTime? createdAt,
    this.extraFees = const [],
    this.buyChannel,
    this.storageLocation,
    this.remark,
    this.remarkHistory,
    this.remarkEntity,
    this.remarkHistoryEntities,
  })  : createdAt = createdAt ?? DateTime.now(),
        icon = icon ?? AppIcon.defaultIcon(),
        id = id ?? Uuid().v4(),
        isInService = isInService ?? true,
        usagePriceValue = usageCount != null
            ? UsagePriceValue(count: usageCount)
            : usagePriceValue {}

  /// 是否以日均计价
  bool get dailyPriceMethod {
    if (priceMethod == PriceMethod.defaultPrice || priceMethod == null) {
      return true;
    }
    return false;
  }

  // 当前资产的额外费用
  double get extraFeesAmount {
    return extraFees.fold(0.0, (sum, item) {
      return sum + item.amount;
    });
  }

  // 计入总价值的额外费用
  double get extraFeesTotalAmount {
    return extraFees.fold(0.0, (sum, item) {
      return sum + item.totalAmount;
    });
  }

  // 订阅类型的循环次数
  int get measureCycleCount {
    if (priceMethod != PriceMethod.cyclePrice || cyclePriceValue == null) {
      return 0;
    }
    final endDate =
        isInService ? DateTime.now() : (retireDate ?? DateTime.now());
    final count = CyclePriceValue.measureCyclePriceCount(
        cyclePriceValue!, purchaseDate, endDate,
        inclusive: true);
    return count == 0 ? 1 : count;
  }

  // 订阅类型的下次付费天数
  int get measureCycleNextPayDays {
    if (priceMethod != PriceMethod.cyclePrice || cyclePriceValue == null) {
      return 0;
    }
    if (!isInService) {
      return 0;
    }
    final start = purchaseDate;
    final end = DateTime.now();
    final nextPaymentDate = CyclePriceValue.nextPaymentDate(
        cyclePriceValue!, start, measureCycleCount);
    return AppDateUtils.daysLength(end, nextPaymentDate, inclusive: false);
  }

  // 通过资产信息计算实际价格
  double get measureAmount {
    // 如果类型为循环计价，需要考虑循环价格
    if (priceMethod == PriceMethod.cyclePrice) {
      return (price) * measureCycleCount + extraFeesTotalAmount;
    }
    // 计算额外费用
    final curAmount = extraFeesTotalAmount + price;
    // 如果资产已退役且有二手价格，使用二手价格减去原价
    if (!isInService && resalePrice != null) {
      return (curAmount - (resalePrice ?? 0.0));
    }
    return curAmount;
  }

  // 资产残值
  double get remainAmount {
    if (!isInService) {
      return resalePrice ?? 0;
    }
    // 日均计价时，残值为剩余比例
    if (priceMethod == PriceMethod.defaultPrice || priceMethod == null) {
      final rate = retireDays / (holdingDays + retireDays);
      return (price * rate);
    }
    // 按次计价时，残值为剩余次数（暂不处理)
    // 循环计价时，残值为剩余循环次数(暂不处理)
    if (priceMethod == PriceMethod.cyclePrice) {}
    return 0;
  }

  // 计算属性：日均价
  double get measureDailyPrice {
    // 如果服务中，且指定了均价，走均价
    if (isInService && dailyPriceMethod && dailyPrice != null) {
      return dailyPrice ?? 0;
    }
    // 服务中，使用价格除以持有天数
    if (isInService) {
      return measureAmount / holdingDays;
    }

    if (measureAmount == 0) {
      return 0;
    }
    return measureAmount / holdingDays;
  }

  // 计算属性：使用次数价格
  double? get measurePriceOfUsage {
    if (priceMethod == PriceMethod.useCount) {
      if (usagePriceValue?.count == null) return null;
      return price / (usagePriceValue?.count ?? 1);
    }
    return null;
  }

  // 用户资产的持有天数
  int get holdingDays {
    if (isInService) {
      final days = AppDateUtils.daysLength(purchaseDate, DateTime.now(),
          inclusive: true);
      return days;
    }
    final days = AppDateUtils.daysLength(
        purchaseDate, retireDate ?? DateTime.now(),
        inclusive: true);
    return days;
  }

  // 资产预计退役时间
  int get retireDays {
    if (isInService && dailyPriceMethod && dailyPrice != null) {
      // 确保 price 和 dailyPrice 都存在且有效
      if ((measureDailyPrice) > 0 && price > 0) {
        // 计算预计退役时间，并向上取整
        int totalDays = (price / measureDailyPrice).ceil();
        final daysUntilRetire =
            totalDays - (AppDateUtils.daysLength(purchaseDate, DateTime.now()));
        if (daysUntilRetire < 0) {
          return 0;
        }
        return daysUntilRetire;
      }
    }
    return 0;
  }

  Asset copyWith({
    String? id,
    String? name,
    String? categoryId,
    AppIcon? icon,
    double? price,
    DateTime? purchaseDate,
    DateTime? warrantyDate,
    PriceMethod? priceMethod,
    double? dailyPrice,
    CyclePriceValue? cyclePriceValue,
    UsagePriceValue? usagePriceValue,
    bool? isInService,
    DateTime? retireDate,
    double? resalePrice,
    bool? isPinned,
    bool? isFavorite,
    List<ExtraFeesEntity>? extraFees,
    String? remark,
    String? buyChannel,
    String? storageLocation,
    List<String>? remarkHistory,
    AssetRemarkEntity? remarkEntity,
    List<AssetRemarkEntity>? remarkHistoryEntities,
  }) {
    return Asset(
        id: id ?? this.id,
        name: name ?? this.name,
        categoryId: categoryId ?? this.categoryId,
        icon: icon ?? this.icon,
        price: price ?? this.price,
        purchaseDate: purchaseDate ?? this.purchaseDate,
        warrantyDate: warrantyDate ?? this.warrantyDate,
        priceMethod: priceMethod ?? this.priceMethod,
        dailyPrice: dailyPrice ?? this.dailyPrice,
        usagePriceValue: usagePriceValue ?? this.usagePriceValue,
        cyclePriceValue: cyclePriceValue ?? this.cyclePriceValue,
        isInService: isInService ?? this.isInService,
        retireDate: retireDate ?? this.retireDate,
        resalePrice: resalePrice ?? this.resalePrice,
        isPinned: isPinned ?? this.isPinned,
        isFavorite: isFavorite ?? this.isFavorite,
        extraFees: extraFees ?? this.extraFees,
        buyChannel: buyChannel ?? this.buyChannel,
        storageLocation: storageLocation ?? this.storageLocation,
        remark: remark ?? this.remark,
        remarkHistory: remarkHistory ?? this.remarkHistory,
        remarkEntity: remarkEntity ?? this.remarkEntity,
        remarkHistoryEntities:
            remarkHistoryEntities ?? this.remarkHistoryEntities,
        createdAt: createdAt);
  }

  factory Asset.fromJson(Map<String, dynamic> json) => _$AssetFromJson(json);

  Map<String, dynamic> toJson() => _$AssetToJson(this);

  // 判断是否是数据相等
  bool isValueEqual(Asset asset) {
    if (asset.id != id) {
      return false;
    }
    if (icon?.value != asset.icon?.value) {
      return false;
    }
    if (asset.categoryId != categoryId) {
      return false;
    }
    if (asset.name != name) {
      return false;
    }
    if (asset.price != price) {
      return false;
    }
    if (asset.purchaseDate != purchaseDate) {
      return false;
    }
    if (asset.warrantyDate != warrantyDate) {
      return false;
    }
    if (asset.priceMethod != priceMethod) {
      return false;
    }
    if (asset.dailyPrice != dailyPrice) {
      return false;
    }
    if (asset.cyclePriceValue != cyclePriceValue) {
      return false;
    }
    if (asset.usagePriceValue != usagePriceValue) {
      return false;
    }
    if (asset.isInService != isInService) {
      return false;
    }
    if (asset.resalePrice != resalePrice) {
      return false;
    }
    // 附加费用
    if (asset.extraFees.length != extraFees.length) {
      return false;
    }
    // 判断附加费用数据变化
    for (int i = 0; i < extraFees.length; i++) {
      if (asset.extraFees[i].isValueEqual(extraFees[i]) == false) {
        return false;
      }
    }

    if ((asset.buyChannel ?? '') != buyChannel) {
      return false;
    }
    if ((asset.storageLocation ?? "") != storageLocation) {
      return false;
    }
    if ((asset.remark ?? "") != remark) {
      return false;
    }
    return true;
  }

  /// 获取购买渠道实体对象
  BuyChannelEntity? getBuyChannelEntity() {
    if (buyChannel == null || buyChannel!.isEmpty) {
      return null;
    }

    try {
      final dataSource = getIt.get<BuyChannelLocalDataSource>();
      // 先尝试通过名称查找现有渠道
      final channel = dataSource.getBuyChannelByName(buyChannel!);

      // 如果找不到，创建一个临时的渠道对象用于显示
      if (channel == null) {
        return BuyChannelEntity(
          id: 'legacy_${buyChannel!.hashCode}',
          name: buyChannel!,
          order: -1,
        );
      }

      return channel;
    } catch (e) {
      // 如果获取失败，创建临时对象
      return BuyChannelEntity(
        id: 'legacy_${buyChannel!.hashCode}',
        name: buyChannel!,
        order: -1,
      );
    }
  }

  /// 检查是否有购买渠道信息
  bool get hasBuyChannel => buyChannel != null && buyChannel!.isNotEmpty;

  /// 获取购买渠道显示名称
  String get buyChannelDisplayName => buyChannel ?? '';

  /// 检查购买渠道是否为历史数据（没有对应的渠道实体）
  bool get isBuyChannelLegacy {
    if (!hasBuyChannel) return false;

    try {
      final dataSource = getIt.get<BuyChannelLocalDataSource>();
      final channel = dataSource.getBuyChannelByName(buyChannel!);
      return channel == null;
    } catch (e) {
      return true;
    }
  }

  /// 获取存放位置实体对象
  StorageLocationEntity? getStorageLocationEntity() {
    if (storageLocation == null || storageLocation!.isEmpty) {
      return null;
    }

    try {
      final dataSource = getIt.get<StorageLocationLocalDataSource>();
      // 先尝试通过名称查找现有位置
      final location = dataSource.getStorageLocationByName(storageLocation!);

      // 如果找不到，创建一个临时的位置对象用于显示
      if (location == null) {
        return StorageLocationEntity(
          id: 'legacy_${storageLocation!.hashCode}',
          name: storageLocation!,
          order: -1,
        );
      }

      return location;
    } catch (e) {
      // 如果获取失败，创建临时对象
      return StorageLocationEntity(
        id: 'legacy_${storageLocation!.hashCode}',
        name: storageLocation!,
        order: -1,
      );
    }
  }

  /// 检查是否有存放位置信息
  bool get hasStorageLocation =>
      storageLocation != null && storageLocation!.isNotEmpty;

  /// 获取存放位置显示名称
  String get storageLocationDisplayName => storageLocation ?? '';

  /// 检查存放位置是否为历史数据（没有对应的位置实体）
  bool get isStorageLocationLegacy {
    if (!hasStorageLocation) return false;

    try {
      final dataSource = getIt.get<StorageLocationLocalDataSource>();
      final location = dataSource.getStorageLocationByName(storageLocation!);
      return location == null;
    } catch (e) {
      return true;
    }
  }

  /// 获取当前备注内容（优先使用新实体，兼容旧字符串）
  String? get currentRemarkContent {
    return remarkEntity?.content ?? remark;
  }

  /// 获取备注历史列表（优先使用新实体，兼容旧字符串列表）
  List<AssetRemarkEntity> get currentRemarkHistory {
    if (remarkHistoryEntities != null && remarkHistoryEntities!.isNotEmpty) {
      return remarkHistoryEntities!;
    }

    // 兼容旧的字符串列表
    if (remarkHistory != null && remarkHistory!.isNotEmpty) {
      return remarkHistory!
          .map((content) => AssetRemarkEntity.fromLegacyString(content))
          .toList();
    }

    return [];
  }

  /// 检查是否有备注内容
  bool get hasRemarkContent {
    return (remarkEntity?.content.isNotEmpty ?? false) ||
        (remark?.isNotEmpty ?? false);
  }
}
