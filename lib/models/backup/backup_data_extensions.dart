import 'package:tasks/models/asset/asset_extensions.dart';
import 'package:tasks/models/backup/backup_data.dart';
import 'package:tasks/models/backup/image_download_info.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/category_extensions.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/models/sync_data.dart';
import 'package:tasks/utils/io_utils.dart';

/// BackupData的扩展方法
extension BackupDataExtensions on BackupData {
  /// 获取所有需要下载的图片信息
  Future<List<ImageDownloadInfo>> getImageDownloadInfos(
      String directory) async {
    final List<ImageDownloadInfo> downloadInfos = [];

    // 获取应用缓存目录
    final cacheDir = await IoUtils.getAppDir();
    final imagesDir = '${cacheDir.path}/images';

    // 处理资产中的图片
    for (final asset in syncData.assets) {
      if (asset.hasLocalImages()) {
        final imagePaths = await asset.getImagePaths();
        for (final originalPath in imagePaths) {
          final filename = originalPath.split('/').last;
          final remotePath = "/$directory/images/$filename";
          final localPath = '$imagesDir/$filename';

          downloadInfos.add(ImageDownloadInfo(
            originalPath: originalPath,
            filename: filename,
            remotePath: remotePath,
            localPath: localPath,
            entityId: asset.id,
            entityType: 'asset',
          ));
        }
      }
    }

    // 处理分类中的图片
    for (final category in syncData.categories) {
      if (category.hasLocalImages()) {
        final imagePaths = await category.getImagePaths();
        for (final originalPath in imagePaths) {
          final filename = originalPath.split('/').last;
          final remotePath = "/$directory/images/$filename";
          final localPath = '$imagesDir/$filename';

          downloadInfos.add(ImageDownloadInfo(
            originalPath: originalPath,
            filename: filename,
            remotePath: remotePath,
            localPath: localPath,
            entityId: category.id,
            entityType: 'category',
          ));
        }
      }
    }

    return downloadInfos;
  }

  /// 更新实体中的图片路径
  BackupData updateImagePaths(List<ImageDownloadInfo> downloadInfos) {
    // 创建路径映射
    final Map<String, String> pathMapping = {};
    for (final info in downloadInfos) {
      pathMapping[info.originalPath] = info.localPath;
    }

    // 更新资产中的图片路径
    final updatedAssets = syncData.assets.map((asset) {
      if (asset.icon != null &&
          asset.icon!.type == IconType.local &&
          asset.icon!.value != null &&
          pathMapping.containsKey(asset.icon!.value)) {
        return asset.copyWith(
          icon: AppIcon.local(pathMapping[asset.icon!.value]!),
        );
      }
      return asset;
    }).toList();

    // 更新分类中的图片路径
    final updatedCategories = syncData.categories.map((category) {
      if (category.icon != null &&
          category.icon!.type == IconType.local &&
          category.icon!.value != null &&
          pathMapping.containsKey(category.icon!.value)) {
        return CategoryEntity(
          id: category.id,
          name: category.name,
          order: category.order,
          icon: AppIcon.local(pathMapping[category.icon!.value]!),
        );
      }
      return category;
    }).toList();

    // 创建新的SyncData
    final updatedSyncData = SyncData(
      assets: updatedAssets,
      categories: updatedCategories,
    );

    // 返回更新后的BackupData
    return BackupData(
      timestamp: timestamp,
      version: version,
      syncData: updatedSyncData,
    );
  }
}
