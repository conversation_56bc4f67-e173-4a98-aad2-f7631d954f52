import 'dart:io' as io;

/// 图片下载信息
class ImageDownloadInfo {
  final String originalPath;
  final String filename;
  final String remotePath;
  final String localPath;
  final String entityId;
  final String entityType; // 'asset' or 'category'

  ImageDownloadInfo({
    required this.originalPath,
    required this.filename,
    required this.remotePath,
    required this.localPath,
    required this.entityId,
    required this.entityType,
  });

  /// 检查本地文件是否已存在
  Future<bool> localFileExists() async {
    final file = io.File(localPath);
    return await file.exists();
  }

  @override
  String toString() {
    return 'ImageDownloadInfo{entityType: $entityType, entityId: $entityId, filename: $filename, localPath: $localPath}';
  }
}

/// 图片下载结果
class ImageDownloadResult {
  final ImageDownloadInfo info;
  final bool success;
  final bool skipped;
  final String? error;

  ImageDownloadResult({
    required this.info,
    required this.success,
    this.skipped = false,
    this.error,
  });

  factory ImageDownloadResult.success(ImageDownloadInfo info) {
    return ImageDownloadResult(info: info, success: true);
  }

  factory ImageDownloadResult.skipped(ImageDownloadInfo info) {
    return ImageDownloadResult(info: info, success: true, skipped: true);
  }

  factory ImageDownloadResult.failed(ImageDownloadInfo info, String error) {
    return ImageDownloadResult(info: info, success: false, error: error);
  }
}
