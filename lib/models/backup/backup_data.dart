import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/models/sync_data.dart';

part 'backup_data.g.dart';

/// WebDAV备份数据实体
@JsonSerializable()
class BackupData {
  final String timestamp;
  final String version;
  final SyncData syncData;

  BackupData({
    required this.timestamp,
    required this.version,
    required this.syncData,
  });

  factory BackupData.fromJson(Map<String, dynamic> json) =>
      _$BackupDataFromJson(json);

  Map<String, dynamic> toJson() => _$BackupDataToJson(this);

  /// 创建新的备份数据
  factory BackupData.create(SyncData syncData) {
    return BackupData(
      timestamp: DateTime.now().toIso8601String(),
      version: "1.0",
      syncData: syncData,
    );
  }

  /// 从Map创建备份数据（兼容旧格式）
  factory BackupData.fromMap(Map<String, dynamic> map) {
    try {
      // 尝试直接解析
      return BackupData.fromJson(map);
    } catch (e) {
      // 如果失败，尝试兼容旧格式
      final syncDataMap = map["syncData"];
      SyncData syncData;

      if (syncDataMap is Map<String, dynamic>) {
        syncData = SyncData.fromJson(syncDataMap);
      } else {
        // 如果syncData已经是SyncData对象
        syncData = syncDataMap as SyncData;
      }

      return BackupData(
        timestamp: map["timestamp"] ?? DateTime.now().toIso8601String(),
        version: map["version"] ?? "1.0",
        syncData: syncData,
      );
    }
  }
}
