import 'package:json_annotation/json_annotation.dart';

part 'app_update_info.g.dart'; // 生成的代码文件

@JsonSerializable()
class AppUpdateInfo {
  final int? id; // ID（可空）
  final String? appId; // 应用ID（可空）
  final String? version; // 版本号（可空）
  final String? platform; // 平台（可空）
  final String? channel; // 渠道（可空）
  final String? updateType; // 更新类型（可空）
  final String? downloadUrl; // 下载地址（可空）
  final String? releaseNotes; // 更新日志（可空）
  final String? md5; // MD5 校验值（可空）
  final String? createdAt; // 创建时间（可空）
  final String? updatedAt; // 更新时间（可空）

  AppUpdateInfo({
    this.id,
    this.appId,
    this.version,
    this.platform,
    this.channel,
    this.updateType,
    this.downloadUrl,
    this.releaseNotes,
    this.md5,
    this.createdAt,
    this.updatedAt,
  });

  bool get isForce => updateType == "force";

  // 从 JSON 反序列化
  factory AppUpdateInfo.fromJson(Map<String, dynamic> json) =>
      _$AppUpdateInfoFromJson(json);

  // 序列化为 JSON
  Map<String, dynamic> toJson() => _$AppUpdateInfoToJson(this);
}
