import 'package:json_annotation/json_annotation.dart';

part 'user_preferences_entity.g.dart';

@JsonSerializable()
class UserPreferencesEntity {
  /// 退役物品不计入总资产
  final bool excludeRetired;

  /// 总物品价值是否显示
  final bool showTotalAssetValue;

  /// 货币符号
  final String currencySymbol;

  /// 货币代码 (如 USD, CNY, EUR)
  final String currencyCode;

  /// 自定义货币列表
  final List<CustomCurrency> customCurrencies;

  /// 是否启用隐私保护（任务栏模糊功能）
  final bool enablePrivacyBlur;

  /// 隐私保护模糊强度 (0.0-20.0)
  final double privacyBlurSigma;

  /// 搜索历史记录
  final List<String> searchHistory;

  /// 附加费用默认是否计入物品总价值
  final bool defaultIncludeExtraFeesInTotal;

  const UserPreferencesEntity({
    this.excludeRetired = true,
    this.showTotalAssetValue = true,
    this.currencySymbol = '¥',
    this.currencyCode = 'CNY',
    this.customCurrencies = const [],
    this.enablePrivacyBlur = true,
    this.privacyBlurSigma = 10.0,
    this.searchHistory = const [],
    this.defaultIncludeExtraFeesInTotal = true,
  });

  /// 默认配置（所有金额都显示）
  static UserPreferencesEntity defaultPreferences() {
    return const UserPreferencesEntity();
  }

  /// 复制并修改某些属性
  UserPreferencesEntity copyWith({
    bool? excludeRetired,
    bool? showTotalAssetValue,
    String? currencySymbol,
    String? currencyCode,
    List<CustomCurrency>? customCurrencies,
    bool? enablePrivacyBlur,
    double? privacyBlurSigma,
    List<String>? searchHistory,
    bool? defaultIncludeExtraFeesInTotal,
  }) {
    return UserPreferencesEntity(
      excludeRetired: excludeRetired ?? this.excludeRetired,
      showTotalAssetValue: showTotalAssetValue ?? this.showTotalAssetValue,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      currencyCode: currencyCode ?? this.currencyCode,
      customCurrencies: customCurrencies ?? this.customCurrencies,
      enablePrivacyBlur: enablePrivacyBlur ?? this.enablePrivacyBlur,
      privacyBlurSigma: privacyBlurSigma ?? this.privacyBlurSigma,
      searchHistory: searchHistory ?? this.searchHistory,
      defaultIncludeExtraFeesInTotal:
          defaultIncludeExtraFeesInTotal ?? this.defaultIncludeExtraFeesInTotal,
    );
  }

  factory UserPreferencesEntity.fromJson(Map<String, dynamic> json) =>
      _$UserPreferencesEntityFromJson(json);

  Map<String, dynamic> toJson() => _$UserPreferencesEntityToJson(this);
}

@JsonSerializable()
class CustomCurrency {
  /// 货币代码
  final String code;

  /// 货币符号
  final String symbol;

  /// 货币名称（可选）
  final String? name;

  /// 创建时间
  final DateTime createdAt;

  const CustomCurrency({
    required this.code,
    required this.symbol,
    this.name,
    required this.createdAt,
  });

  factory CustomCurrency.fromJson(Map<String, dynamic> json) =>
      _$CustomCurrencyFromJson(json);

  Map<String, dynamic> toJson() => _$CustomCurrencyToJson(this);

  /// 获取显示名称
  String get displayName => name ?? code;

  /// 格式化预览金额
  String formatPreview(double amount) => '$symbol${amount.toStringAsFixed(2)}';
}
