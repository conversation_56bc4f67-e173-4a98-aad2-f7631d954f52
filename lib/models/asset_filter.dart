import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset/asset_price_method.dart';
import 'package:tasks/models/asset/asset_sort.dart';
import 'package:tasks/widgets/filter_options/filter_item.dart';

part 'asset_filter.g.dart';

enum AssetStatus {
  inService,
  retired,
  favorite,
}

extension AssetStatusExtension on AssetStatus {
  String get description {
    switch (this) {
      case AssetStatus.inService:
        return S.current.homeFilterInService;
      case AssetStatus.retired:
        return S.current.homeFilterNotInService;
      case AssetStatus.favorite:
        return S.current.homeFilterCollect;
    }
  }
}

@JsonSerializable()
class AssetFilter {
  final AssetStatus? status;
  final String? categoryId;
  final AssetSortType sortType;
  final PriceMethod? priceMethod;

  AssetFilter({
    this.status,
    this.categoryId,
    this.priceMethod,
    this.sortType = AssetSortType.defaultSort,
  });

  // 复制方法
  AssetFilter copyWith({
    AssetStatus? status,
    String? categoryId,
    AssetSortType? sortType,
    PriceMethod? priceMethod,
  }) {
    return AssetFilter(
        status: status ?? this.status,
        categoryId: categoryId ?? this.categoryId,
        priceMethod: priceMethod ?? this.priceMethod,
        sortType: sortType ?? this.sortType);
  }

  List<String> get statusIds {
    if (status == null) {
      return [FilterItem.filterAllId];
    }
    return [status!.name];
  }

  List<String> get categoryIds {
    if (categoryId == null) {
      return [FilterItem.filterAllId];
    }
    return [categoryId!];
  }

  List<String> get priceMethodIds {
    if (priceMethod == null) {
      return [FilterItem.filterAllId];
    }
    return [priceMethod!.name];
  }

  bool get primaryFilter {
    return status == null && categoryId == null && priceMethod == null;
  }

  factory AssetFilter.fromJson(Map<String, dynamic> json) =>
      _$AssetFilterFromJson(json);

  Map<String, dynamic> toJson() => _$AssetFilterToJson(this);
}
