import 'dart:io' as io;

import 'package:tasks/models/category.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/utils/io_utils.dart';

/// CategoryEntity类的扩展方法
extension CategoryExtensions on CategoryEntity {
  /// 获取当前分类的所有图片文件路径
  /// 返回本地存在的图片文件路径列表
  Future<List<String>> getImagePaths() async {
    final List<String> imagePaths = [];

    // 检查分类的icon是否为本地图片
    if (icon != null && icon!.type == IconType.local && icon!.value != null) {
      final imagePath = icon!.value!;

      // 检查文件是否存在
      final file = io.File(imagePath);
      if (await file.exists()) {
        imagePaths.add(imagePath);
      } else {
        // 尝试在当前应用目录中查找文件
        final appDir = await IoUtils.getAppDir();
        final filename = imagePath.split('/').last;
        final normalizedPath = '${appDir.path}/images/$filename';
        final normalizedFile = io.File(normalizedPath);

        if (await normalizedFile.exists()) {
          imagePaths.add(normalizedPath);
        }
      }
    }

    return imagePaths;
  }

  /// 获取当前分类的所有图片文件信息
  /// 返回包含文件路径和文件名的Map列表
  Future<List<Map<String, String>>> getImageFiles() async {
    final List<Map<String, String>> imageFiles = [];
    final imagePaths = await getImagePaths();

    for (final path in imagePaths) {
      final filename = path.split('/').last;
      imageFiles.add({
        'path': path,
        'filename': filename,
      });
    }

    return imageFiles;
  }

  /// 检查分类是否有本地图片
  bool hasLocalImages() {
    return icon != null &&
        icon!.type == IconType.local &&
        icon!.value != null &&
        icon!.value!.isNotEmpty;
  }
}
