import 'package:json_annotation/json_annotation.dart';

part 'buy_channel.g.dart';

@JsonSerializable()
class BuyChannelEntity {
  final String id;
  final String name;
  int order;

  BuyChannelEntity({
    required this.id,
    required this.name,
    required this.order,
  });

  factory BuyChannelEntity.fromJson(Map<String, dynamic> json) =>
      _$BuyChannelEntityFromJson(json);

  Map<String, dynamic> toJson() => _$BuyChannelEntityToJson(this);
}
