import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:uuid/uuid.dart';

part 'recycle_bin_item.g.dart';

@JsonSerializable()
class RecycleBinItem {
  final String id;
  final Asset asset;
  final DateTime deletedAt;

  RecycleBinItem({
    required this.asset,
    required this.deletedAt,
    String? id,
  }) : id = id ?? Uuid().v4();

  factory RecycleBinItem.fromJson(Map<String, dynamic> json) =>
      _$RecycleBinItemFromJson(json);

  Map<String, dynamic> toJson() => _$RecycleBinItemToJson(this);

  RecycleBinItem copyWith({
    String? id,
    Asset? asset,
    DateTime? deletedAt,
  }) {
    return RecycleBinItem(
      id: id ?? this.id,
      asset: asset ?? this.asset,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RecycleBinItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
