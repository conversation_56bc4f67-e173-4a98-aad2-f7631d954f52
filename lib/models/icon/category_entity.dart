import 'package:json_annotation/json_annotation.dart';

import 'icon_entity.dart';

part 'category_entity.g.dart';

@JsonSerializable()
class CategoryIconEntity {
  final String id;
  final int index;
  final String name;
  final List<IconEntity> icons;

  CategoryIconEntity({
    required this.id,
    required this.index,
    required this.name,
    this.icons = const [],
  });

  factory CategoryIconEntity.fromJson(Map<String, dynamic> json) =>
      _$CategoryIconEntityFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryIconEntityToJson(this);

  get measureLines {
    return (icons.length / 4).ceil();
  }

  CategoryIconEntity copyWith({
    String? id,
    int? index,
    String? name,
    List<IconEntity>? icons,
  }) {
    return CategoryIconEntity(
      id: id ?? this.id,
      index: index ?? this.index,
      name: name ?? this.name,
      icons: icons ?? this.icons,
    );
  }
}
