import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/models/category_icon.dart';

part 'icon_entity.g.dart';

@JsonSerializable()
class IconEntity {
  final String id;
  final int index;
  final String name;
  final String url;
  final bool requiredVip;
  final AppIcon icon;

  IconEntity({
    required this.id,
    required this.index,
    required this.name,
    required this.url,
    required this.requiredVip,
    required this.icon,
  });

  factory IconEntity.fromJson(Map<String, dynamic> json) =>
      _$IconEntityFromJson(json);

  Map<String, dynamic> toJson() => _$IconEntityToJson(this);
}
