import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/models/category_icon.dart';

part 'category.g.dart';

@JsonSerializable()
class CategoryEntity {
  final String id;
  final String name;
  int order;
  final AppIcon? icon;

  CategoryEntity({
    required this.id,
    required this.name,
    required this.order,
    this.icon,
  });

  factory CategoryEntity.fromJson(Map<String, dynamic> json) =>
      _$CategoryEntityFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryEntityToJson(this);
}
