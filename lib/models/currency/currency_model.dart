import 'dart:ui';

class CurrencyModel {
  final String code;
  final String symbol;
  final String name;

  const CurrencyModel({
    required this.code,
    required this.symbol,
    required this.name,
  });

  // 常用货币列表
  static List<CurrencyModel> availableCurrencies = [
    CurrencyModel(code: 'CNY', symbol: '¥', name: '人民币'),
    CurrencyModel(code: 'USD', symbol: '\$', name: '美元'),
    CurrencyModel(code: 'EUR', symbol: '€', name: '欧元'),
    CurrencyModel(code: 'GBP', symbol: '£', name: '英镑'),
    CurrencyModel(code: 'JPY', symbol: '¥', name: '日元'),
    CurrencyModel(code: 'KRW', symbol: '₩', name: '韩元'),
    CurrencyModel(code: 'HKD', symbol: 'HK\$', name: '港币'),
    CurrencyModel(code: 'TWD', symbol: 'NT\$', name: '新台币'),
    CurrencyModel(code: 'SGD', symbol: 'S\$', name: '新加坡元'),
    CurrencyModel(code: 'AUD', symbol: 'A\$', name: '澳元'),
    CurrencyModel(code: 'CAD', symbol: 'C\$', name: '加元'),
    CurrencyModel(code: 'CHF', symbol: 'Fr', name: '瑞士法郎'),
    CurrencyModel(code: 'RUB', symbol: '₽', name: '俄罗斯卢布'),
    CurrencyModel(code: 'INR', symbol: '₹', name: '印度卢比'),
  ];

  // 根据语言代码获取默认货币
  static CurrencyModel getDefaultCurrencyByLocale(Locale locale) {
    final languageCode = locale.languageCode;
    final countryCode = locale.countryCode;

    if (countryCode == 'CN' || languageCode == 'zh') {
      return availableCurrencies.firstWhere((c) => c.code == 'CNY');
    } else if (countryCode == 'US' || languageCode == 'en') {
      return availableCurrencies.firstWhere((c) => c.code == 'USD');
    } else if (countryCode == 'JP' || languageCode == 'ja') {
      return availableCurrencies.firstWhere((c) => c.code == 'JPY');
    } else if (countryCode == 'KR' || languageCode == 'ko') {
      return availableCurrencies.firstWhere((c) => c.code == 'KRW');
    } else if (countryCode == 'GB') {
      return availableCurrencies.firstWhere((c) => c.code == 'GBP');
    } else if (countryCode == 'HK') {
      return availableCurrencies.firstWhere((c) => c.code == 'HKD');
    } else if (countryCode == 'TW') {
      return availableCurrencies.firstWhere((c) => c.code == 'TWD');
    } else if (countryCode == 'SG') {
      return availableCurrencies.firstWhere((c) => c.code == 'SGD');
    } else if (countryCode == 'AU') {
      return availableCurrencies.firstWhere((c) => c.code == 'AUD');
    } else if (countryCode == 'CA') {
      return availableCurrencies.firstWhere((c) => c.code == 'CAD');
    } else if (countryCode == 'CH') {
      return availableCurrencies.firstWhere((c) => c.code == 'CHF');
    } else if (countryCode == 'RU') {
      return availableCurrencies.firstWhere((c) => c.code == 'RUB');
    } else if (countryCode == 'IN') {
      return availableCurrencies.firstWhere((c) => c.code == 'INR');
    } else if (languageCode == 'de' ||
        languageCode == 'fr' ||
        languageCode == 'it' ||
        languageCode == 'es') {
      return availableCurrencies.firstWhere((c) => c.code == 'EUR');
    }

    // 默认返回人民币
    return availableCurrencies.firstWhere((c) => c.code == 'CNY');
  }

  // 根据货币代码获取货币模型
  static CurrencyModel getCurrencyByCode(String code) {
    try {
      return availableCurrencies.firstWhere((c) => c.code == code);
    } catch (e) {
      return availableCurrencies.first; // 默认返回第一个货币
    }
  }
}
