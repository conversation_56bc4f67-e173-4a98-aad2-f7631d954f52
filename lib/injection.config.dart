// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:shared_preferences/shared_preferences.dart' as _i460;
import 'package:tasks/core/api_client.dart' as _i421;
import 'package:tasks/data/asset/asset_local_data_source.dart' as _i459;
import 'package:tasks/data/asset/asset_repo.dart' as _i835;
import 'package:tasks/data/asset/search_assets_usecase.dart' as _i493;
import 'package:tasks/data/buy_channel/buy_channel_local_data_source.dart'
    as _i702;
import 'package:tasks/data/cache/cache_lds.dart' as _i842;
import 'package:tasks/data/cache/cache_repo.dart' as _i1018;
import 'package:tasks/data/category/category_local_data_source.dart' as _i193;
import 'package:tasks/data/config/app_config.dart' as _i831;
import 'package:tasks/data/config/config_local_data_source.dart' as _i236;
import 'package:tasks/data/config/config_repo.dart' as _i491;
import 'package:tasks/data/data_sync_repo.dart' as _i205;
import 'package:tasks/data/icons/icon_local_data_source.dart' as _i423;
import 'package:tasks/data/icons/icons_data_source.dart' as _i555;
import 'package:tasks/data/icons/icons_repository.dart' as _i339;
import 'package:tasks/data/membership_activity/membership_activity_repo.dart'
    as _i868;
import 'package:tasks/data/message/message_repo.dart' as _i440;
import 'package:tasks/data/pay/apple_order_repo.dart' as _i524;
import 'package:tasks/data/pay/order_remote_source.dart' as _i1042;
import 'package:tasks/data/pay/order_repo.dart' as _i689;
import 'package:tasks/data/recycle_bin/recycle_bin_local_data_source.dart'
    as _i1067;
import 'package:tasks/data/share/share_repo.dart' as _i558;
import 'package:tasks/data/storage_location/storage_location_local_data_source.dart'
    as _i471;
import 'package:tasks/data/theme/locale_local_data_source.dart' as _i300;
import 'package:tasks/data/theme/theme_local_data_source.dart' as _i958;
import 'package:tasks/data/user/LoginUseCase.dart' as _i473;
import 'package:tasks/data/user/user_data_source.dart' as _i140;
import 'package:tasks/data/user/user_repo.dart' as _i627;
import 'package:tasks/data/user/wechat_repo.dart' as _i909;
import 'package:tasks/data/user_preferences/user_preferences_lds.dart' as _i942;
import 'package:tasks/injection.dart' as _i652;
import 'package:tasks/modal/upgrade/widgets/apple/apple_upgrade_state.dart'
    as _i363;
import 'package:tasks/pages/main/home/<USER>' as _i606;
import 'package:tasks/pages/main/main_state.dart' as _i671;
import 'package:tasks/providers/asset_store.dart' as _i597;
import 'package:tasks/providers/buy_channel_store.dart' as _i539;
import 'package:tasks/providers/category_store.dart' as _i310;
import 'package:tasks/providers/storage_location_store.dart' as _i911;
import 'package:tasks/providers/user_preferences_store.dart' as _i1058;
import 'package:tasks/providers/user_store.dart' as _i1004;
import 'package:tasks/providers/wechat_state.dart' as _i989;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    gh.factory<_i842.CacheLds>(() => _i842.CacheLds());
    gh.factory<_i606.HomeState>(() => _i606.HomeState());
    await gh.singletonAsync<_i460.SharedPreferences>(
      () => registerModule.prefs,
      preResolve: true,
    );
    gh.lazySingleton<_i236.ConfigLocalDataSource>(
        () => _i236.ConfigLocalDataSource(gh<_i460.SharedPreferences>()));
    gh.lazySingleton<_i459.AssetLocalDataSource>(
        () => _i459.AssetLocalDataSource(
              gh<_i460.SharedPreferences>(),
              gh<_i842.CacheLds>(),
            ));
    gh.lazySingleton<_i193.CategoryLocalDataSource>(
        () => _i193.CategoryLocalDataSource(
              gh<_i460.SharedPreferences>(),
              gh<_i842.CacheLds>(),
            ));
    gh.lazySingleton<_i702.BuyChannelLocalDataSource>(
        () => _i702.BuyChannelLocalDataSource(
              gh<_i460.SharedPreferences>(),
              gh<_i842.CacheLds>(),
            ));
    gh.lazySingleton<_i493.SearchAssetsUseCase>(() => _i493.SearchAssetsUseCase(
          gh<_i459.AssetLocalDataSource>(),
          gh<_i193.CategoryLocalDataSource>(),
        ));
    gh.lazySingleton<_i310.CategoryStore>(
        () => _i310.CategoryStore(gh<_i193.CategoryLocalDataSource>()));
    gh.lazySingleton<_i539.BuyChannelStore>(
        () => _i539.BuyChannelStore(gh<_i702.BuyChannelLocalDataSource>()));
    gh.lazySingleton<_i471.StorageLocationLocalDataSource>(() =>
        _i471.StorageLocationLocalDataSource(gh<_i460.SharedPreferences>()));
    gh.lazySingleton<_i831.AppConfigDataSource>(
        () => _i831.AppConfigDataSource(gh<_i460.SharedPreferences>()));
    gh.lazySingleton<_i140.UserDataSource>(
        () => _i140.UserDataSource(gh<_i460.SharedPreferences>()));
    gh.lazySingleton<_i958.ThemeLocalDataSource>(
        () => _i958.ThemeLocalDataSource(gh<_i460.SharedPreferences>()));
    gh.lazySingleton<_i300.LocaleLocalDataSource>(
        () => _i300.LocaleLocalDataSource(gh<_i460.SharedPreferences>()));
    gh.lazySingleton<_i942.UserPreferencesLds>(
        () => _i942.UserPreferencesLds(gh<_i460.SharedPreferences>()));
    gh.lazySingleton<_i423.IconLocalDataSource>(
        () => _i423.IconLocalDataSource(gh<_i460.SharedPreferences>()));
    gh.lazySingleton<_i1067.RecycleBinLocalDataSource>(
        () => _i1067.RecycleBinLocalDataSource(gh<_i460.SharedPreferences>()));
    gh.lazySingleton<_i911.StorageLocationStore>(() =>
        _i911.StorageLocationStore(gh<_i471.StorageLocationLocalDataSource>()));
    gh.lazySingleton<_i1058.UserPreferencesStore>(
        () => _i1058.UserPreferencesStore(gh<_i942.UserPreferencesLds>()));
    gh.lazySingleton<_i421.ApiClient>(
        () => _i421.ApiClient(gh<_i140.UserDataSource>()));
    gh.lazySingleton<_i491.ConfigRepo>(() => _i491.ConfigRepo(
          gh<_i236.ConfigLocalDataSource>(),
          gh<_i421.ApiClient>(),
        ));
    gh.lazySingleton<_i205.DataSyncRepo>(() => _i205.DataSyncRepo(
          gh<_i459.AssetLocalDataSource>(),
          gh<_i193.CategoryLocalDataSource>(),
          gh<_i702.BuyChannelLocalDataSource>(),
          gh<_i471.StorageLocationLocalDataSource>(),
          gh<_i421.ApiClient>(),
        ));
    gh.lazySingleton<_i524.AppleOrderRepo>(
        () => _i524.AppleOrderRepo(gh<_i421.ApiClient>()));
    gh.lazySingleton<_i558.ShareRepo>(
        () => _i558.ShareRepo(gh<_i421.ApiClient>()));
    gh.lazySingleton<_i1042.OrderRemoteSource>(
        () => _i1042.OrderRemoteSource(gh<_i421.ApiClient>()));
    gh.lazySingleton<_i440.MessageRepo>(
        () => _i440.MessageRepo(gh<_i421.ApiClient>()));
    gh.lazySingleton<_i909.WechatRepo>(
        () => _i909.WechatRepo(gh<_i421.ApiClient>()));
    gh.lazySingleton<_i555.IconsDataSource>(
        () => _i555.IconsDataSource(gh<_i421.ApiClient>()));
    gh.lazySingleton<_i868.MembershipActivityRepo>(
        () => _i868.MembershipActivityRepo(gh<_i421.ApiClient>()));
    gh.lazySingleton<_i689.OrderRepo>(
        () => _i689.OrderRepo(gh<_i1042.OrderRemoteSource>()));
    gh.lazySingleton<_i1018.CacheRepo>(
        () => _i1018.CacheRepo(gh<_i205.DataSyncRepo>()));
    gh.lazySingleton<_i627.UserRepo>(() => _i627.UserRepo(
          gh<_i140.UserDataSource>(),
          gh<_i421.ApiClient>(),
        ));
    gh.lazySingleton<_i835.AssetRepo>(() => _i835.AssetRepo(
          gh<_i459.AssetLocalDataSource>(),
          gh<_i1058.UserPreferencesStore>(),
          gh<_i193.CategoryLocalDataSource>(),
          gh<_i491.ConfigRepo>(),
        ));
    gh.lazySingleton<_i473.LoginUseCase>(() => _i473.LoginUseCase(
          gh<_i140.UserDataSource>(),
          gh<_i627.UserRepo>(),
          gh<_i909.WechatRepo>(),
        ));
    gh.lazySingleton<_i1004.UserStore>(
        () => _i1004.UserStore(gh<_i627.UserRepo>()));
    gh.lazySingleton<_i339.IconsRepository>(() => _i339.IconsRepository(
          gh<_i555.IconsDataSource>(),
          gh<_i423.IconLocalDataSource>(),
        ));
    gh.lazySingleton<_i363.AppleUpgradeState>(() => _i363.AppleUpgradeState(
          gh<_i524.AppleOrderRepo>(),
          gh<_i1004.UserStore>(),
        ));
    gh.factory<_i671.MainState>(
        () => _i671.MainState(repo: gh<_i835.AssetRepo>()));
    gh.lazySingleton<_i597.AssetStore>(() => _i597.AssetStore(
          gh<_i835.AssetRepo>(),
          gh<_i459.AssetLocalDataSource>(),
        ));
    gh.lazySingleton<_i989.WechatState>(() => _i989.WechatState(
          gh<_i909.WechatRepo>(),
          gh<_i473.LoginUseCase>(),
        ));
    return this;
  }
}

class _$RegisterModule extends _i652.RegisterModule {}
