import 'package:json_annotation/json_annotation.dart';

part 'invited_record_resp.g.dart';

@JsonSerializable()
class InvitedRecordResp {
  final List<InvitedRecordItem>? list;

  InvitedRecordResp({
    this.list,
  });

  factory InvitedRecordResp.fromJson(Map<String, dynamic> json) =>
      _$InvitedRecordRespFromJson(json);

  Map<String, dynamic> toJson() => _$InvitedRecordRespToJson(this);
}

@JsonSerializable()
class InvitedRecordItem {
  final String? createTime;
  final String? maskUserName;
  final bool? isVip;

  InvitedRecordItem({
    this.createTime,
    this.maskUserName,
    this.isVip,
  });

  factory InvitedRecordItem.fromJson(Map<String, dynamic> json) =>
      _$InvitedRecordItemFromJson(json);

  Map<String, dynamic> toJson() => _$InvitedRecordItemToJson(this);
}
