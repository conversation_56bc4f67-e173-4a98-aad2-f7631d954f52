import 'package:injectable/injectable.dart';
import 'package:tasks/core/api_client.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/share/invited_record_resp.dart';

@lazySingleton
class ShareRepo {
  final ApiClient client;

  ShareRepo(this.client);

  /// 获取邀请记录
  Future<Result<List<InvitedRecordItem>>> getInvitedRecord() async {
    var result = await client.get("/share/v1/invitedList",
        fromJson: InvitedRecordResp.fromJson);
    return result.map((r) => r?.list ?? List.empty());
  }
}
