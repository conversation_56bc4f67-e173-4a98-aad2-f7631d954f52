import 'dart:convert';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/models/user_preferences/user_preferences_entity.dart';

@lazySingleton
class UserPreferencesLds {
  final SharedPreferences _preferences;

  UserPreferencesLds(this._preferences);

  // 用户偏好设置
  static const String _key_user_preferences = "user_preferences";

  // 保存用户偏好设置
  Future<void> saveUserPreferences(UserPreferencesEntity preferences) async {
    try {
      final encoded = json.encode(preferences.toJson());
      await _preferences.setString(_key_user_preferences, encoded);
    } catch (e) {
      print("保存用户偏好设置出错：$e");
    }
  }

  // 获取用户偏好设置
  UserPreferencesEntity getUserPreferences() {
    try {
      final encoded = _preferences.getString(_key_user_preferences);
      if (encoded != null) {
        final decoded = json.decode(encoded);
        return UserPreferencesEntity.fromJson(decoded);
      } else {
        return UserPreferencesEntity.defaultPreferences();
      }
    } catch (e) {
      print("获取用户偏好设置出错：$e");
      return UserPreferencesEntity.defaultPreferences();
    }
  }

  // 同步获取用户偏好设置
  UserPreferencesEntity? getUserPreferencesSync() {
    try {
      final encoded = _preferences.getString(_key_user_preferences);
      if (encoded != null) {
        final decoded = json.decode(encoded);
        return UserPreferencesEntity.fromJson(decoded);
      } else {
        return UserPreferencesEntity.defaultPreferences();
      }
    } catch (e) {
      print("同步获取用户偏好设置出错：$e");
      return UserPreferencesEntity.defaultPreferences();
    }
  }

  // 更新特定的偏好设置
  Future<void> updatePreference({
    bool? showTotalAssetValue,
    String? currencySymbol,
    String? currencyCode,
    List<CustomCurrency>? customCurrencies,
    bool? enablePrivacyBlur,
    double? privacyBlurSigma,
    List<String>? searchHistory,
    bool? excludeRetired,
    bool? defaultIncludeExtraFeesInTotal,
  }) async {
    final currentPreferences = getUserPreferences();
    final updatedPreferences = currentPreferences.copyWith(
      showTotalAssetValue: showTotalAssetValue,
      currencySymbol: currencySymbol,
      currencyCode: currencyCode,
      customCurrencies: customCurrencies,
      enablePrivacyBlur: enablePrivacyBlur,
      privacyBlurSigma: privacyBlurSigma,
      searchHistory: searchHistory,
      excludeRetired: excludeRetired,
      defaultIncludeExtraFeesInTotal: defaultIncludeExtraFeesInTotal,
    );
    await saveUserPreferences(updatedPreferences);
  }

  // 添加自定义货币
  Future<void> addCustomCurrency(CustomCurrency currency) async {
    final currentPreferences = getUserPreferences();
    final updatedCurrencies =
        List<CustomCurrency>.from(currentPreferences.customCurrencies);

    // 检查是否已存在相同代码的货币
    final existingIndex =
        updatedCurrencies.indexWhere((c) => c.code == currency.code);
    if (existingIndex != -1) {
      // 如果存在，则更新
      updatedCurrencies[existingIndex] = currency;
    } else {
      // 如果不存在，则添加
      updatedCurrencies.add(currency);
    }

    await updatePreference(customCurrencies: updatedCurrencies);
  }

  // 删除自定义货币
  Future<void> removeCustomCurrency(String currencyCode) async {
    final currentPreferences = getUserPreferences();
    final updatedCurrencies = currentPreferences.customCurrencies
        .where((c) => c.code != currencyCode)
        .toList();

    await updatePreference(customCurrencies: updatedCurrencies);
  }

  // 更新隐私保护设置
  Future<void> updatePrivacyBlurSettings({
    bool? enablePrivacyBlur,
    double? privacyBlurSigma,
  }) async {
    await updatePreference(
      enablePrivacyBlur: enablePrivacyBlur,
      privacyBlurSigma: privacyBlurSigma,
    );
  }

  // 获取隐私保护设置
  Future<Map<String, dynamic>> getPrivacyBlurSettings() async {
    final preferences = getUserPreferences();
    return {
      'enablePrivacyBlur': preferences.enablePrivacyBlur,
      'privacyBlurSigma': preferences.privacyBlurSigma,
    };
  }

  // 添加搜索历史
  Future<void> addSearchHistory(String searchText) async {
    if (searchText.trim().isEmpty) return;

    final currentPreferences = getUserPreferences();
    final updatedHistory = List<String>.from(currentPreferences.searchHistory);

    // 如果已存在，先移除
    updatedHistory.remove(searchText);
    // 添加到最前面
    updatedHistory.insert(0, searchText);

    // 限制历史记录数量为20条
    if (updatedHistory.length > 20) {
      updatedHistory.removeRange(20, updatedHistory.length);
    }

    await updatePreference(searchHistory: updatedHistory);
  }

  // 获取搜索历史
  Future<List<String>> getSearchHistory() async {
    final preferences = getUserPreferences();
    return preferences.searchHistory;
  }

  // 清空搜索历史
  Future<void> clearSearchHistory() async {
    await updatePreference(searchHistory: []);
  }

  // 移除单个搜索历史项
  Future<void> removeSearchHistory(String searchText) async {
    final currentPreferences = getUserPreferences();
    final updatedHistory = List<String>.from(currentPreferences.searchHistory);
    updatedHistory.remove(searchText);
    await updatePreference(searchHistory: updatedHistory);
  }
}
