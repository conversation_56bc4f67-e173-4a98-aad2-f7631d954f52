import 'dart:convert';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/models/storage_location.dart';

@lazySingleton
class StorageLocationLocalDataSource {
  final SharedPreferences _preferences;
  static const String _key = 'storage_locations';

  StorageLocationLocalDataSource(this._preferences);

  /// 获取存放位置数据，如果本地没有数据，则返回空列表
  Future<List<StorageLocationEntity>> getStorageLocations() async {
    final String? jsonString = _preferences.getString(_key);
    try {
      if (jsonString == null) {
        return [];
      }

      final List<dynamic> decoded = json.decode(jsonString);
      return decoded.map((i) => StorageLocationEntity.fromJson(i)).toList();
    } catch (e) {
      return [];
    }
  }

  /// 保存存放位置数据
  Future<void> saveStorageLocations(List<StorageLocationEntity> locations) async {
    final String encoded =
        json.encode(locations.map((e) => e.toJson()).toList());
    await _preferences.setString(_key, encoded);
  }

  /// 添加存放位置
  Future<void> addStorageLocation(String name) async {
    final locations = await getStorageLocations();
    final newLocation = StorageLocationEntity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      order: locations.length,
    );
    locations.add(newLocation);
    await saveStorageLocations(locations);
  }

  /// 更新存放位置
  Future<void> updateStorageLocation(String id, String name) async {
    final locations = await getStorageLocations();
    final index = locations.indexWhere((location) => location.id == id);
    if (index != -1) {
      locations[index] = StorageLocationEntity(
        id: id,
        name: name,
        order: locations[index].order,
      );
      await saveStorageLocations(locations);
    }
  }

  /// 删除存放位置
  Future<void> deleteStorageLocation(String id) async {
    final locations = await getStorageLocations();
    locations.removeWhere((location) => location.id == id);
    // 重新排序
    for (int i = 0; i < locations.length; i++) {
      locations[i].order = i;
    }
    await saveStorageLocations(locations);
  }

  /// 根据ID获取存放位置
  StorageLocationEntity? getStorageLocation(String id) {
    // 这里需要同步获取，所以先从缓存中获取
    final String? jsonString = _preferences.getString(_key);
    if (jsonString == null) return null;
    
    try {
      final List<dynamic> decoded = json.decode(jsonString);
      final locations = decoded.map((i) => StorageLocationEntity.fromJson(i)).toList();
      return locations.firstWhere((location) => location.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 根据名称获取存放位置（用于处理历史数据）
  StorageLocationEntity? getStorageLocationByName(String name) {
    final String? jsonString = _preferences.getString(_key);
    if (jsonString == null) return null;
    
    try {
      final List<dynamic> decoded = json.decode(jsonString);
      final locations = decoded.map((i) => StorageLocationEntity.fromJson(i)).toList();
      return locations.where((location) => location.name == name).firstOrNull;
    } catch (e) {
      return null;
    }
  }
}
