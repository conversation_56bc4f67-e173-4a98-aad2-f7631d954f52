import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/recycle_bin/recycle_bin_item.dart';

@lazySingleton
class RecycleBinLocalDataSource {
  final SharedPreferences _preferences;

  RecycleBinLocalDataSource(this._preferences);

  static const String _key = 'recycle_bin_items';

  /// 获取回收站中的所有项目
  Future<List<RecycleBinItem>> getRecycleBinItems() async {
    final String? itemsJson = _preferences.getString(_key);
    if (itemsJson == null) return [];

    try {
      final List<dynamic> decoded = json.decode(itemsJson);
      return decoded.map((item) => RecycleBinItem.fromJson(item)).toList();
    } catch (e) {
      return [];
    }
  }

  /// 同步获取回收站中的所有项目
  List<RecycleBinItem> getRecycleBinItemsSync() {
    final String? itemsJson = _preferences.getString(_key);
    if (itemsJson == null) return [];

    try {
      final List<dynamic> decoded = json.decode(itemsJson);
      return decoded.map((item) => RecycleBinItem.fromJson(item)).toList();
    } catch (e) {
      return [];
    }
  }

  /// 保存回收站项目列表
  Future<void> saveRecycleBinItems(List<RecycleBinItem> items) async {
    final String encoded = json.encode(items.map((e) => e.toJson()).toList());
    await _preferences.setString(_key, encoded);
  }

  /// 添加资产到回收站
  Future<void> addToRecycleBin(Asset asset) async {
    final items = await getRecycleBinItems();
    final recycleBinItem = RecycleBinItem(
      asset: asset,
      deletedAt: DateTime.now(),
    );
    items.insert(0, recycleBinItem); // 最新删除的在前面
    await saveRecycleBinItems(items);
  }

  /// 从回收站恢复资产
  Future<Asset?> restoreFromRecycleBin(String recycleBinItemId) async {
    final items = await getRecycleBinItems();
    final index = items.indexWhere((item) => item.id == recycleBinItemId);

    if (index != -1) {
      final item = items[index];
      items.removeAt(index);
      await saveRecycleBinItems(items);
      return item.asset;
    }

    return null;
  }

  /// 从回收站永久删除
  Future<void> permanentlyDelete(String recycleBinItemId) async {
    final items = await getRecycleBinItems();
    items.removeWhere((item) => item.id == recycleBinItemId);
    await saveRecycleBinItems(items);
  }

  /// 批量从回收站恢复资产
  Future<List<Asset>> batchRestoreFromRecycleBin(
      List<String> recycleBinItemIds) async {
    final items = await getRecycleBinItems();
    final restoredAssets = <Asset>[];

    for (final id in recycleBinItemIds) {
      final index = items.indexWhere((item) => item.id == id);
      if (index != -1) {
        restoredAssets.add(items[index].asset);
        items.removeAt(index);
      }
    }

    await saveRecycleBinItems(items);
    return restoredAssets;
  }

  /// 批量永久删除
  Future<void> batchPermanentlyDelete(List<String> recycleBinItemIds) async {
    final items = await getRecycleBinItems();
    items.removeWhere((item) => recycleBinItemIds.contains(item.id));
    await saveRecycleBinItems(items);
  }

  /// 清空回收站
  Future<void> clearRecycleBin() async {
    await _preferences.remove(_key);
  }

  /// 根据ID获取回收站项目
  Future<RecycleBinItem?> getRecycleBinItemById(String id) async {
    final items = await getRecycleBinItems();
    try {
      return items.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }
}
