import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'package:tasks/generated/assets.gen.dart';
import 'package:tasks/models/sync_data.dart';

@injectable
class CacheLds {
  /// 从本地assets缓存中获取同步数据
  Future<SyncData?> getAssetSyncData() async {
    try {
      final syncData = await rootBundle.loadString(Assets.cache.cache);
      final decoded = json.decode(syncData);
      return SyncData.fromJson(decoded);
    } catch (e) {
      return null;
    }
  }
}
