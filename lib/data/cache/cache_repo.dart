import 'dart:convert';
import 'dart:io' as io;

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/asset/asset_local_data_source.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/data/data_sync_repo.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/asset/asset_extensions.dart';
import 'package:tasks/models/backup/backup_data.dart';
import 'package:tasks/models/backup/backup_data_extensions.dart';
import 'package:tasks/models/backup/image_download_info.dart';
import 'package:tasks/models/category_extensions.dart';
import 'package:tasks/models/sync_data.dart';
import 'package:tasks/models/webdav/webdav_config_entity.dart';
import 'package:tasks/utils/io_utils.dart';
import 'package:webdav_client/webdav_client.dart';

@lazySingleton
class CacheRepo {
  final DataSyncRepo repo;

  CacheRepo(this.repo);

  Future<Result<String?>> backupToWebdav(WebdavConfigEntity config) async {
    try {
      // 获取需要备份的数据
      final backupData = await _prepareBackupData();

      // 将数据备份到WebDAV
      // 这里需要实现WebDAV客户端逻辑，或者使用第三方库
      // 例如：使用http包发送PUT请求到WebDAV服务器

      // 示例代码（实际实现需要根据WebDAV协议进行）
      final serverUrl = config.serverUrl!;
      final username = config.username!;
      final password = config.password!;
      final directory = config.directory ?? "recollect";

      // 创建WebDAV客户端
      var client = newClient(
        serverUrl,
        user: username,
        password: password,
        debug: kDebugMode,
      );

      // 确保目录存在
      try {
        await client.mkdir('/$directory');
      } catch (e) {
        // 目录可能已存在，忽略错误
        print("目录可能已存在: $e");
      }

      // 确保images子目录存在
      try {
        await client.mkdir('/$directory/images');
      } catch (e) {
        // 目录可能已存在，忽略错误
        print("images目录可能已存在: $e");
      }

      // 上传本地图片
      final updatedBackupData =
          await _uploadLocalImages(client, backupData, directory);

      // 构建备份文件名（使用当前时间戳）
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename = "backup_$timestamp.json";
      // 先将文件写到本地，再上传
      // 获取应用缓存目录
      final cacheDir = await getTemporaryDirectory();
      final localFilePath = '${cacheDir.path}/$filename';
      // 将备份数据写入本地文件
      final file = io.File(localFilePath);
      await file.writeAsBytes(utf8.encode(jsonEncode(updatedBackupData)));

      // 上传备份文件
      CancelToken c = CancelToken();
      await client.writeFromFile(file.absolute.path, '/$directory/$filename',
          onProgress: (c, t) {
        print(c / t);
      }, cancelToken: c);

      return Result.success(filename);
    } catch (e) {
      return Result.failure("备份到WebDAV失败: $e");
    }
  }

  /// 保存到本地

  // 准备备份数据
  Future<Map<String, dynamic>> _prepareBackupData() async {
    // 获取需要备份的所有数据
    // 这里应该实现实际的数据收集逻辑
    final syncData = await repo.getSyncData();
    return {
      "timestamp": DateTime.now().toIso8601String(),
      "version": "1.0",
      // 添加其他需要备份的数据
      "syncData": syncData
    };
  }

  // 上传本地图片到WebDAV服务器
  Future<Map<String, dynamic>> _uploadLocalImages(
      Client client, Map<String, dynamic> backupData, String directory) async {
    // 深拷贝备份数据，以便于修改
    final updatedBackupData = Map<String, dynamic>.from(backupData);
    final syncData = updatedBackupData["syncData"];

    // 如果syncData是SyncData类型，则直接处理其assets和categories
    if (syncData is! Map<String, dynamic>) {
      final syncDataObj = syncData as SyncData;

      // 处理资产中的本地图片
      for (final asset in syncDataObj.assets) {
        final imagePaths = await asset.getImagePaths();
        for (final imagePath in imagePaths) {
          await _uploadImageToWebdav(client, imagePath, directory);
        }
      }

      // 处理分类中的本地图片
      for (final category in syncDataObj.categories) {
        final imagePaths = await category.getImagePaths();
        for (final imagePath in imagePaths) {
          await _uploadImageToWebdav(client, imagePath, directory);
        }
      }
    } else {
      // 如果是Map<String, dynamic>类型，则按原来的方式处理
      final syncDataMap = syncData;

      // 处理资产中的本地图片
      if (syncDataMap.containsKey("assets")) {
        final assets = List<Map<String, dynamic>>.from(syncDataMap["assets"]);
        for (final assetMap in assets) {
          if (assetMap.containsKey("icon") && assetMap["icon"] != null) {
            final icon = assetMap["icon"] as Map<String, dynamic>;
            if (icon["type"] == "local" && icon["value"] != null) {
              final localPath = icon["value"] as String;
              // 检查文件是否存在并上传
              final file = io.File(localPath);
              if (await file.exists()) {
                await _uploadImageToWebdav(client, localPath, directory);
              } else {
                // 尝试在当前应用目录中查找文件
                final appDir = await IoUtils.getAppDir();
                final filename = localPath.split('/').last;
                final normalizedPath = '${appDir.path}/images/$filename';
                final normalizedFile = io.File(normalizedPath);
                if (await normalizedFile.exists()) {
                  await _uploadImageToWebdav(client, normalizedPath, directory);
                }
              }
            }
          }
        }
      }

      // 处理分类中的本地图片
      if (syncDataMap.containsKey("categories")) {
        final categories =
            List<Map<String, dynamic>>.from(syncDataMap["categories"]);
        for (final categoryMap in categories) {
          if (categoryMap.containsKey("icon") && categoryMap["icon"] != null) {
            final icon = categoryMap["icon"] as Map<String, dynamic>;
            if (icon["type"] == "local" && icon["value"] != null) {
              final localPath = icon["value"] as String;
              // 检查文件是否存在并上传
              final file = io.File(localPath);
              if (await file.exists()) {
                await _uploadImageToWebdav(client, localPath, directory);
              } else {
                // 尝试在当前应用目录中查找文件
                final appDir = await IoUtils.getAppDir();
                final filename = localPath.split('/').last;
                final normalizedPath = '${appDir.path}/images/$filename';
                final normalizedFile = io.File(normalizedPath);
                if (await normalizedFile.exists()) {
                  await _uploadImageToWebdav(client, normalizedPath, directory);
                }
              }
            }
          }
        }
      }
    }

    // 返回原始数据，因为我们没有修改它
    return updatedBackupData;
  }

  // 上传单个图片到WebDAV服务器
  Future<String?> _uploadImageToWebdav(
      Client client, String localPath, String directory) async {
    try {
      final file = io.File(localPath);
      if (!await file.exists()) {
        print("本地图片不存在: $localPath");
        return null;
      }

      // 使用原始文件名作为标识符
      final originalFilename = localPath.split('/').last;
      final remotePath = "/$directory/images/$originalFilename";

      // 检查远程文件是否已存在
      try {
        await client.readProps(remotePath);
        print("远程图片已存在，跳过上传: $remotePath");
        return remotePath;
      } catch (e) {
        // 文件不存在，继续上传
        print("远程图片不存在，开始上传: $remotePath");
      }

      // 上传文件
      CancelToken c = CancelToken();
      await client.writeFromFile(file.absolute.path, remotePath,
          onProgress: (c, t) {
        print("上传图片进度: ${c / t}");
      }, cancelToken: c);

      print("图片上传成功: $remotePath");
      return remotePath;
    } catch (e) {
      print("上传图片失败: $e");
      return null;
    }
  }

  // 下载单个图片文件
  Future<ImageDownloadResult> _downloadSingleImage(
      Client client, ImageDownloadInfo info) async {
    try {
      // 检查本地文件是否已存在
      if (await info.localFileExists()) {
        print("本地图片已存在，跳过下载: ${info.localPath}");
        return ImageDownloadResult.skipped(info);
      }

      // 确保本地目录存在
      final localFile = io.File(info.localPath);
      final localDir = localFile.parent;
      if (!await localDir.exists()) {
        await localDir.create(recursive: true);
      }

      // 下载图片
      await client.read2File(info.remotePath, info.localPath);
      print("下载图片成功: ${info.remotePath} -> ${info.localPath}");

      return ImageDownloadResult.success(info);
    } catch (e) {
      print("下载图片失败: ${info.remotePath}, $e");
      return ImageDownloadResult.failed(info, e.toString());
    }
  }

  // 下载相关的图片文件（新版本，使用实体）
  Future<BackupData> _downloadImagesWithEntities(
      Client client, BackupData backupData, String directory) async {
    try {
      // 获取所有需要下载的图片信息
      final downloadInfos = await backupData.getImageDownloadInfos(directory);

      if (downloadInfos.isEmpty) {
        print("没有需要下载的图片");
        return backupData;
      }

      print("开始下载 ${downloadInfos.length} 个图片文件");

      // 下载所有图片
      final List<ImageDownloadResult> results = [];
      for (final info in downloadInfos) {
        final result = await _downloadSingleImage(client, info);
        results.add(result);
      }

      // 统计下载结果
      final successful = results.where((r) => r.success).length;
      final skipped = results.where((r) => r.skipped).length;
      final failed = results.where((r) => !r.success).length;

      print("图片下载完成: 成功 $successful, 跳过 $skipped, 失败 $failed");

      // 更新备份数据中的图片路径
      return backupData.updateImagePaths(downloadInfos);
    } catch (e) {
      print("下载图片过程中发生错误: $e");
      return backupData;
    }
  }

  // 恢复WebDAV备份
  Future<Result<void>> recoverWebdav(String fileId) async {
    try {
      // 获取WebDAV配置
      final userRepo = getIt.get<UserRepo>();
      final config = await userRepo.getWebdavConfig();

      if (config == null ||
          config.serverUrl == null ||
          config.serverUrl!.isEmpty) {
        return Result.failure("请先配置WebDAV服务");
      }

      final serverUrl = config.serverUrl!;
      final username = config.username!;
      final password = config.password!;
      final directory = config.directory ?? "recollect";

      // 创建WebDAV客户端
      var client = newClient(
        serverUrl,
        user: username,
        password: password,
        debug: kDebugMode,
      );

      // 下载备份文件
      final remotePath = "/$directory/$fileId";

      // 获取应用缓存目录
      final cacheDir = await IoUtils.getAppDir();
      final localFilePath = '${cacheDir.path}/$fileId';
      final localFile = io.File(localFilePath);

      // 下载文件
      try {
        await client.read2File(remotePath, localFilePath, onProgress: (c, t) {
          print("下载备份文件进度: ${c / t}");
        });
      } catch (e) {
        return Result.failure("下载备份文件失败: $e");
      }

      // 读取并解析备份文件
      final fileContent = await localFile.readAsString();
      final backupDataMap = json.decode(fileContent) as Map<String, dynamic>;

      // 使用实体解析备份数据
      BackupData backupData;
      try {
        backupData = BackupData.fromMap(backupDataMap);
      } catch (e) {
        return Result.failure("备份文件格式错误: $e");
      }

      // 下载相关的图片文件（使用新的实体方法）
      final updatedBackupData =
          await _downloadImagesWithEntities(client, backupData, directory);

      // 将数据应用到本地存储
      final assetLocalDataSource = getIt.get<AssetLocalDataSource>();
      final categoryLocalDataSource = getIt.get<CategoryLocalDataSource>();

      await assetLocalDataSource.saveAssets(updatedBackupData.syncData.assets);
      await categoryLocalDataSource
          .saveCategories(updatedBackupData.syncData.categories);

      return Result.success(null);
    } catch (e) {
      return Result.failure("恢复WebDAV备份失败: $e");
    }
  }
}
