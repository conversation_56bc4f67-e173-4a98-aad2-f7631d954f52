import 'package:injectable/injectable.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/core/api_client.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/icons/response/category_response.dart';

@lazySingleton
class IconsDataSource {
  final ApiClient _client;

  IconsDataSource(this._client);

  Future<Result<CategoryResponse?>> getIcons() async {
    final response = await _client.get(ApiConstants.getIcons,
        fromJson: CategoryResponse.fromJson);
    return response;
  }
}
