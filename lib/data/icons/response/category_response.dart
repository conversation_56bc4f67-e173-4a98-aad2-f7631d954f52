import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/models/icon/category_entity.dart';
import 'package:tasks/models/icon/icon_entity.dart';
import 'package:tasks/utils/string_ext.dart';

part 'category_response.g.dart';

@JsonSerializable()
class CategoryResponse {
  List<CategoryItem?>? categories;

  CategoryResponse({this.categories}) {}

  factory CategoryResponse.fromJson(Map<String, dynamic> json) =>
      _$CategoryResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryResponseToJson(this);

  // 网络实体转业务实体
  static List<CategoryIconEntity> getCategories(CategoryResponse? resp) {
    return resp?.categories
            ?.map((i) => CategoryItem.asEntity(i))
            .where((i) => i != null)
            .cast<CategoryIconEntity>()
            .toList() ??
        List.empty();
  }
}

@JsonSerializable()
class CategoryItem {
  String? id;
  int? index;
  String? name;
  String? path;
  List<IconItem?>? icons;

  CategoryItem({
    this.id,
    this.name,
    this.path,
    this.icons,
    this.index,
  });

  factory CategoryItem.fromJson(Map<String, dynamic> json) =>
      _$CategoryItemFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryItemToJson(this);

  static CategoryIconEntity? asEntity(CategoryItem? item) {
    if (item == null) {
      return null;
    }
    return CategoryIconEntity(
        id: item.id ?? "",
        index: item.index ?? 0,
        name: item.name ?? "",
        icons: item.icons
                ?.map(IconItem.asEntity)
                .where((i) => i != null && !i.id.isNullOrEmpty)
                .cast<IconEntity>()
                .toList() ??
            List.empty());
  }
}

@JsonSerializable()
class IconItem {
  String? id;
  String? name;
  String? url;
  int? index;
  bool? requiredVip;
  @JsonKey(fromJson: IconType.fromJson, toJson: IconType.toJson)
  IconType iconType;

  IconItem(
      {this.id,
      this.name,
      this.url,
      this.index,
      this.requiredVip,
      this.iconType = IconType.remote});

  factory IconItem.fromJson(Map<String, dynamic> json) =>
      _$IconItemFromJson(json);

  Map<String, dynamic> toJson() => _$IconItemToJson(this);

  static IconEntity? asEntity(IconItem? item) {
    if (item == null) {
      return null;
    }
    final icon = AppIcon(type: item.iconType, value: item.url, key: item.id);
    return IconEntity(
        id: item.id ?? "",
        name: item.name ?? "",
        url: item.url ?? "",
        icon: icon,
        index: item.index ?? 0,
        requiredVip: item.requiredVip ?? true);
  }
}
