import 'package:injectable/injectable.dart';
import 'package:tasks/data/icons/icon_local_data_source.dart';
import 'package:tasks/data/icons/icons_data_source.dart';
import 'package:tasks/data/icons/response/category_response.dart';
import 'package:tasks/models/icon/category_entity.dart';
import 'package:tasks/models/icon/icon_entity.dart';

@lazySingleton
class IconsRepository {
  final IconsDataSource _dataSource;
  final IconLocalDataSource _localDataSource;

  IconsRepository(this._dataSource, this._localDataSource);

  Future<List<CategoryIconEntity>> getIcons() async {
    // 无缓存时获取一次接口
    if (_localDataSource.noCache()) {
      Future(() => _fetchAndSaveIcons());
    }
    // 先从缓存中获取
    final cacheData = await _localDataSource.getCategoryIcons();
    if (cacheData != null) {
      return CategoryResponse.getCategories(cacheData);
    }
    // 缓存中不存在才从网络获取
    final icons = await _fetchAndSaveIcons();
    if (icons.isNotEmpty) {
      return icons;
    }
    // 请求失败时强制走缓存
    return CategoryResponse.getCategories(
        (await _localDataSource.getCategoryIcons(forceCache: true)));
  }

  Future<List<CategoryIconEntity>> _fetchAndSaveIcons() async {
    // 否则从缓存中获取
    final resp = await _dataSource.getIcons();
    if (resp.isSuccess) {
      // 缓存图标分类数据
      _localDataSource.saveCategoryIcons(resp.data);
      return CategoryResponse.getCategories(resp.data);
    }
    return [];
  }

  /// 获取自定义分类
  Future<CategoryIconEntity> getCustomIcons() async {
    final cacheData = await _localDataSource.getCustomIcons();
    List<IconEntity> icons = cacheData
        .map((i) => IconItem.asEntity(i))
        .where((i) => i != null)
        .cast<IconEntity>()
        .toList();
    return CategoryIconEntity(
      id: "-1",
      name: "自定义",
      icons: [...icons],
      index: 1,
    );
  }

  Future<void> addIcon(IconItem icon) async {
    // 先从缓存中获取，缓存中不存在才从网络获取
    _localDataSource.saveCustomIcons(icon);
  }

  Future<void> deleteIcon(String id) async {
    // 先从缓存中获取，缓存中不存在才从网络获取
    _localDataSource.removeCustomIcons(id);
  }
}
