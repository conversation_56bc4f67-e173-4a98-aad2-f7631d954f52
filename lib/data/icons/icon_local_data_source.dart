import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/data/icons/response/category_response.dart';
import 'package:tasks/generated/assets.gen.dart';

@lazySingleton
class IconLocalDataSource {
  final SharedPreferences _preferences;

  IconLocalDataSource(this._preferences);

  static const String _keyTime = "localIconsDays";

  static const String _key = 'localIcons';

  static const String _keyCustom = "icon_local_custom_icon";

  /// 判断未获取过图标数据
  bool noCache() {
    try {
      return _preferences.getString(_key) == null;
    } catch (e) {
      return true;
    }
  }

  Future<CategoryResponse?> getCategoryIcons({bool forceCache = false}) async {
    try {
      // 取出当前缓存数据
      int? storedTime = _preferences.getInt(_keyTime);
      String? assetsJson = _preferences.getString(_key);
      storedTime ??= DateTime.now().millisecondsSinceEpoch;
      assetsJson ??= await rootBundle.loadString(
        Assets.cache.icon,
      );
      // 判断缓存是否失效
      final DateTime now = DateTime.now();
      final DateTime storedDateTime =
          DateTime.fromMillisecondsSinceEpoch(storedTime);
      final Duration difference = now.difference(storedDateTime);
      // 缓存时间如果大于24h，则认为缓存已过期
      if (difference.inSeconds > 60 * 60 * 24 && !forceCache) {
        return null; // 缓存失效
      }
      final dynamic decoded = json.decode(assetsJson);
      return CategoryResponse.fromJson(decoded);
    } catch (e) {
      print("获取缓存失败：$e");
      return null;
    }
  }

  Future<void> saveCategoryIcons(CategoryResponse? icons) async {
    try {
      final String encoded = json.encode(icons?.toJson());
      await _preferences.setString(_key, encoded);
      await _preferences.setInt(
          _keyTime, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      print("缓存失败：$e");
    }
  }

  /// 获取当前用户自定义图标
  Future<List<IconItem>> getCustomIcons() async {
    final String? jsonString = _preferences.getString(_keyCustom);

    if (jsonString == null) {
      return []; // 直接返回空列表
    }

    final List<dynamic> decoded = json.decode(jsonString);
    return decoded.map((item) => IconItem.fromJson(item)).toList();
  }

  /// 保存用户自定义图标
  Future<void> saveCustomIcons(IconItem icon) async {
    final List<IconItem> assets = await getCustomIcons();
    assets.insert(0, icon);
    final String encoded = json.encode(assets.map((e) => e.toJson()).toList());
    await _preferences.setString(_keyCustom, encoded);
  }

  Future<void> removeCustomIcons(String id) async {
    final List<IconItem> assets = await getCustomIcons();
    assets.removeWhere((element) => element.id == id);
    final String encoded = json.encode(assets.map((e) => e.toJson()).toList());
    await _preferences.setString(_keyCustom, encoded);
  }
}
