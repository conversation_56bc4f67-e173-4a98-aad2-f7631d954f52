import 'package:injectable/injectable.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/core/api_client.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/models/message/message_info.dart';
import 'package:tasks/models/message/message_list_resp.dart';

@lazySingleton
class MessageRepo {
  final ApiClient _client;

  MessageRepo(this._client);

  /// 获取消息列表
  Future<Result<List<MessageInfo>>> getMessageList() async {
    final result = await _client.get(ApiConstants.messageList,
        fromJson: MessageListResp.fromJson);
    return result.map((r) =>
        r?.lists?.where((i) => i != null).cast<MessageInfo>().toList() ??
        List.empty());
  }
}
