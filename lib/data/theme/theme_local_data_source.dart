import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/models/custom_theme/custom_theme.dart';
import 'package:tasks/models/custom_theme/old_custom_theme.dart';

@lazySingleton
class ThemeLocalDataSource {
  final SharedPreferences _preferences;

  ThemeLocalDataSource(this._preferences);

  static const String _themePrefsKey = 'theme_mode';
  static const String _keySeedColor = 'theme_seedColor';
  static const String _keyEnableDynamicColor = 'theme_enableDynamicColor';
  final _keyCustomTheme = 'theme_customTheme';
  final _keyCustomThemeCardTheme = 'theme_customThemeCardTheme';

  /// 获取缓存的主题
  Future<CustomTheme> readTheme() async {
    try {
      final jsonString = _preferences.getString(_keyCustomThemeCardTheme);
      if (jsonString == null) {
        final newDefaultTheme = await defaultTheme();
        writeTheme(newDefaultTheme);
        return newDefaultTheme;
      }

      // Convert the string to a Map
      final Map<String, dynamic> jsonMap = json.decode(jsonString);

      // Convert the Map to a CustomTheme object
      return CustomTheme.fromJson(jsonMap);
    } catch (e) {
      // If encountering an error, return null
      return defaultTheme();
    }
  }

  Future<OldCustomTheme?> readOldTheme() async {
    try {
      final jsonString = _preferences.getString(_keyCustomTheme);
      if (jsonString == null) {
        return null;
      }

      // Convert the string to a Map
      final Map<String, dynamic> jsonMap = json.decode(jsonString);

      // Convert the Map to a CustomTheme object
      return OldCustomTheme.fromJson(jsonMap);
    } catch (e) {
      // If encountering an error, return null
      return null;
    }
  }

  Future<CustomTheme> defaultTheme() async {
    CustomTheme theme = CustomTheme.classicCustomTheme();
    final savedThemeMode = _preferences.getString(_themePrefsKey);
    // 之前缓存的主题
    final themeMode = ThemeMode.values.firstWhere(
      (mode) => mode.toString() == savedThemeMode,
      orElse: () => ThemeMode.system,
    );
    // 是否开启动态色
    final enableDynamicColor =
        _preferences.getBool(_keyEnableDynamicColor) ?? false;
    // 动态色获取
    final seedColorInt = _preferences.getInt(_keySeedColor);
    final seedColor = seedColorInt != null ? Color(seedColorInt) : Colors.red;
    // 老的缓存的主题
    final oldTheme = await readOldTheme();
    if (oldTheme == null) {
      return theme.copyWith(
          themeMode: themeMode,
          enableDynamicColor: enableDynamicColor,
          seedColor: seedColor);
    }
    // 从老背景中复制出原始背景数据
    return theme.copyWith(
      themeMode: themeMode,
      enableDynamicColor: enableDynamicColor,
      seedColor: seedColor,
      topCard: theme.topCard.copyWith(
        startColor: oldTheme.homeTopInfoGradientStart,
        endColor: oldTheme.homeTopInfoGradientEnd,
      ),
      normalCard: theme.normalCard.copyWith(
        startColor: oldTheme.normalCardGradientStart,
        endColor: oldTheme.normalCardGradientEnd,
      ),
      favoriteCard: theme.favoriteCard.copyWith(
        startColor: oldTheme.favoriteCardGradientStart,
        endColor: oldTheme.favoriteCardGradientEnd,
      ),
      retiredCard: theme.retiredCard.copyWith(
        startColor: oldTheme.retiredCardGradientStart,
        endColor: oldTheme.retiredCardGradientEnd,
      ),
    );
  }

  /// 写入缓存的主题
  Future<void> writeTheme(CustomTheme theme) async {
    final jsonString = json.encode(theme.toJson());

    // Write the theme to SharedPreferences
    await _preferences.setString(_keyCustomThemeCardTheme, jsonString);
  }
}
