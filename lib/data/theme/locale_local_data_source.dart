import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/models/custom_theme/custom_locate.dart';

@lazySingleton
class LocaleLocalDataSource {
  final SharedPreferences _preferences;

  LocaleLocalDataSource(this._preferences);

  static const String _Key = 'cur_locale';

  Future<CustomLocate?> getCustomLocale() async {
    try {
      final jsonString = _preferences.getString(_Key);

      if (jsonString == null) {
        return null;
      }
      // Convert the string to a Map
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      final customLocale = CustomLocate.fromJson(jsonMap);
      if (customLocale.valid()) {
        return customLocale;
      }
      // Convert the Map to a CustomTheme object
      return null;
    } catch (e) {
      // If encountering an error, return null
      return null;
    }
  }

  /// 获取缓存的语言
  Future<Locale?> getLocale() async {
    try {
      CustomLocate? customLocate = await getCustomLocale();
      if (customLocate == null) return null;

      // Convert the Map to a CustomTheme object
      return Locale(customLocate.languageCode!, customLocate.countryCode);
    } catch (e) {
      // If encountering an error, return null
      return null;
    }
  }

  /// 写入缓存的语言
  Future<void> writeLocale(Locale? locale) async {
    try {
      if (locale == null) {
        await _preferences.remove(_Key);
        return;
      }

      CustomLocate customLocale =
          (await getCustomLocale()) ?? CustomLocate.instance();
      final jsonString = json.encode(customLocale.copyWith(
          languageCode: locale.languageCode, countryCode: locale.countryCode));
      // Write the theme to SharedPreferences
      await _preferences.setString(_Key, jsonString);
    } catch (e) {
      // doNothing
    }
  }
}
