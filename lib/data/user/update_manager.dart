import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/injection.dart';

class UpdateManager {
  static const String _keyTodayUpdate = "today_update_tips"; // 存储键
  static const String _keyLastPromptDate = "last_prompt_date"; // 上次提示日期

  // 检查是否需要提示更新
  static Future<bool> shouldShowUpdatePrompt() async {
    final prefs = getIt<SharedPreferences>();

    // 检查是否已经点击过“稍后提醒”
    final bool todayUpdateTips = prefs.getBool(_keyTodayUpdate) ?? false;
    if (todayUpdateTips) {
      // 检查当前日期与上次提示日期
      final String? lastPromptDate = prefs.getString(_keyLastPromptDate);
      if (lastPromptDate != null) {
        final DateTime lastDate = DateTime.parse(lastPromptDate);
        final DateTime now = DateTime.now();

        // 如果是新的一天，重置提示状态
        if (lastDate.year != now.year ||
            lastDate.month != now.month ||
            lastDate.day != now.day) {
          await prefs.setBool(_keyTodayUpdate, false); // 重置提示状态
          return true; // 需要提示更新
        }
      }
      return false; // 当天已经提示过，不再提示
    }

    return true; // 需要提示更新
  }

  // 用户点击“稍后提醒”
  static Future<void> setRemindLater() async {
    final prefs = getIt<SharedPreferences>();

    // 记录当天不再提示
    await prefs.setBool(_keyTodayUpdate, true);

    // 记录当前时间为上次提示日期
    final String now = DateTime.now().toIso8601String();
    await prefs.setString(_keyLastPromptDate, now);
  }
}
