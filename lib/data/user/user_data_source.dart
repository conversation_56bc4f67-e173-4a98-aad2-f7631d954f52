import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/data/user/models/login_user_cache_info.dart';
import 'package:tasks/data/user/models/user_model.dart';
import 'package:tasks/models/webdav/webdav_config_entity.dart';

@lazySingleton
class UserDataSource {
  final SharedPreferences _preferences;

  UserDataSource(this._preferences);

  final String _key_token = 'access_token';

  final String _key_userinfo = "user_info";

  final String _key_cache_login_info = "cache_login_info";

  // WebDAV配置相关的键
  final String _key_webdav_config = "webdav_config";

  // 读取Token
  String? getToken() {
    return _preferences.getString(_key_token);
  }

  Future<void> saveToken(String token) async {
    await _preferences.setString(_key_token, token);
  }

  Future<void> clearToken() async {
    await _preferences.remove(_key_token);
  }

  Future<void> saveUserInfo(UserModel model) async {
    await _preferences.setString(_key_userinfo, json.encode(model));
  }

  UserModel? getUserInfo() {
    try {
      final userJson = _preferences.getString(_key_userinfo);
      final dynamic jsonObj = json.decode(userJson ?? "{}");
      final userModel = UserModel.fromJson(jsonObj);
      return userModel;
    } catch (e) {
      return null;
    }
  }

  /// 清空用户缓存数据
  Future<void> clearLoginInfo() async {
    await _preferences.remove(_key_token);
    await _preferences.remove(_key_userinfo);
  }

  /// 保存登录信息
  Future<void> saveLoginInfo(
      {String? username, String? password, String? email}) async {
    try {
      await _preferences.setString(
          _key_cache_login_info,
          json.encode(LoginUserCacheInfo(
            username: username,
            password: password,
          )));
    } catch (e) {
      // doNothing
    }
  }

  /// 获取当前登录用户信息
  LoginUserCacheInfo? getLoginInfo() {
    try {
      final userJson = _preferences.getString(_key_cache_login_info);
      final dynamic jsonObj = json.decode(userJson ?? "{}");
      final model = LoginUserCacheInfo.fromJson(jsonObj);
      return model;
    } catch (e) {
      return null;
    }
  }

  // 保存WebDAV配置到本地
  Future<void> saveWebdavConfig(WebdavConfigEntity config) async {
    try {
      final jsonString = json.encode(config.toJson());
      await _preferences.setString(_key_webdav_config, jsonString);
    } catch (e) {
      // 处理异常
      print("保存WebDAV配置失败: $e");
    }
  }

  // 从本地获取WebDAV配置
  WebdavConfigEntity? getWebdavConfig() {
    try {
      final jsonString = _preferences.getString(_key_webdav_config);
      if (jsonString == null || jsonString.isEmpty) {
        return null;
      }

      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      return WebdavConfigEntity.fromJson(jsonMap);
    } catch (e) {
      print("获取WebDAV配置失败: $e");
      return null;
    }
  }

  // 清除WebDAV配置
  Future<void> clearWebdavConfig() async {
    await _preferences.remove(_key_webdav_config);
  }
}
