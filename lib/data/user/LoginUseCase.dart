// 处理登录请求，并在登录成功后保存Token，同时请求个人信息

import 'package:injectable/injectable.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/user/models/login_model.dart';
import 'package:tasks/data/user/user_data_source.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/data/user/wechat_repo.dart';
import 'package:tasks/utils/string_ext.dart';

@lazySingleton
class LoginUseCase {
  final UserDataSource _source;
  final UserRepo repo;
  final WechatRepo _wechatRepo;

  LoginUseCase(this._source, this.repo, this._wechatRepo);

  Future<Result<void>> login(String username, String password) async {
    // 发起登录请求
    final response =
        await repo.login(LoginRequest(password: password, username: username));

    if (response.isSuccess) {
      final token = response.data?.token;
      // 保存 token 到 SharedPreferences
      await _source.saveToken(token ?? "");
      await repo.getUserInfo();
    }
    return response;
  }

  /// 邮箱登录
  Future<Result<void>> emailLogin(String email, String verifyCode) async {
    // 发起登录请求
    final response = await repo.emailLogin(email, verifyCode);

    if (response.isSuccess) {
      final token = response.data?.token;
      if (token.isNullOrEmpty) {
        return response;
      }
      // 保存 token 到 SharedPreferences
      await _source.saveToken(token ?? "");
      await repo.getUserInfo();
    }
    return response;
  }

  Future<Result<WechatLoginResponse?>> wechatLogin(String code) async {
    // 发起登录请求
    final response = await _wechatRepo.wechatLogin(code);

    if (response.isSuccess) {
      final token = response.data?.token;
      if (token.isNullOrEmpty) {
        return response;
      }
      // 保存 token 到 SharedPreferences
      await _source.saveToken(token ?? "");
      await repo.getUserInfo();
    }
    return response;
  }

  /// 使用微信注册账号
  Future<Result<WechatLoginResponse?>> wechatRegister(String code) async {
    // 发起登录请求
    final response = await _wechatRepo.wechatRegister(code);

    if (response.isSuccess) {
      final token = response.data?.token;
      if (token.isNullOrEmpty) {
        return response;
      }
      // 保存 token 到 SharedPreferences
      await _source.saveToken(token ?? "");
      await repo.getUserInfo();
    }
    return response;
  }
}
