import 'package:injectable/injectable.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/core/api_client.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/user/models/login_model.dart';
import 'package:tasks/models/user/wechat_action.dart';

@lazySingleton
class WechatRepo {
  final ApiClient _client;

  WechatRepo(this._client);

  // 用户使用微信登录
  Future<Result<WechatLoginResponse?>> wechatLogin(String code) async {
    final result = await _client.post(ApiConstants.wechatLogin,
        data: {"code": code, "state": WechatAction.login.name},
        fromJson: WechatLoginResponse.fromJson);
    return result;
  }

  // 用户使用微信注册
  Future<Result<WechatLoginResponse?>> wechatRegister(String code) async {
    final result = await _client.post(ApiConstants.wechatRegister,
        data: {"code": code, "state": WechatAction.register.name},
        fromJson: WechatLoginResponse.fromJson);
    return result;
  }

  Future<Result<void>> bindWechat(
    String code,
  ) async {
    final result = await _client.post(ApiConstants.wechatBind,
        data: {"code": code, "state": WechatAction.wechat_bind.name});
    return result;
  }

  Future<Result<void>> unbindWechat(
    String code,
  ) async {
    final result = await _client.post(ApiConstants.wechatUnbind,
        data: {"code": code, "state": WechatAction.wechat_unbind.name});
    return result;
  }
}
