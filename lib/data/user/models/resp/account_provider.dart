import 'package:json_annotation/json_annotation.dart';

part 'account_provider.g.dart';

enum OAuthProvider {
  Google,
  Wechat,
}

@JsonSerializable()
class OAuthProviderEntity {
  final OAuthProvider? provider; // 第三方登录提供商（可空）
  final String? providerUserId; // 第三方用户ID（可空）
  final String? accessToken; // 访问令牌（可空）

  OAuthProviderEntity({
    this.provider,
    this.providerUserId,
    this.accessToken,
  });

  factory OAuthProviderEntity.fromJson(Map<String, dynamic> json) =>
        _$OAuthProviderEntityFromJson(json);

  Map<String, dynamic> toJson() => _$OAuthProviderEntityToJson(this);
}

@JsonSerializable()
class AccountProviderResponse {
  final List<OAuthProviderEntity>? list; // 第三方登录提供商列表（可空）

  AccountProviderResponse({this.list});

  factory AccountProviderResponse.fromJson(Map<String, dynamic> json) =>
        _$AccountProviderResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AccountProviderResponseToJson(this);
}
