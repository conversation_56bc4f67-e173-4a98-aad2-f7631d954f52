import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/utils/string_ext.dart';

part 'login_model.g.dart';

@JsonSerializable()
class LoginRequest {
  LoginRequest({
    required this.password,
    required this.username,
  });

  String password;
  String username;

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class LoginResponse {
  final String? token;

  LoginResponse({this.token});

  factory LoginResponse.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}

@JsonSerializable()
class WechatLoginResponse {
  final String? token;
  final String? wechatId;

  WechatLoginResponse({this.token, this.wechatId});

  // 微信登录成功
  get loginSuccess => !token.isNullOrEmpty && !wechatId.isNullOrEmpty;

  // 微信登录成功，但是需要绑定用户
  get bindWechat => token.isNullOrEmpty && !wechatId.isNullOrEmpty;

  factory WechatLoginResponse.fromJson(Map<String, dynamic> json) =>
      _$WechatLoginResponseFromJson(json);

  Map<String, dynamic> toJson() => _$WechatLoginResponseToJson(this);
}
