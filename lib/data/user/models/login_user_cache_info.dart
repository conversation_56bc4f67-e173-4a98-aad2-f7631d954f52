import 'package:json_annotation/json_annotation.dart';

part 'login_user_cache_info.g.dart';

@JsonSerializable()
class LoginUserCacheInfo {
  String? username;
  String? password;
  String? email;

  LoginUserCacheInfo({this.username, this.password, this.email});

  factory LoginUserCacheInfo.fromJson(Map<String, dynamic> json) =>
      _$LoginUserCacheInfoFromJson(json);

  Map<String, dynamic> toJson() => _$LoginUserCacheInfoToJson(this);
}
