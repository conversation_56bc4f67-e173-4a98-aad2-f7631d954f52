import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/core/api_client.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/user/models/exchange_cdkey.dart';
import 'package:tasks/data/user/models/login_model.dart';
import 'package:tasks/data/user/models/register.dart';
import 'package:tasks/data/user/models/resp/account_provider.dart';
import 'package:tasks/data/user/models/user_model.dart';
import 'package:tasks/data/user/update_manager.dart';
import 'package:tasks/data/user/user_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/app_update_info.dart';
import 'package:tasks/models/user/email_verify_code_type.dart';
import 'package:tasks/models/webdav/webdav_config_entity.dart';
import 'package:webdav_client/webdav_client.dart';

@lazySingleton
class UserRepo {
  final UserDataSource _source;
  final ApiClient _client;

  UserRepo(this._source, this._client);

  // 是否是会员
  static bool isVip() {
    UserDataSource source = getIt.get();
    final userInfo = source.getUserInfo();
    return userInfo?.paidVip == true || userInfo?.freeVip == true;
  }

  static bool isPaidVip() {
    UserDataSource source = getIt.get();
    final userInfo = source.getUserInfo();
    return userInfo?.paidVip == true;
  }

  static bool isLogin() {
    UserDataSource source = getIt.get();
    return source.getToken() != null;
  }

  Future<Result<AppUpdateInfo?>> checkUpdate(String? source) async {
    // 首页处理多次启动
    if (source == "home") {
      if (!(await UpdateManager.shouldShowUpdatePrompt())) {
        return Result.success(null);
      }
    }
    final result = await _client.get(ApiConstants.checkUpdate,
        fromJson: AppUpdateInfo.fromJson);
    return result;
  }

  // 用户注册
  Future<Result<void>> register(RegisterParams params) async {
    final result = await _client.post(
      ApiConstants.register,
      data: params,
    );
    // 注册成功时缓存登录信息
    if (result.isSuccess) {
      _source.saveLoginInfo(
          username: params.username, password: params.password);
    }
    return result;
  }

  // 用户登录
  Future<Result<LoginResponse?>> login(LoginRequest params) async {
    final result = await _client.post(ApiConstants.login,
        data: params, fromJson: LoginResponse.fromJson);
    // 注册成功时缓存登录信息
    if (result.isSuccess) {
      _source.saveLoginInfo(
          username: params.username, password: params.password);
    }
    return result;
  }

  void logout() {
    _source.clearLoginInfo();
  }

  /// 使用邮箱登录
  Future<Result<LoginResponse?>> emailLogin(
      String email, String verifyCode) async {
    final result = await _client.post(ApiConstants.emailLogin,
        data: {"email": email, "verifyCode": verifyCode},
        fromJson: LoginResponse.fromJson);
    // 注册成功时缓存登录信息
    if (result.isSuccess) {
      _source.saveLoginInfo(email: email);
    }
    return result;
  }

  // 从本地获取用户信息
  UserModel? getUserInfoFromCache() {
    final token = _source.getToken();
    if (token == null || token.isEmpty) {
      return null;
    }
    return _source.getUserInfo();
  }

  // 获取用户信息
  Future<Result<UserModel?>> getUserInfo() async {
    final token = _source.getToken();
    if (token == null || token.isEmpty) {
      return Result.failure("未登录");
    }
    final result =
        await _client.get(ApiConstants.userInfo, fromJson: UserModel.fromJson);
    // 缓存用户信息
    if (result.isSuccess && result.data != null) {
      _source.saveUserInfo(result.data!);
    }
    return result;
  }

  // 兑换会员
  Future<Result<void>> exchangeVip(String cdkey) async {
    final result = await _client.post(ApiConstants.exchangeVip,
        data: ExchangeCdkeyParams(cdkey: cdkey));
    return result;
  }

  // 修改昵称
  Future<Result<void>> editNickName(String name) async {
    final result =
        await _client.post(ApiConstants.editNickName, data: {"nickname": name});
    return result;
  }

  /// 修改用户名
  Future<Result<void>> editUsername(String username) async {
    final result = await _client
        .post("user/v1/updateUsername", data: {"username": username});
    return result;
  }

  // 获取绑定邮箱验证码
  Future<Result<void>> getEmailVerifyCode(
      String value, EmailVerifyCodeType type) async {
    final result = await _client.post(ApiConstants.sendEmailCode,
        data: {"email": value, "type": type.name});
    return result;
  }

  // 绑定邮箱
  Future<Result<void>> bindEmail(String email, String verifyCode) async {
    final result = await _client.post(ApiConstants.bindEmail,
        data: {"email": email, "verifyCode": verifyCode, "type": "bindEmail"});
    return result;
  }

  // 解绑邮箱
  Future<Result<void>> unBindEmail(String email, String verifyCode) async {
    final result = await _client.post(ApiConstants.unbindEmail, data: {
      "email": email,
      "verifyCode": verifyCode,
      "type": "unbindEmail"
    });
    return result;
  }

  // 获取三方登录列表
  Future<Result<List<OAuthProviderEntity>>> getThirdList() async {
    final result = await _client.get(ApiConstants.getThirdList,
        fromJson: AccountProviderResponse.fromJson);
    return result.map((r) => r?.list ?? List.empty());
  }

  /// 注销账号
  Future<Result<void>> logoutAccount() async {
    final result = await _client.post("user/v1/deleteAccount");
    return result;
  }

  // 获取WebDAV配置
  Future<WebdavConfigEntity?> getWebdavConfig() async {
    return _source.getWebdavConfig();
  }

  // 保存WebDAV配置
  Future<Result<bool>> saveWebdavConfig({
    required String serverUrl,
    required String username,
    required String password,
    String directory = "recollect",
  }) async {
    try {
      final config = WebdavConfigEntity(
        serverUrl: serverUrl,
        username: username,
        password: password,
        directory: directory,
        isEnabled: true,
      );

      await _source.saveWebdavConfig(config);
      return Result.success(true);
    } catch (e) {
      return Result.failure("保存WebDAV配置失败: $e");
    }
  }

  // 测试WebDAV连接
  Future<Result<bool>> testWebdavConnection({
    required String serverUrl,
    required String username,
    required String password,
  }) async {
    try {
      // 简单的URL格式验证
      if (!serverUrl.startsWith('http://') &&
          !serverUrl.startsWith('https://')) {
        return Result.failure("服务器地址必须以http://或https://开头");
      }

      // 创建WebDAV客户端
      var client = newClient(
        serverUrl,
        user: username,
        password: password,
        debug: kDebugMode,
      );

      try {
        // 等待ping方法的异步执行
        await client.ping();
        return Result.success(true);
      } catch (e) {
        // 根据错误类型返回具体的错误信息
        String errorMessage = _parseWebdavError(e);
        return Result.failure(errorMessage);
      }
    } catch (e) {
      return Result.failure("WebDAV连接测试失败: $e");
    }
  }

  // 解析WebDAV错误信息
  String _parseWebdavError(dynamic error) {
    String errorStr = error.toString().toLowerCase();

    if (errorStr.contains('401') || errorStr.contains('unauthorized')) {
      return "认证失败，请检查用户名和密码";
    } else if (errorStr.contains('404') || errorStr.contains('not found')) {
      return "服务器地址不正确或WebDAV服务不可用";
    } else if (errorStr.contains('403') || errorStr.contains('forbidden')) {
      return "访问被拒绝，请检查账户权限";
    } else if (errorStr.contains('timeout') ||
        errorStr.contains('connection')) {
      return "连接超时，请检查网络连接和服务器地址";
    } else if (errorStr.contains('certificate') ||
        errorStr.contains('ssl') ||
        errorStr.contains('tls')) {
      return "SSL证书验证失败，请检查服务器证书";
    } else {
      return "WebDAV连接测试失败: $error";
    }
  }

  // 清除WebDAV配置
  Future<void> clearWebdavConfig() async {
    await _source.clearWebdavConfig();
  }
}
