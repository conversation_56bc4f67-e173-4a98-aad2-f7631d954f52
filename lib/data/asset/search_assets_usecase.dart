import 'package:injectable/injectable.dart';
import 'package:tasks/data/asset/asset_local_data_source.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/models/category.dart';

/// 资产搜索用例
/// 支持：
/// 1. 忽略大小写搜索
/// 2. 搜索分类名称
@lazySingleton
class SearchAssetsUseCase {
  final AssetLocalDataSource _assetDataSource;
  final CategoryLocalDataSource _categoryDataSource;

  SearchAssetsUseCase(this._assetDataSource, this._categoryDataSource);

  /// 搜索资产
  Future<List<AssetCardEntity>> search(String text) async {
    if (text.trim().isEmpty) {
      return [];
    }

    final searchText = text.trim().toLowerCase();
    final allAssets = _assetDataSource.getAssetsSync();
    final allCategories = _categoryDataSource.getCategoriesSync();

    // 生成搜索关键词
    final searchKeywords = _generateSearchKeywords(searchText);

    // 过滤资产
    final filtered = allAssets.where((asset) {
      return _matchesAsset(asset, allCategories, searchKeywords);
    }).toList();

    // 转换为卡片实体
    return filtered.map((asset) => _assetToCard(asset, allCategories)).toList();
  }

  /// 生成搜索关键词
  List<String> _generateSearchKeywords(String searchText) {
    // 只返回原始搜索词
    return [searchText];
  }

  /// 检查资产是否匹配搜索关键词
  bool _matchesAsset(
      Asset asset, List<CategoryEntity> categories, List<String> keywords) {
    // 获取分类名称
    final category =
        categories.where((c) => c.id == asset.categoryId).firstOrNull;
    final categoryName = category?.name ?? '';

    // 构建搜索目标文本
    final searchTargets = [
      asset.name.toLowerCase(),
      (asset.remark ?? '').toLowerCase(),
      categoryName.toLowerCase(),
    ];

    // 检查是否有任何关键词匹配任何搜索目标
    return keywords.any((keyword) {
      return searchTargets.any((target) => target.contains(keyword));
    });
  }

  /// 将资产转换为卡片实体
  AssetCardEntity _assetToCard(Asset asset, List<CategoryEntity> categories) {
    final categoryId = asset.categoryId;
    var categoryEntity =
        categories.where((i) => i.id == categoryId).firstOrNull;
    return AssetCardEntity(
      asset: asset,
      id: asset.id,
      name: asset.name,
      categoryName: categoryEntity?.name,
      price: asset.price,
      resalePrice: asset.resalePrice,
      isFavorite: asset.isFavorite,
      isInService: asset.isInService,
      icon: asset.icon,
    );
  }

  /// 释放资源
  void dispose() {
    // 当前没有需要释放的资源
  }
}
