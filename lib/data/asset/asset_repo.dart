import 'dart:ui';

import 'package:injectable/injectable.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/asset/asset_local_data_source.dart';
import 'package:tasks/providers/user_preferences_store.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/data/config/config_repo.dart';
import 'package:tasks/data/recycle_bin/recycle_bin_local_data_source.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/models/asset/asset_sort.dart';
import 'package:tasks/models/asset/asset_statistics.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/models/asset_filter.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/user_preferences/user_preferences_entity.dart';
import 'package:tasks/platform/home_widget_manager.dart';
import 'package:tasks/utils/string_ext.dart';

@lazySingleton
class AssetRepo {
  final AssetLocalDataSource _datasource;
  final UserPreferencesStore _userPreferencesStore;
  final CategoryLocalDataSource _categoryLocalDataSource;
  final ConfigRepo repo;
  final HomeWidgetManager _homeWidgetManager = HomeWidgetManager();

  AssetRepo(this._datasource, this._userPreferencesStore,
      this._categoryLocalDataSource, this.repo);

  /// 判断是否可以添加资产
  Future<bool> checkAddAvailable() async {
    final assets = await _datasource.getAssets();
    final assetLength = assets.length;
    final isVip = UserRepo.isVip();
    final config = repo.getConfig();
    if (isVip) {
      return true;
    }
    if (!UserRepo.isLogin()) {
      return assetLength < config.maxAssetNumUnLogin;
    }
    return assetLength < config.maxAssetNumLogin;
  }

  /// 获取是否提示缓存数据
  bool checkNeedCacheTips() {
    final lastCacheTime = _datasource.getCacheAssetsTips();
    _datasource.saveCacheAssetsTips();
    final cur = DateTime.now().millisecondsSinceEpoch;
    return cur - lastCacheTime > 1000 * 60 * 60 * 24;
  }

  // 计算资产总价格
  double measureAmount(String price, List<ExtraFeesEntity> extraFees) {
    double extraAmount = extraFees.fold(0.0, (sum, item) {
      return sum + item.amount;
    });
    return (price.toDoubleOrNull ?? 0) + extraAmount;
  }

  AssetStatistics getAssetStatistics(
      {required List<AssetCardEntity> assets,
      required UserPreferencesEntity preferencesEntity}) {
    // 获取用户当前计算配置
    bool excludeRetired = (preferencesEntity).excludeRetired;
    double totalPrice = measureTotalPrice(assets, excludeRetired);
    double dailyPrice = measureTotalDailyPrice(assets, excludeRetired);
    final maxDailyPrice = measureMaxDailyPrice(assets, excludeRetired);
    final minDailyPrice = measureMinDailyPrice(assets, excludeRetired);

    final entity = AssetStatistics(
        totalPrice: totalPrice,
        totalAssets: assets.length,
        dailyPrice: dailyPrice,
        maxDailyPrice: maxDailyPrice,
        minDailyPrice: minDailyPrice);
    return entity;
  }

  double measureTotalPrice(List<AssetCardEntity> assets, bool excludeRetired) {
    double total = assets.fold(0, (sum, asset) {
      if (excludeRetired && !asset.isInService) {
        return sum;
      }
      return sum + asset.measureAmount;
    });
    return total;
  }

  double measureTotalDailyPrice(
      List<AssetCardEntity> assets, bool excludeRetired) {
    double total =
        assets.where((i) => i.asset.dailyPriceMethod).fold(0, (sum, asset) {
      if (excludeRetired && !asset.isInService) {
        return sum;
      }
      return sum + (asset.dailyPrice);
    });
    return total;
  }

  double measureMaxDailyPrice(
      List<AssetCardEntity> assets, bool excludeRetired) {
    final validAssets = assets.where((asset) {
      if (excludeRetired && !asset.isInService) return false;
      return asset.asset.dailyPriceMethod && asset.dailyPrice > 0;
    });

    if (validAssets.isEmpty) return 0.0;
    final maxPrice = validAssets
        .map((asset) => asset.dailyPrice)
        .reduce((a, b) => a > b ? a : b);

    return maxPrice.toString().toDoubleOrZero;
  }

  double measureMinDailyPrice(
      List<AssetCardEntity> assets, bool excludeRetired) {
    final validAssets = assets.where((asset) {
      if (excludeRetired && !asset.isInService) return false;
      return asset.asset.dailyPriceMethod && asset.dailyPrice > 0;
    });

    if (validAssets.isEmpty) return 0.0;

    final minPrice = validAssets
        .map((asset) => asset.dailyPrice)
        .reduce((a, b) => a < b ? a : b);
    return minPrice.toString().toDoubleOrZero;
  }

  List<AssetCardEntity> getFilterList(
    List<Asset> assets,
    List<CategoryEntity> categories, {
    AssetFilter? filter,
  }) {
    var filtered = List<Asset>.from(assets);
    // 筛选状态
    if (filter?.status != null) {
      // 应用状态筛选
      switch (filter!.status!) {
        case AssetStatus.inService:
          filtered = filtered.where((asset) => asset.isInService).toList();
          break;
        case AssetStatus.retired:
          filtered = filtered.where((asset) => !asset.isInService).toList();
          break;
        case AssetStatus.favorite:
          filtered = filtered.where((asset) => asset.isFavorite).toList();
          break;
      }
    }
    // 筛选分类
    if (filter?.categoryId != null) {
      // 应用分类筛选
      filtered = filtered
          .where((asset) => asset.categoryId == filter?.categoryId)
          .toList();
    }
    // 筛选计价类型
    if (filter?.priceMethod != null) {
      filtered = filtered
          .where((asset) => asset.priceMethod == filter?.priceMethod)
          .toList();
    }
    var cardList = filtered.map((i) => assetToCard(i, categories)).toList();
    // 如果是默认排序或反序，不做排序处理
    if (filter?.sortType == AssetSortType.defaultSort) {
      // doNothing
    } else if (filter?.sortType == AssetSortType.defaultSortRevert) {
      cardList = cardList.reversed.toList();
    } else {
      // 排序
      cardList.sort((i1, i2) {
        return i1.compare(filter?.sortType ?? AssetSortType.createTime, i2);
      });
    }
    return cardList;
  }

  /// 获取收藏的资产列表
  List<AssetCardEntity> getStaredList(
      List<Asset> assets, List<CategoryEntity> categories) {
    var filtered = List<Asset>.from(assets);
    var cardList = filtered
        .where((i) => i.isFavorite)
        .map((i) => assetToCard(i, categories))
        .toList();
    return cardList;
  }

  AssetCardEntity assetToCard(Asset asset, List<CategoryEntity> categories) {
    final categoryId = asset.categoryId;
    var categoryEntity =
        categories.where((i) => i.id == categoryId).firstOrNull;
    return AssetCardEntity(
        asset: asset,
        id: asset.id,
        name: asset.name,
        categoryName: categoryEntity?.name,
        price: asset.price,
        resalePrice: asset.resalePrice,
        isFavorite: asset.isFavorite,
        isInService: asset.isInService,
        icon: asset.icon);
  }

  // 交换资产位置
  Future<void> swap(Iterable<String> ids) async {
    // 插入到新位置
    final curAssets = await _datasource.getAssets();
    // ids的顺序为新顺序
    final assets = ids.map((id) {
      final asset = curAssets.firstWhere((i) => i.id == id);
      return asset;
    }).toList();
    await _datasource.saveAssets(assets);
  }

  Future<Result<void>> createAsset(Asset asset) async {
    try {
      await _datasource.addAsset(asset);

      return Result.success(1);
    } catch (e) {
      return Result.failure(e.toString());
    }
  }

  // 不存在则创建，存在则更新
  Future<Result<void>> updateAssetInfo(Asset asset) async {
    final exist = await _datasource.getAssetById(asset.id) != null;
    if (!exist) {
      return Result.failure("更新失败");
    }
    // 存在则更新
    await _datasource.updateAsset(asset);

    return Result.success(1);
  }

  /// 更新资产（用于状态管理层调用）
  Future<void> updateAsset(Asset asset) async {
    await _datasource.updateAsset(asset);
  }

  Future<Result<void>> deleteAsset(String id) async {
    try {
      // 获取要删除的资产
      final asset = await _datasource.getAssetById(id);
      if (asset == null) {
        return Result.failure('Asset not found');
      }

      // 将资产移到回收站
      final recycleBinDataSource = getIt.get<RecycleBinLocalDataSource>();
      await recycleBinDataSource.addToRecycleBin(asset);

      // 从资产列表中删除
      await _datasource.deleteAsset(id);
      return Result.success(1);
    } catch (e) {
      return Result.failure(e.toString());
    }
  }

  Future<AssetStatistics> getAssetStatisticsFromCache() async {
    final assets = await _datasource.getAssets();
    final categories = await _categoryLocalDataSource.getCategories();
    final assetCards =
        assets.map((asset) => assetToCard(asset, categories)).toList();
    final preferencesEntity = _userPreferencesStore.preferencesEntity;
    final statistics = getAssetStatistics(
        assets: assetCards, preferencesEntity: preferencesEntity);
    return statistics;
  }

  /// 刷新小组件（仅在用户点击小组件或进入首页时调用）
  Future<void> refreshWidget() async {
    try {
      final statistics = await getAssetStatisticsFromCache();

      // 获取用户偏好设置
      final userPrefs = _userPreferencesStore.preferencesEntity;

      // 获取系统主题状态
      final isDarkMode = _getSystemTheme();

      // 使用HomeWidgetManager更新小组件
      print('AssetRepo: Updating widget via HomeWidgetManager...');
      await _homeWidgetManager.updateWidgetData(
        statistics,
        currencySymbol: userPrefs.currencySymbol,
        currencyCode: userPrefs.currencyCode,
        appName: '极简记物',
        isDarkMode: isDarkMode,
      );
      print('totalPrice: ${statistics.totalPrice}');
      print('AssetRepo: Widget refresh completed successfully');
    } catch (e) {
      print('AssetRepo: Failed to refresh widget: $e');
      print('AssetRepo: Error stack trace: ${StackTrace.current}');
    }
  }

  /// 获取系统主题状态
  bool _getSystemTheme() {
    try {
      // 获取系统亮度
      final brightness = PlatformDispatcher.instance.platformBrightness;
      return brightness == Brightness.dark;
    } catch (e) {
      print('AssetRepo: Failed to get system theme: $e');
      return false; // 默认返回浅色主题
    }
  }
}
