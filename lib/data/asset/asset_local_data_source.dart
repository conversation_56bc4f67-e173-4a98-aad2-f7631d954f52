import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/data/cache/cache_lds.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/asset_filter.dart';

@lazySingleton
class AssetLocalDataSource {
  final SharedPreferences _preferences;
  final CacheLds _cacheLds;

  AssetLocalDataSource(this._preferences, this._cacheLds);

  static const String _key = 'assets';

  static const String _key_filter = "assets_filter";

  // 定期提示缓存时间
  static const String _key_cache_assets = "cache_assets_tips";

  // 小组件统计数据缓存
  static const String _key_widget_statistics = "widget_statistics";

  List<Asset> getAssetsSync() {
    final String? assetsJson = _preferences.getString(_key);
    if (assetsJson == null) return [];
    try {
      final List<dynamic> decoded = json.decode(assetsJson);
      return decoded.map((item) => Asset.fromJson(item)).toList();
    } catch (e) {
      return [];
    }
  }

  /// 获取初始数据，如果本地没有数据，则取本地默认初始数据
  Future<List<Asset>> getAssets() async {
    final String? assetsJson = _preferences.getString(_key);
    try {
      if (assetsJson == null) {
        final syncData = await _cacheLds.getAssetSyncData();
        final assets = syncData?.assets ?? [];
        await saveAssets(assets);
        return assets;
      }
      final List<dynamic> decoded = json.decode(assetsJson);
      return decoded.map((item) => Asset.fromJson(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Future<void> saveAssets(List<Asset> assets) async {
    final String encoded = json.encode(assets.map((e) => e.toJson()).toList());
    await _preferences.setString(_key, encoded);
  }

  Future<void> addAsset(Asset asset) async {
    final assets = await getAssets();
    assets.insert(0, asset);
    await saveAssets(assets);
  }

  Future<void> updateAsset(Asset asset) async {
    final assets = await getAssets();
    final index = assets.indexWhere((a) => a.id == asset.id);
    if (index != -1) {
      assets[index] = asset;
      await saveAssets(assets);
    }
  }

  Future<void> deleteAsset(String id) async {
    final assets = await getAssets();
    assets.removeWhere((asset) => asset.id == id);
    await saveAssets(assets);
  }

  Future<Asset?> getAssetById(String id) async {
    final assets = await getAssets();
    try {
      return assets.firstWhere((asset) => asset.id == id);
    } catch (e) {
      return null;
    }
  }

  // 保存筛选数据
  Future<void> saveAssetFilter(AssetFilter filter) async {
    try {
      final encoded = json.encode(filter.toJson());
      await _preferences.setString(_key_filter, encoded);
    } catch (e) {
      print("保存筛选数据出错：$e");
    }
  }

  // 获取筛选数据
  AssetFilter getAssetFilter() {
    try {
      final encoded = _preferences.getString(_key_filter);
      if (encoded != null) {
        final decoded = json.decode(encoded);
        return AssetFilter.fromJson(decoded);
      } else {
        return AssetFilter();
      }
    } catch (e) {
      print("获取筛选数据出错：$e");
      return AssetFilter();
    }
  }

  // 缓存提示时间
  Future<void> saveCacheAssetsTips() async {
    try {
      await _preferences.setInt(
          _key_cache_assets, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      // doNothing
    }
  }

  int getCacheAssetsTips() {
    return _preferences.getInt(_key_cache_assets) ?? 0;
  }

  // 保存小组件统计数据到SharedPreferences
  Future<void> saveWidgetStatistics(Map<String, dynamic> statistics) async {
    try {
      final encoded = json.encode(statistics);
      await _preferences.setString(_key_widget_statistics, encoded);
      print("AssetLocalDataSource: Widget statistics saved: $encoded");
    } catch (e) {
      print("AssetLocalDataSource: 保存小组件统计数据出错：$e");
    }
  }

  // 获取小组件统计数据
  Map<String, dynamic>? getWidgetStatistics() {
    try {
      final encoded = _preferences.getString(_key_widget_statistics);
      if (encoded != null) {
        return json.decode(encoded) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      print("获取小组件统计数据出错：$e");
      return null;
    }
  }
}
