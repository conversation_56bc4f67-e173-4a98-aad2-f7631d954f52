import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/core/api_client.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/asset/asset_local_data_source.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/data/buy_channel/buy_channel_local_data_source.dart';
import 'package:tasks/data/storage_location/storage_location_local_data_source.dart';
import 'package:tasks/models/cache/cache_history_resp.dart';
import 'package:tasks/models/sync_data.dart';

@lazySingleton
class DataSyncRepo {
  final AssetLocalDataSource _assetLocalDataSource;
  final CategoryLocalDataSource _categoryLocalDataSource;
  final BuyChannelLocalDataSource _buyChannelLocalDataSource;
  final StorageLocationLocalDataSource _storageLocationLocalDataSource;
  final ApiClient _client;

  DataSyncRepo(
      this._assetLocalDataSource,
      this._categoryLocalDataSource,
      this._buyChannelLocalDataSource,
      this._storageLocationLocalDataSource,
      this._client);

  // 返回所有待缓存的数据
  Future<SyncData> getSyncData() async {
    final assets = await _assetLocalDataSource.getAssets();
    final categories = await _categoryLocalDataSource.getCategories();
    final buyChannels = await _buyChannelLocalDataSource.getBuyChannels();
    final storageLocations =
        await _storageLocationLocalDataSource.getStorageLocations();
    return SyncData(
        assets: assets,
        categories: categories,
        buyChannels: buyChannels,
        storageLocations: storageLocations);
  }

  // 用户缓存数据
  Future<Result<void>> saveCache() async {
    final syncData = await getSyncData();
    final result = await _client.post(
      ApiConstants.cache,
      data: {"value": json.encode(syncData.toJson()), "type": "Network"},
    );
    return result;
  }

  /// 记录用户缓存Webdav
  Future<Result<void>> saveWebdavCache(String fileId) async {
    final result = await _client.post(
      ApiConstants.cache,
      data: {"fileId": fileId, "type": "Webdav"},
    );
    return result;
  }

  // 获取缓存列表
  Future<Result<List<CacheHistoryEntity>>> getCacheList() async {
    final result = await _client.get(ApiConstants.fetchCacheList,
        fromJson: CacheHistoryResp.fromJson);
    return result.map((r) => r?.list ?? List.empty());
  }

  // 启用缓存数据
  Future<Result<void>> fetchCache(String id) async {
    final result = await _client.post(ApiConstants.fetchCache,
        data: {"id": id}, fromJson: SyncData.fromJson);
    result.onSuccess((r) {
      final assets = r?.assets;
      final categories = r?.categories;
      final buyChannels = r?.buyChannels;
      final storageLocations = r?.storageLocations;
      if (assets != null) {
        _assetLocalDataSource.saveAssets(assets);
      }
      if (categories != null) {
        _categoryLocalDataSource.saveCategories(categories);
      }
      if (buyChannels != null) {
        _buyChannelLocalDataSource.saveBuyChannels(buyChannels);
      }
      if (storageLocations != null) {
        _storageLocationLocalDataSource.saveStorageLocations(storageLocations);
      }
    });
    return result;
  }

  // 删除缓存记录及数据
  Future<Result<void>> deleteCache(String id) async {
    final result =
        await _client.post(ApiConstants.deleteCache, data: {"id": id});
    return result;
  }
}
