import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/pay/order_remote_source.dart';
import 'package:tobias/tobias.dart';

@lazySingleton
class OrderRepo {
  final OrderRemoteSource _dataSource;

  OrderRepo(this._dataSource);

  Future<Result<void>> createOrder() async {
    final tobias = <PERSON>();
    // if (!(await tobias.isAliPayInstalled)) {
    //   return Result.failure("未安装支付宝，支付失败");
    // }
    final result = await _dataSource.createOrder();
    final orderStr = result.data;
    final orderId = result.data?.orderId;
    final payResult =
        await tobias.pay(result.data?.orderStr ?? "", evn: AliPayEvn.online);
    print('result: ${jsonEncode(payResult)}');
    final tradeNo =
        jsonDecode(payResult["result"])["alipay_trade_app_pay_response"]
            ["trade_no"];
    return Result.success("刷新就完事");
  }
}
