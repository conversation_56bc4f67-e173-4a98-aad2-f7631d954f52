import 'package:injectable/injectable.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/core/api_client.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/pay/model/create_order_resp.dart';

@lazySingleton
class OrderRemoteSource {
  final ApiClient _client;

  OrderRemoteSource(this._client);

  /// 调用接口创建订单
  Future<Result<CreateOrderResp?>> createOrder() async {
    final response = await _client.post(ApiConstants.createOrder,
        fromJson: CreateOrderResp.fromJson);
    if (response.isFailure) {
      return Result.failure(response.msg);
    }
    return Result.success(response.data);
  }

  /// 调用接口创建订单
  Future<Result<String?>> surePay(String orderId, String tradeNo) async {
    final response = await _client.post(ApiConstants.surePay,
        data: {"orderId": orderId, "tradeNo": tradeNo},
        fromJson: CreateOrderResp.fromJson);
    if (response.isFailure) {
      return Result.failure(response.msg);
    }
    return Result.success(response.data?.orderStr);
  }
}
