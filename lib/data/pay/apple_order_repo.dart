import 'package:injectable/injectable.dart';
import 'package:tasks/core/api_client.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/pay/model/create_order_resp.dart';
import 'package:tasks/data/pay/model/product_list_resp.dart';
import 'package:tasks/models/order/product_item.dart';

@lazySingleton
class AppleOrderRepo {
  final ApiClient client;

  AppleOrderRepo(this.client);

  /// 获取商品列表
  Future<List<ProductItem>> getProductList() async {
    final result = await client.get("apple/pay/v2/productList",
        fromJson: ProductListResp.fromJson);
    final list = result.data?.asEntity() ?? [];
    return list;
  }

  /// 创建订单
  Future<Result<CreateOrderResp?>> createOrder(String productId) async {
    final response = await client.post("apple/pay/v1/createOrder",
        data: {
          "productId": productId,
        },
        fromJson: CreateOrderResp.fromJson);
    return response;
  }

  /// 手动调用接口恢复用户购买
  Future<Result<void>> restorePurchase(String serverData) async {
    final response = await client.post("apple/pay/v1/userVerifyOrder", data: {
      "signedPayload": serverData,
    });
    return response;
  }
}
