import 'package:tasks/models/order/product_item.dart';

class ProductListResp {
  List<ProductListItem?>? list;

  ProductListResp({
    this.list,
  });

  factory ProductListResp.fromJson(Map<String, dynamic> json) {
    return ProductListResp(
      list: (json['list'] as List<dynamic>?)
          ?.map((item) => item != null ? ProductListItem.fromJson(item) : null)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'list': list?.map((item) => item?.toJson()).toList(),
    };
  }

  List<ProductItem> asEntity() {
    return list
            ?.map((e) => ProductItem(
                  id: e?.productId ?? "",
                  name: e?.productName ?? "",
                  discountPrice: e?.discountPrice ?? "",
                  originalPrice: e?.originalPrice ?? "",
                ))
            .toList() ??
        [];
  }
}

class ProductListItem {
  String? productId;
  String? productName;
  String? discountPrice;
  String? originalPrice;
  String? productDesc;

  ProductListItem({
    this.productId,
    this.productName,
    this.discountPrice,
    this.originalPrice,
    this.productDesc,
  });

  factory ProductListItem.fromJson(Map<String, dynamic> json) {
    return ProductListItem(
      productId: json['productId'],
      productName: json['productName'],
      discountPrice: json['discountPrice'],
      originalPrice: json['originalPrice'],
      productDesc: json['productDesc'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'discountPrice': discountPrice,
      'originalPrice': originalPrice,
      'productDesc': productDesc,
    };
  }
}
