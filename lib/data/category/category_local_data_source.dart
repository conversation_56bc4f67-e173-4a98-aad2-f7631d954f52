import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/data/cache/cache_lds.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/category_icon.dart';

@lazySingleton
class CategoryLocalDataSource {
  final SharedPreferences _preferences;
  final CacheLds _cacheLds;

  CategoryLocalDataSource(this._preferences, this._cacheLds);

  static const String _key = 'categories';

  List<CategoryEntity> getCategoriesSync() {
    try {
      final String? categoriesJson = _preferences.getString(_key);
      if (categoriesJson == null) {
        return List.empty();
      }
      final List<dynamic> decoded = json.decode(categoriesJson);
      return decoded.map((i) => CategoryEntity.fromJson(i)).toList();
    } catch (e) {
      return List.empty();
    }
  }

  /// 获取分类数据,如果本地没有数据，则取本地默认初始数据
  Future<List<CategoryEntity>> getCategories() async {
    final String? jsonString = _preferences.getString(_key);
    try {
      if (jsonString == null) {
        final syncData = await _cacheLds.getAssetSyncData();
        final categories = syncData?.categories ?? [];
        await saveCategories(categories);
        return categories;
      }

      final List<dynamic> decoded = json.decode(jsonString);
      return decoded.map((i) => CategoryEntity.fromJson(i)).toList();
    } catch (e) {
      return List.empty();
    }
  }

  Future<void> saveCategories(List<CategoryEntity> categories) async {
    final String encoded =
        json.encode(categories.map((e) => e.toJson()).toList());
    await _preferences.setString(_key, encoded);
  }

  Future<void> addCategory(String name, AppIcon icon) async {
    final categories = await getCategories();
    final newCategory = CategoryEntity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      order: categories.length,
      icon: icon,
    );
    categories.add(newCategory);
    await saveCategories(categories);
  }

  Future<void> updateCategory(String id, String name, AppIcon icon) async {
    final categories = await getCategories();
    final index = categories.indexWhere((category) => category.id == id);
    if (index != -1) {
      categories[index] = CategoryEntity(
        id: id,
        name: name,
        order: categories[index].order,
        icon: icon,
      );
      await saveCategories(categories);
    }
  }

  // 通过id获取分类数据
  CategoryEntity? getCategory(String? id) {
    if (id == null) {
      return null;
    }
    final categories = getCategoriesSync();
    try {
      return categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> deleteCategory(String id) async {
    final categories = await getCategories();
    categories.removeWhere((category) => category.id == id);
    // 重新排序
    for (int i = 0; i < categories.length; i++) {
      categories[i].order = i;
    }
    await saveCategories(categories);
  }

  Future<void> reorderCategories(int oldIndex, int newIndex) async {
    final categories = await getCategories();
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final CategoryEntity item = categories.removeAt(oldIndex);
    categories.insert(newIndex, item);

    // 更新所有项的order
    for (int i = 0; i < categories.length; i++) {
      categories[i].order = i;
    }

    await saveCategories(categories);
  }
}
