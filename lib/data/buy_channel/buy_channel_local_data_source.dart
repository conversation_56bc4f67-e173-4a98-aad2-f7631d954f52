import 'dart:convert';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/models/buy_channel.dart';
import 'package:tasks/data/cache/cache_lds.dart';

@lazySingleton
class BuyChannelLocalDataSource {
  final SharedPreferences _preferences;
  final CacheLds _cacheLds;
  static const String _key = 'buy_channels';

  BuyChannelLocalDataSource(this._preferences, this._cacheLds);

  /// 获取购买渠道数据，如果本地没有数据，则从assets中读取初始数据
  Future<List<BuyChannelEntity>> getBuyChannels() async {
    final String? jsonString = _preferences.getString(_key);
    try {
      if (jsonString == null) {
        // 从assets中获取初始数据
        final syncData = await _cacheLds.getAssetSyncData();
        final channels = syncData?.buyChannels ?? _getDefaultChannels();
        await saveBuyChannels(channels);
        return channels;
      }

      final List<dynamic> decoded = json.decode(jsonString);
      return decoded.map((i) => BuyChannelEntity.fromJson(i)).toList();
    } catch (e) {
      // 如果解析失败，从assets中获取初始数据
      final syncData = await _cacheLds.getAssetSyncData();
      final channels = syncData?.buyChannels ?? _getDefaultChannels();
      await saveBuyChannels(channels);
      return channels;
    }
  }

  /// 保存购买渠道数据
  Future<void> saveBuyChannels(List<BuyChannelEntity> channels) async {
    final String encoded =
        json.encode(channels.map((e) => e.toJson()).toList());
    await _preferences.setString(_key, encoded);
  }

  /// 添加购买渠道
  Future<void> addBuyChannel(String name) async {
    final channels = await getBuyChannels();
    final newChannel = BuyChannelEntity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      order: channels.length,
    );
    channels.add(newChannel);
    await saveBuyChannels(channels);
  }

  /// 更新购买渠道
  Future<void> updateBuyChannel(String id, String name) async {
    final channels = await getBuyChannels();
    final index = channels.indexWhere((channel) => channel.id == id);
    if (index != -1) {
      channels[index] = BuyChannelEntity(
        id: id,
        name: name,
        order: channels[index].order,
      );
      await saveBuyChannels(channels);
    }
  }

  /// 删除购买渠道
  Future<void> deleteBuyChannel(String id) async {
    final channels = await getBuyChannels();
    channels.removeWhere((channel) => channel.id == id);
    // 重新排序
    for (int i = 0; i < channels.length; i++) {
      channels[i].order = i;
    }
    await saveBuyChannels(channels);
  }

  /// 根据ID获取购买渠道
  BuyChannelEntity? getBuyChannel(String id) {
    // 这里需要同步获取，所以先从缓存中获取
    final String? jsonString = _preferences.getString(_key);
    if (jsonString == null) return null;

    try {
      final List<dynamic> decoded = json.decode(jsonString);
      final channels =
          decoded.map((i) => BuyChannelEntity.fromJson(i)).toList();
      return channels.firstWhere((channel) => channel.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 根据名称获取购买渠道（用于处理历史数据）
  BuyChannelEntity? getBuyChannelByName(String name) {
    final String? jsonString = _preferences.getString(_key);
    if (jsonString == null) return null;

    try {
      final List<dynamic> decoded = json.decode(jsonString);
      final channels =
          decoded.map((i) => BuyChannelEntity.fromJson(i)).toList();
      return channels.where((channel) => channel.name == name).firstOrNull;
    } catch (e) {
      return null;
    }
  }

  /// 获取默认购买渠道数据
  List<BuyChannelEntity> _getDefaultChannels() {
    return [
      BuyChannelEntity(
        id: '1',
        name: '京东',
        order: 0,
      ),
      BuyChannelEntity(
        id: '2',
        name: '淘宝',
        order: 1,
      ),
      BuyChannelEntity(
        id: '3',
        name: '拼多多',
        order: 2,
      ),
      BuyChannelEntity(
        id: '4',
        name: '天猫',
        order: 3,
      ),
      BuyChannelEntity(
        id: '5',
        name: '苏宁易购',
        order: 4,
      ),
    ];
  }
}
