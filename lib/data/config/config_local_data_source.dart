import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tasks/data/config/asset_config_net_model.dart';

@lazySingleton
class ConfigLocalDataSource {
  String _key_config = "k_config";
  final SharedPreferences preferences;

  ConfigLocalDataSource(this.preferences);

  /// 本地取配置数据
  AssetConfigNetModel? getConfig() {
    try {
      String? config = preferences.getString(_key_config);
      if (config != null) {
        return AssetConfigNetModel.fromJson(jsonDecode(config));
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> saveConfig(AssetConfigNetModel config) async {
    try {
      String configJson = jsonEncode(config.toJson());
      await preferences.setString(_key_config, configJson);
    } catch (e) {
      // doNothing
    }
  }
}
