import 'package:injectable/injectable.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/core/api_client.dart';
import 'package:tasks/data/config/asset_config_net_model.dart';
import 'package:tasks/data/config/config_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/config/config_entity.dart';

@lazySingleton
class ConfigRepo {
  final ConfigLocalDataSource source;
  final ApiClient _client;

  ConfigRepo(this.source, this._client);

  // 获取配置数据
  ConfigEntity getConfig() {
    // 否则从本地取配置数据
    return (source.getConfig() ?? AssetConfigNetModel.defaultConfig())
        .asEntity();
  }

  // 同步配置数据
  Future<ConfigEntity?> syncConfig() async {
    try {
      final result = await _client.get(ApiConstants.config,
          fromJson: (json) => AssetConfigNetModel.fromJson(json));
      final netModel = result.data;
      if (result.isSuccess && netModel != null) {
        await source.saveConfig(netModel);
      }
      return getConfig();
    } catch (e) {
      //doNothing
      return null;
    }
  }

  /// 全局获取配置信息
  static ConfigEntity getAllConfig() {
    return getIt.get<ConfigRepo>().getConfig();
  }
}
