import 'package:json_annotation/json_annotation.dart';
import 'package:tasks/models/config/config_entity.dart';
import 'package:tasks/utils/platform.dart';

part 'asset_config_net_model.g.dart';

@JsonSerializable()
class AssetConfigNetModel {
  // 未登录时最大资产数量
  final int? maxAssetNumUnLogin;

  // 登录时最大资产数量
  final int? maxAssetNumLogin;
  final String? shareUrl;

  // 配置捐款链接
  final String? donateUrl;
  final String? feedbackEmail;
  final String? defaultIcon;
  final String? moneyFormat;
  final String? payMeUrl;
  final String? privacyPolicy;
  final String? servicePolicy;
  final String? redBookLink;
  final String? devWechat;
  final String? discountAmount;
  final String? originAmount;

  // 活动页面配置
  final bool? showMembershipActivity;

  AssetConfigNetModel({
    this.maxAssetNumUnLogin,
    this.maxAssetNumLogin,
    this.shareUrl,
    this.donateUrl,
    this.feedbackEmail,
    this.defaultIcon,
    this.moneyFormat,
    this.payMeUrl,
    this.privacyPolicy,
    this.servicePolicy,
    this.redBookLink,
    this.devWechat,
    this.discountAmount,
    this.originAmount,
    this.showMembershipActivity,
  });

  factory AssetConfigNetModel.fromJson(Map<String, dynamic> json) =>
      _$AssetConfigNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AssetConfigNetModelToJson(this);

  ConfigEntity asEntity() {
    return ConfigEntity(
      maxAssetNumUnLogin: maxAssetNumUnLogin ?? 10,
      maxAssetNumLogin: maxAssetNumLogin ?? 20,
      donateUrl: donateUrl ?? "",
      shareUrl: shareUrl ?? "",
      feedbackEmail: feedbackEmail ?? "",
      defaultIcon: defaultIcon ?? "",
      moneyFormat: moneyFormat ?? "",
      payMeUrl: payMeUrl ?? "",
      privacyPolicy: privacyPolicy ?? "",
      servicePolicy: servicePolicy ?? "",
      redBookLink: redBookLink ?? "",
      devWechat: devWechat ?? defaultConfig().devWechat!,
      discountAmount: discountAmount ?? defaultConfig().discountAmount!,
      originAmount: originAmount ?? defaultConfig().originAmount!,
      showMembershipActivity:
          showMembershipActivity ?? defaultConfig().showMembershipActivity!,
    );
  }

  static AssetConfigNetModel defaultConfig() {
    return AssetConfigNetModel(
      maxAssetNumUnLogin: 10,
      maxAssetNumLogin: 20,
      shareUrl: "https://me.pajiatu.com/",
      donateUrl: "https://afdian.com/a/guiwu",
      feedbackEmail: "<EMAIL>",
      defaultIcon:
          "https://i.nihaocq.com/guiwu/digital/67a8d339a8b850f05cf1c62b.png",
      moneyFormat: "¥",
      payMeUrl: "https://afdian.com/a/guiwu",
      privacyPolicy: "https://doc.pajiatu.com/recollect/privacyPolicy",
      servicePolicy: "https://doc.pajiatu.com/recollect/servicePolicy",
      redBookLink:
          "https://www.xiaohongshu.com/user/profile/5c7b7a1b000000001100d1c3",
      devWechat: "hello_mr3",
      discountAmount: "¥12",
      originAmount: "¥24",
      showMembershipActivity: false,
    );
  }
}
