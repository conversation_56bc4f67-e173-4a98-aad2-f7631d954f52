import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

@lazySingleton
class AppConfigDataSource {
  final SharedPreferences _preferences;

  AppConfigDataSource(this._preferences);

  final String _keyAgreePrivacy = "app_config_agree_privacy";

  bool getAgreePrivacy() {
    return _preferences.getBool(_keyAgreePrivacy) ?? false;
  }

  Future<void> setAgreePrivacy(bool agree) async {
    await _preferences.setBool(_keyAgreePrivacy, agree);
  }

  Future<void> clearAgreePrivacy() async {
    await _preferences.remove(_keyAgreePrivacy);
  }
}
