import 'package:injectable/injectable.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/core/api_client.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/models/activity/activity_info.dart';

@lazySingleton
class MembershipActivityRepo {
  final ApiClient _client;

  MembershipActivityRepo(this._client);

  /// 获取活动列表
  Future<Result<List<ActivityInfo>>> getActivityList() async {
    final result = await _client.get(ApiConstants.activityList,
        fromJson: ActivityResponse.fromJson);
    return result.map((i) => i?.lists ?? []);
  }

  /// 提交活动申请
  Future<Result<void>> submitActivity(String activityId, String link) async {
    final result = await _client.post(
      ApiConstants.submitActivity,
      data: {
        'activityId': activityId,
        'link': link,
      },
    );

    return result;
  }
}
