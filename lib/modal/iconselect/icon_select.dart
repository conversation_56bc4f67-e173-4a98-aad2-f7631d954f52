import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/iconselect/icon_state.dart';
import 'package:tasks/modal/iconselect/widgets/icon_category_item.dart';
import 'package:tasks/modal/iconselect/widgets/icon_item_container.dart';
import 'package:tasks/modal/iconselect/widgets/search_list.dart';
import 'package:tasks/modal/upgrade/upgradle_vip_utils.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/models/icon/icon_entity.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';

class IconSelectModal extends StatefulWidget {
  final AppIcon? value;

  final ValueChanged<AppIcon> onChange;

  const IconSelectModal({Key? key, this.value, required this.onChange})
      : super(key: key);

  @override
  _IconSelectModalState createState() => _IconSelectModalState();
}

class _IconSelectModalState extends State<IconSelectModal> {
  late IconState state;

  final ScrollController _scrollController = ScrollController();
  final FocusNode _searchFocusNode = FocusNode();

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void dispose() {
    _searchFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    state = IconState(
      widget.value,
    );

    // 添加搜索框焦点监听
    _searchFocusNode.addListener(() {
      state.setSearchFocus(_searchFocusNode.hasFocus);
    });
  }

  @override
  Widget build(BuildContext context) {
    return StateWarp(
      store: state,
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
        ),
        child: Column(
          children: [
            // 搜索框
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: ClearInputTextField(
                value: state.searchText,
                focusNode: _searchFocusNode,
                onChange: state.setSearchText,
                fillColor: colorScheme.surfaceContainer,
                icon: Icons.search,
                hintText: l10n.homeSearchIcon,
                onSubmitted: (v) {},
              ),
            ),
            Divider(
              height: 1,
              thickness: 0.5,
            ),
            // 主内容区域 - 使用Stack来实现搜索结果覆盖
            Expanded(
              child: Stack(
                children: [
                  // 底部的分类和图标内容
                  Row(
                    children: [
                      // 左侧可滚动区域
                      Observer(builder: (context) {
                        return Container(
                          color: colorScheme.surface,
                          width: 89.6,
                          child: SafeArea(
                            child: ListView.builder(
                                itemCount: state.searchIcons.length,
                                itemBuilder: (context, index) {
                                  return Observer(builder: (context) {
                                    final item = state.searchIcons[index];
                                    return IconCategoryItem(
                                      category: item,
                                      selected:
                                          state.selectedCategoryId == item.id,
                                      onSelected: (value) {
                                        state.selectCategory(value);
                                      },
                                    );
                                  });
                                }),
                          ),
                        );
                      }),
                      // 右侧内容区域
                      Observer(builder: (context) {
                        return IconItemContainer(
                          isVip: UserRepo.isVip(),
                          value: state.selectedCategory,
                          onSelected: handleIconSelect,
                          onIconChange: () {
                            state.loadCustomCategory();
                          },
                        );
                      }),
                    ],
                  ),
                  // 搜索结果覆盖层
                  Observer(builder: (context) {
                    if (!state.isSearching) {
                      return SizedBox.shrink();
                    }
                    return SearchList(
                        searchResults: state.allSearchResults,
                        onSelected: handleIconSelect);
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void handleIconSelect(IconEntity entity) {
    if (entity.requiredVip && !UserRepo.isVip()) {
      showUpgradeVip(context);
      return;
    }
    widget.onChange(entity.icon);
  }
}
