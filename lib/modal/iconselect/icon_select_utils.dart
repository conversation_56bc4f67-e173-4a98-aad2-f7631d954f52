import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/modal/iconselect/icon_select.dart';
import 'package:tasks/models/category_icon.dart';

void showIconSelect(
    BuildContext context, AppIcon? icon, ValueChanged<AppIcon> onChange) {
  // 先获取到数据

  showModalBottomSheet(
    context: context,
    showDragHandle: false,
    isScrollControlled: true,
    constraints:
        BoxConstraints(minWidth: double.infinity, maxWidth: double.infinity),
    builder: (context) {
      return SizedBox(
        height: MediaQuery.of(context).size.height - 120,
        child: IconSelectModal(
            onChange: (v) {
              onChange(v);
              context.pop();
            },
            value: icon),
      );
    },
  );
}
