import 'package:mobx/mobx.dart';
import 'package:tasks/data/icons/icons_repository.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/models/icon/category_entity.dart';
import 'package:tasks/models/icon/icon_entity.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/category_store.dart';

part 'icon_state.g.dart';

class IconState = _IconState with _$IconState;

abstract class _IconState extends BaseStore with Store {
  IconsRepository repository = getIt.get();

  CategoryStore categoryStore = getIt.get();

  @observable
  CategoryIconEntity? customCategory;

  @observable
  List<CategoryIconEntity> categoryIcons = [];

  @computed
  List<CategoryIconEntity> get searchIcons {
    final searchList = categoryIcons
        .map((r) {
          return r.copyWith(
              icons: r.icons
                  .where((element) => element.name.contains(searchText))
                  .toList());
        })
        .where((i) => i.icons.isNotEmpty)
        .toList();
    if (customCategory == null) {
      return searchList;
    }
    if (customCategory!.icons.isEmpty) {
      return searchList;
    }
    return [customCategory!, ...searchList];
  }

  @observable
  String selectedCategoryId = "1";

  @computed
  CategoryIconEntity? get selectedCategory {
    return searchIcons.where((i) => i.id == selectedCategoryId).firstOrNull;
  }

  @observable
  String searchText = "";

  @observable
  bool isSearching = false;

  @computed
  List<IconEntity> get allSearchResults {
    if (searchText.trim().isEmpty) return [];

    List<IconEntity> results = [];
    for (var category in categoryIcons) {
      for (var icon in category.icons) {
        if (icon.name.toLowerCase().contains(searchText.toLowerCase())) {
          results.add(icon);
        }
      }
    }

    // 添加自定义图标的搜索结果
    if (customCategory != null) {
      for (var icon in customCategory!.icons) {
        if (icon.name.toLowerCase().contains(searchText.toLowerCase())) {
          results.add(icon);
        }
      }
    }

    return results;
  }

  _IconState(
    AppIcon? icon,
  ) {
    loadCustomCategory();
    loadIcons();
  }

  @action
  Future<void> loadIcons() async {
    if (categoryStore.iconList.isNotEmpty) {
      runInAction(() {
        categoryIcons = categoryStore.iconList;
        selectedCategoryId = categoryIcons.firstOrNull?.id ?? "";
      });
      return;
    }
    runWithLoading(() async {
      // 加载图标
      final icons = await repository.getIcons();
      runInAction(() {
        categoryStore.iconList = icons;
        categoryIcons = icons;
        selectedCategoryId = categoryIcons.firstOrNull?.id ?? "";
      });
    });
  }

  @action
  Future<void> loadCustomCategory() async {
    customCategory = await repository.getCustomIcons();
  }

  @action
  selectCategory(CategoryIconEntity category) {
    selectedCategoryId = category.id;
  }

  @action
  setSearchText(String text) {
    searchText = text;
    isSearching = text.trim().isNotEmpty;
  }

  @action
  setSearchFocus(bool hasFocus) {
    if (!hasFocus && searchText.trim().isEmpty) {
      isSearching = false;
    }
  }
}
