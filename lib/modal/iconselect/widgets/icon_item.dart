import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/models/icon/icon_entity.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/widgets/app_icon_widget.dart';

class IconItem extends StatelessWidget {
  final IconEntity value;

  final ValueChanged<IconEntity> onSelected;

  final VoidCallback? onIconChange;

  final bool isVip;

  IconItem(
      {super.key,
      required this.isVip,
      required this.value,
      required this.onSelected,
      this.onIconChange});

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    TextTheme textTheme = Theme.of(context).textTheme;
    final showLock = !isVip && value.requiredVip;
    return GestureDetector(
      onTap: () => onSelected(value),
      child: Container(
        margin: const EdgeInsets.all(8),
        alignment: Alignment.center,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                color: showLock
                    ? colorScheme.onSurface.withOpacity(0.3)
                    : colorScheme.surface,
              ),
              Padding(
                padding: const EdgeInsets.all(14.0),
                child: AppIconWidget(
                  icon: value.icon,
                  size: 60,
                  fit: BoxFit.contain,
                ),
              ),
              Positioned(
                  top: 2,
                  left: 4,
                  child: Text(
                    value.name,
                    style: textTheme.bodySmall!.copyWith(fontSize: 8),
                  )),
              Visibility(
                  visible: !isVip && value.requiredVip,
                  child: Icon(
                    Icons.lock,
                    color: colorScheme.onSurface.withOpacity(0.5),
                  ))
            ],
          ),
        ),
      ),
    );
  }
}
