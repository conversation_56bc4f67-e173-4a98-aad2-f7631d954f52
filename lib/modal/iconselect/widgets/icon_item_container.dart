import 'package:flutter/material.dart';
import 'package:tasks/modal/iconselect/widgets/icon_item.dart';
import 'package:tasks/models/icon/category_entity.dart';
import 'package:tasks/models/icon/icon_entity.dart';

class IconItemContainer extends StatelessWidget {
  final CategoryIconEntity? value;

  final ValueChanged<IconEntity> onSelected;

  final VoidCallback? onIconChange;

  final bool isVip;

  IconItemContainer(
      {super.key,
      required this.isVip,
      required this.value,
      required this.onSelected,
      this.onIconChange});

  @override
  Widget build(BuildContext context) {
    final icons = value?.icons ?? [];
    return Expanded(
      child: Align(
        alignment: Alignment.topCenter,
        child: SafeArea(
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
            ),
            itemCount: icons.length,
            itemBuilder: (context, index) {
              return IconItem(
                isVip: isVip,
                value: icons[index],
                onSelected: onSelected,
                onIconChange: onIconChange,
              );
            },
          ),
        ),
      ),
    );
  }
}
