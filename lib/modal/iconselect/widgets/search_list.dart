import 'package:flutter/material.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/modal/iconselect/widgets/icon_item.dart';
import 'package:tasks/models/icon/icon_entity.dart';
import 'package:tasks/widgets/empty_state_widget.dart';

class SearchList extends StatelessWidget {
  final List<IconEntity> searchResults;

  final ValueChanged<IconEntity> onSelected;

  const SearchList(
      {super.key, required this.searchResults, required this.onSelected});

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    bool isVip = UserRepo.isVip();
    return Container(
      height: double.infinity,
      color: colorScheme.surfaceContainer,
      child: Builder(builder: (context) {
        if (searchResults.isEmpty) {
          return EmptyStateWidget(
            title: "没有找到相关图标",
            subtitle: "尝试使用其他关键词搜索",
            backgroundColor: colorScheme.surfaceContainer,
            icon: Icons.search_off,
          );
        }

        // 使用GridView展示搜索结果，一行5个
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
            ),
            itemCount: searchResults.length,
            itemBuilder: (context, index) {
              final icon = searchResults[index];
              return IconItem(
                  isVip: isVip, value: icon, onSelected: onSelected);
            },
          ),
        );
      }),
    );
  }
}
