import 'package:flutter/material.dart';
import 'package:tasks/models/icon/category_entity.dart';

class IconCategoryItem extends StatelessWidget {
  final CategoryIconEntity category;

  final ValueChanged<CategoryIconEntity> onSelected;

  final bool selected;

  IconCategoryItem(
      {super.key,
      required this.category,
      required this.selected,
      required this.onSelected});

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    TextTheme textTheme = Theme.of(context).textTheme;
    Color color =
        selected ? colorScheme.primary : textTheme.titleMedium!.color!;
    return InkWell(
      child: Container(
        height: 46,
        color: selected ? colorScheme.surfaceContainer : colorScheme.surface,
        child: Row(
          children: [
            SizedBox(
              width: 12,
              child: Row(
                children: [
                  Visibility(
                    visible: selected,
                    child: Container(
                      decoration: BoxDecoration(
                        color: colorScheme.primary,
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(2),
                          bottomRight: Radius.circular(2),
                        ),
                      ),
                      child: SizedBox(
                        width: 3,
                        height: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: Text(
                category.name,
                style: textTheme.titleMedium!.copyWith(
                    color: color,
                    fontWeight: selected ? FontWeight.w600 : FontWeight.w500),
              ),
            ),
          ],
        ),
      ),
      onTap: () {
        onSelected(category);
      },
    );
  }
}
