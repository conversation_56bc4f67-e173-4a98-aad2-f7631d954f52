import 'package:flutter/material.dart';
import 'package:tasks/data/user/update_manager.dart';
import 'package:tasks/models/app_update_info.dart';
import 'package:tasks/utils/url_utils.dart';

class UpdateDialog extends StatelessWidget {
  final AppUpdateInfo info;

  const UpdateDialog({Key? key, required this.info}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: _buildDialogContent(context),
    );
  }

  Widget _buildDialogContent(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          Text(
            '发现新版本${info.version ?? ""}',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 16),

          // 描述内容
          Text(
            info.releaseNotes ?? '新版本优化了性能并修复了已知问题，建议立即更新以获得更好的体验。',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 24),

          // 更新按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // 处理更新逻辑
                _handleUpdate();
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                '立即更新',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          SizedBox(height: 12),

          // 稍后提醒按钮
          Visibility(
            visible: !info.isForce,
            child: TextButton(
              onPressed: () {
                // 处理稍后提醒逻辑
                _handleNoTips();
                Navigator.pop(context);
              },
              child: Text(
                '稍后提醒',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleUpdate() {
    // 处理更新逻辑
    UrlUtils.openUrlOut(info.downloadUrl);
  }

  void _handleNoTips() {
    // 处理稍后提醒逻辑
    UpdateManager.setRemindLater();
  }
}

void showUpdateDialog(BuildContext context, AppUpdateInfo info) {
  showDialog(
    context: context,
    barrierDismissible: !info.isForce,
    builder: (BuildContext context) {
      return UpdateDialog(info: info);
    },
  );
}
