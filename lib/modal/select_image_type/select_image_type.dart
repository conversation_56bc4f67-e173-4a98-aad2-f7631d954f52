import 'package:bottom_picker/bottom_picker.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/iconselect/icon_select_utils.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/utils/image_utils.dart';

import 'image_source_type.dart';

Future<void> showImageSourceTypeSelect(
    BuildContext context, ValueChanged<AppIcon?> onChange,
    {int defaultIndex = 2}) async {
  final items = ImageSourceTypeEntity.values;
  final colorScheme = Theme.of(context).colorScheme;
  final l10n = S.of(context);
  var selectedIndex = defaultIndex;

  BottomPicker(
    items: [
      ...List.generate(items.length, (index) {
        final item = items[index];
        return Center(
          key: Key(index.toString()),
          child: Text(item.label),
        );
      })
    ],
    onChange: (item) {
      if (item is int) {
        selectedIndex = item;
      }
    },
    selectedItemIndex: selectedIndex,
    pickerTitle: SizedBox(
      height: 56,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // 关闭弹窗
            },
            child: Text(
              l10n.cancel,
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ),
          Text(
            l10n.commonImageType,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          TextButton(
            onPressed: () async {
              final selectedItem = items[selectedIndex].value;
              // 系统图标
              if (selectedItem == ImageSourceType.iconLibrary) {
                showIconSelect(context, null, (icon) {
                  onChange(icon);
                  context.pop(icon);
                });
                return;
              }
              // 拍照
              if (selectedItem == ImageSourceType.camera) {
                final icon = await ImageUtils.takePhoto(context);
                if (icon != null) {
                  onChange(icon);
                }
                if (!context.mounted) return;
                context.pop(icon);
                return;
              }
              // 从图库选择
              final icon = await ImageUtils.pickImage(context);
              if (icon != null) {
                onChange(icon);
              }
              if (!context.mounted) return;
              context.pop(icon);
            },
            child: Text(
              l10n.confirm,
              style: TextStyle(
                color: colorScheme.primary,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    ),
    titleAlignment: Alignment.center,
    displaySubmitButton: false,
    displayCloseIcon: false,
    pickerTextStyle: TextStyle(
      color: Colors.black,
      fontWeight: FontWeight.bold,
    ),
  ).show(context);
}
