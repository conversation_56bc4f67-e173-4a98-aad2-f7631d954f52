import 'package:tasks/generated/l10n.dart';

enum ImageSourceType {
  gallery,
  camera,
  iconLibrary,
}

class ImageSourceTypeEntity {
  final ImageSourceType value;
  final String label;

  const ImageSourceTypeEntity({required this.value, required this.label});

  static List<ImageSourceTypeEntity> get values => [
        ImageSourceTypeEntity(
            value: ImageSourceType.gallery,
            label: S.current.commonImageTypeGallery),
        ImageSourceTypeEntity(
            value: ImageSourceType.camera,
            label: S.current.commonImageTypeCamera),
        ImageSourceTypeEntity(
            value: ImageSourceType.iconLibrary,
            label: S.current.commonImageTypeIconLibrary),
      ];
}
