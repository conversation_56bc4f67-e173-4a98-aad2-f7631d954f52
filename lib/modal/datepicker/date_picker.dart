import 'package:bottom_picker/bottom_picker.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/datepicker/period_date_picker.dart';

/// 快捷按钮数据
class _QuickButtonData {
  final String label;
  final DateTime? date;
  final bool isEnabled;

  const _QuickButtonData(this.label, this.date, {this.isEnabled = true});
}

/// 时间选择器
/// baseDate基于这个时间去增加月份
Future<DateTime?> showAppDatePicker(
  BuildContext context,
  DateTime value, {
  DateTime? baseDate,
  String? title,
  DateTime? max,
  DateTime? min,
  List<PeriodOption>? periodOptions,
  bool showPeriodButtons = true,
}) {
  return showModalBottomSheet<DateTime?>(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder: (BuildContext context) {
      return _CustomDatePickerContent(
        initialValue: value,
        baseDate: baseDate,
        title: title,
        max: max,
        min: min,
        periodOptions: periodOptions,
        showPeriodButtons: showPeriodButtons,
      );
    },
  );
}

class _CustomDatePickerContent extends StatefulWidget {
  final DateTime initialValue;
  final DateTime baseDate;
  final String? title;
  final DateTime? max;
  final DateTime? min;
  final List<PeriodOption>? periodOptions;
  final bool showPeriodButtons;

  _CustomDatePickerContent({
    required this.initialValue,
    DateTime? baseDate,
    this.title,
    this.max,
    this.min,
    this.periodOptions,
    this.showPeriodButtons = true,
  }) : baseDate = baseDate ?? DateTime.now();

  @override
  State<_CustomDatePickerContent> createState() =>
      _CustomDatePickerContentState();
}

class _CustomDatePickerContentState extends State<_CustomDatePickerContent> {
  late DateTime currentValue;
  late DateTime displayMonth;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    currentValue = widget.initialValue;
    displayMonth =
        DateTime(widget.initialValue.year, widget.initialValue.month);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final l10n = S.of(context);

    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.85,
      ),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 年月选择器头部
          _buildMonthYearHeader(colorScheme, textTheme),
          // 星期标题
          _buildWeekdayHeader(colorScheme, textTheme),
          // 日历网格
          Flexible(
            child: _buildCalendarGrid(colorScheme, textTheme),
          ),
          // 底部快捷按钮
          _buildQuickButtons(colorScheme, textTheme, l10n),
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
        ],
      ),
    );
  }

  // 构建年月选择器头部
  Widget _buildMonthYearHeader(ColorScheme colorScheme, TextTheme textTheme) {
    final monthNames = [
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月'
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            child: Text(l10n.cancel, style: textTheme.bodyMedium),
            onPressed: () => context.pop(null),
          ),
          // 左箭头
          IconButton(
            onPressed: () => _changeMonth(-1),
            icon: Icon(Icons.chevron_left, color: colorScheme.onSurface),
          ),
          // 年月显示和点击
          GestureDetector(
            onTap: _showYearMonthPicker,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHigh.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                ' ${displayMonth.year} ${monthNames[displayMonth.month - 1]}',
                style: textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          // 右箭头
          IconButton(
            onPressed: () => _changeMonth(1),
            icon: Icon(Icons.chevron_right, color: colorScheme.onSurface),
          ),
          TextButton(
            child: Text(l10n.confirm,
                style:
                    textTheme.bodyMedium!.copyWith(color: colorScheme.primary)),
            onPressed: () => context.pop(currentValue),
          ),
        ],
      ),
    );
  }

  // 构建星期标题
  Widget _buildWeekdayHeader(ColorScheme colorScheme, TextTheme textTheme) {
    final weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        children: weekdays
            .map((weekday) => Expanded(
                  child: Center(
                    child: Text(
                      weekday,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ))
            .toList(),
      ),
    );
  }

  // 构建日历网格
  Widget _buildCalendarGrid(ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: _buildCalendarDays(colorScheme, textTheme),
    );
  }

  // 构建日历天数
  Widget _buildCalendarDays(ColorScheme colorScheme, TextTheme textTheme) {
    final firstDayOfMonth = DateTime(displayMonth.year, displayMonth.month, 1);
    final lastDayOfMonth =
        DateTime(displayMonth.year, displayMonth.month + 1, 0);
    final firstWeekday =
        firstDayOfMonth.weekday % 7; // 0 = Sunday, 1 = Monday, etc.

    final days = <Widget>[];

    // 添加上个月的日期（灰色显示）
    final prevMonth = DateTime(displayMonth.year, displayMonth.month - 1);
    final lastDayOfPrevMonth =
        DateTime(displayMonth.year, displayMonth.month, 0);
    for (int i = firstWeekday - 1; i >= 0; i--) {
      final day = lastDayOfPrevMonth.day - i;
      days.add(_buildDayCell(
        DateTime(prevMonth.year, prevMonth.month, day),
        colorScheme,
        textTheme,
        isCurrentMonth: false,
      ));
    }

    // 添加当前月的日期
    for (int day = 1; day <= lastDayOfMonth.day; day++) {
      days.add(_buildDayCell(
        DateTime(displayMonth.year, displayMonth.month, day),
        colorScheme,
        textTheme,
        isCurrentMonth: true,
      ));
    }

    // 添加下个月的日期（灰色显示）
    final nextMonth = DateTime(displayMonth.year, displayMonth.month + 1);
    final remainingCells = 42 - days.length; // 6 rows * 7 days = 42 cells
    for (int day = 1; day <= remainingCells; day++) {
      days.add(_buildDayCell(
        DateTime(nextMonth.year, nextMonth.month, day),
        colorScheme,
        textTheme,
        isCurrentMonth: false,
      ));
    }

    return GridView.count(
      crossAxisCount: 7,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: days,
    );
  }

  // 构建单个日期单元格
  Widget _buildDayCell(
      DateTime date, ColorScheme colorScheme, TextTheme textTheme,
      {required bool isCurrentMonth}) {
    final isSelected = date.year == currentValue.year &&
        date.month == currentValue.month &&
        date.day == currentValue.day;
    final isToday = date.year == DateTime.now().year &&
        date.month == DateTime.now().month &&
        date.day == DateTime.now().day;
    final isDisabled = !isCurrentMonth ||
        (widget.min != null && date.isBefore(widget.min!)) ||
        (widget.max != null && date.isAfter(widget.max!));

    return GestureDetector(
      onTap: isDisabled
          ? null
          : () {
              setState(() {
                currentValue = date;
              });
            },
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: isSelected
              ? colorScheme.primary
              : isToday
                  ? colorScheme.primary.withValues(alpha: 0.12)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            '${date.day}',
            style: textTheme.bodyLarge?.copyWith(
              color: isSelected
                  ? colorScheme.onPrimary
                  : isToday
                      ? colorScheme.primary
                      : isDisabled
                          ? colorScheme.onSurface.withValues(alpha: 0.3)
                          : colorScheme.onSurface,
              fontWeight:
                  isSelected || isToday ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  // 验证日期是否在允许的范围内
  bool _isDateInRange(DateTime? date) {
    if (date == null) return true; // null 日期（无日期）不受限制

    final dateOnly = DateTime(date.year, date.month, date.day);

    if (widget.min != null) {
      final minDateOnly =
          DateTime(widget.min!.year, widget.min!.month, widget.min!.day);
      if (dateOnly.isBefore(minDateOnly)) {
        return false;
      }
    }

    if (widget.max != null) {
      final maxDateOnly =
          DateTime(widget.max!.year, widget.max!.month, widget.max!.day);
      if (dateOnly.isAfter(maxDateOnly)) {
        return false;
      }
    }

    return true;
  }

  // 构建底部快捷按钮
  Widget _buildQuickButtons(
      ColorScheme colorScheme, TextTheme textTheme, S l10n) {
    if (!widget.showPeriodButtons) {
      return const SizedBox.shrink();
    }

    // 构建基础快捷按钮（带时间范围验证）
    final baseButtons = [
      _QuickButtonData('无日期', null, isEnabled: true), // 无日期不受限制
    ];

    // 构建时间段按钮（带时间范围验证）
    final periodButtons = <_QuickButtonData>[];
    if (widget.periodOptions != null) {
      for (final option in widget.periodOptions!) {
        if (option.enabled) {
          final date = addMonths(widget.baseDate, option.months);
          final isEnabled = _isDateInRange(date);
          periodButtons
              .add(_QuickButtonData(option.label, date, isEnabled: isEnabled));
        }
      }
    }

    // 合并所有按钮
    final allButtons = [...baseButtons, ...periodButtons];

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 动态生成按钮行
          ..._buildButtonRows(allButtons, colorScheme, textTheme),
        ],
      ),
    );
  }

  // 构建按钮行
  List<Widget> _buildButtonRows(List<_QuickButtonData> buttons,
      ColorScheme colorScheme, TextTheme textTheme) {
    final rows = <Widget>[];
    const buttonsPerRow = 4;

    for (int i = 0; i < buttons.length; i += buttonsPerRow) {
      final rowButtons = buttons.skip(i).take(buttonsPerRow).toList();

      rows.add(
        Row(
          children: [
            for (int j = 0; j < rowButtons.length; j++) ...[
              if (j > 0) const SizedBox(width: 12),
              _buildQuickButton(
                rowButtons[j].label,
                rowButtons[j].date,
                colorScheme,
                textTheme,
                isEnabled: rowButtons[j].isEnabled,
              ),
            ],
            // 填充剩余空间
            if (rowButtons.length < buttonsPerRow)
              ...List.generate(
                buttonsPerRow - rowButtons.length,
                (index) => const Expanded(child: SizedBox()),
              ),
          ],
        ),
      );

      if (i + buttonsPerRow < buttons.length) {
        rows.add(const SizedBox(height: 12));
      }
    }

    return rows;
  }

  // 构建单个快捷按钮
  Widget _buildQuickButton(String label, DateTime? date,
      ColorScheme colorScheme, TextTheme textTheme,
      {bool isEnabled = true}) {
    final isSelected = date != null &&
        date.year == currentValue.year &&
        date.month == currentValue.month &&
        date.day == currentValue.day;
    final isToday = date != null &&
        date.year == DateTime.now().year &&
        date.month == DateTime.now().month &&
        date.day == DateTime.now().day;

    return Expanded(
      child: GestureDetector(
        onTap: isEnabled
            ? () {
                if (date == null) {
                  // "无日期" 选项直接关闭弹窗并返回 null
                  Navigator.pop(context, null);
                } else {
                  // 其他选项只更新选中的日期，不关闭弹窗
                  setState(() {
                    currentValue = date;
                    displayMonth = DateTime(date.year, date.month);
                  });
                }
              }
            : null, // 禁用时不响应点击
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: !isEnabled
                ? colorScheme.surfaceContainerHigh
                    .withValues(alpha: 0.1) // 禁用状态的背景色
                : isSelected || isToday
                    ? colorScheme.primary
                    : colorScheme.surfaceContainerHigh.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Text(
              label,
              style: textTheme.bodyMedium?.copyWith(
                color: !isEnabled
                    ? colorScheme.onSurface.withValues(alpha: 0.3) // 禁用状态的文字颜色
                    : isSelected || isToday
                        ? colorScheme.onPrimary
                        : colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 切换月份
  void _changeMonth(int delta) {
    setState(() {
      displayMonth = DateTime(displayMonth.year, displayMonth.month + delta);
    });
  }

  // 显示年月选择器
  void _showYearMonthPicker() {
    BottomPicker.date(
      pickerTitle: Text(
        '选择年月',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      titleAlignment: Alignment.center,
      initialDateTime: displayMonth,
      maxDateTime: widget.max ?? DateTime(2100),
      minDateTime: widget.min ?? DateTime(1900),
      onSubmit: (date) {
        setState(() {
          displayMonth = DateTime(date.year, date.month);
        });
      },
    ).show(context);
  }
}
