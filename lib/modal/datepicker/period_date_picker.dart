/// 时间段选项配置
class PeriodOption {
  final String label;
  final int months;
  final bool enabled;

  const PeriodOption({
    required this.label,
    required this.months,
    this.enabled = true,
  });
}

/// 默认的时间段选项配置
class DefaultPeriodOptions {
  static List<PeriodOption> get defaultOptions => [
        const PeriodOption(label: '今天', months: 0),
        const PeriodOption(label: '1个月', months: 1),
        const PeriodOption(label: '3个月', months: 3),
        const PeriodOption(label: '6个月', months: 6),
        const PeriodOption(label: '9个月', months: 9),
        const PeriodOption(label: '1年', months: 12),
        const PeriodOption(label: '3年', months: 36),
      ];
}

/// 安全地给日期添加月份
/// 处理月末日期和跨年的情况
DateTime addMonths(DateTime date, int months) {
  try {
    int newYear = date.year;
    int newMonth = date.month + months;

    // 处理月份溢出
    while (newMonth > 12) {
      newYear++;
      newMonth -= 12;
    }

    while (newMonth < 1) {
      newYear--;
      newMonth += 12;
    }

    int newDay = date.day;

    // 处理月末日期（如1月31日加1个月应该是2月28/29日）
    int daysInNewMonth = DateTime(newYear, newMonth + 1, 0).day;
    if (newDay > daysInNewMonth) {
      newDay = daysInNewMonth;
    }

    return DateTime(newYear, newMonth, newDay, date.hour, date.minute,
        date.second, date.millisecond, date.microsecond);
  } catch (e) {
    // 如果计算失败，返回原始日期
    return date;
  }
}
