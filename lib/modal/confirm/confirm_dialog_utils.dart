import 'package:flutter/material.dart';
import 'package:tasks/modal/confirm/confirm_dialog.dart';

Future<bool?> showConfirm(BuildContext context,
    {String? title,
    String? content,
    String? confirmText,
    String? cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel}) async {
  return showDialog<bool>(
    context: context,
    builder: (context) => ConfirmDialog(
      title: title,
      content: content,
      confirmText: confirmText,
      cancelText: cancelText,
      onConfirm: onConfirm,
      onCancel: onCancel,
    ),
  );
}
