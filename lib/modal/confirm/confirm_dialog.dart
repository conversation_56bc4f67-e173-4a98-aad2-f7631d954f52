import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';

class ConfirmDialog extends StatefulWidget {
  final String? title;
  final String? content;
  final String? confirmText;
  final String? cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  const ConfirmDialog({
    super.key,
    this.title,
    this.content,
    this.confirmText,
    this.cancelText,
    this.onConfirm,
    this.onCancel,
  });

  @override
  State<ConfirmDialog> createState() => _ConfirmDialogState();
}

class _ConfirmDialogState extends State<ConfirmDialog> {
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return AlertDialog(
      title: Text(widget.title ?? l10n.tips),
      content: Text(widget.content ?? l10n.tipsContent),
      actions: [
        TextButton(
          child: Text(
            widget.cancelText ?? l10n.cancel,
            style: textTheme.bodyMedium,
          ),
          onPressed: () {
            widget.onCancel?.call();
            context.pop(false);
          },
        ),
        FilledButton(
          child: Text(
            widget.confirmText ?? l10n.confirm,
            style:
                textTheme.titleMedium!.copyWith(color: colorScheme.onPrimary),
          ),
          onPressed: () {
            widget.onConfirm?.call();
            context.pop(true);
          },
        ),
      ],
    );
  }
}
