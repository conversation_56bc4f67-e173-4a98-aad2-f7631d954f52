import 'package:flutter/material.dart';
import 'package:tasks/modal/upgrade/upgrade_vip.dart';

void showUpgradeVip(BuildContext context) {
  showModalBottomSheet(
    context: context,
    showDragHandle: true,
    isScrollControlled: true,
    constraints:
        BoxConstraints(minWidth: double.infinity, maxWidth: double.infinity),
    builder: (context) {
      return Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: SingleChildScrollView(
          child: UpgradeVipModal(),
        ),
      );
    },
  );
}
