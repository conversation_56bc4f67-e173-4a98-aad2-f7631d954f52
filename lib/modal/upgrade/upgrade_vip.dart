import 'dart:io';

import 'package:flutter/material.dart';
import 'package:tasks/data/config/config_repo.dart';
import 'package:tasks/data/pay/order_repo.dart';
import 'package:tasks/flavor.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/modal/upgrade/widgets/alipay/alipay_upgrade_confirm.dart';
import 'package:tasks/modal/upgrade/widgets/apple/apple_upgrade.dart';
import 'package:tasks/modal/upgrade/widgets/wechat/wechat_upgrade_confirm.dart';
import 'package:tasks/utils/privacy_utils.dart';

class UpgradeVipModal extends StatefulWidget {
  UpgradeVipModal({
    Key? key,
  }) : super(key: key);

  @override
  State<UpgradeVipModal> createState() => _UpgradeVipModalState();
}

class _UpgradeVipModalState extends State<UpgradeVipModal> {
  OrderRepo repo = getIt.get();

  S get l10n => S.of(context);

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  Widget build(BuildContext context) {
    final originAmount = ConfigRepo.getAllConfig().originAmount;
    final discountAmount = ConfigRepo.getAllConfig().discountAmount;

    return Container(
      padding: const EdgeInsets.all(20),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              l10n.ucenterUpgradeMember,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${l10n.ucenterVipOriginalPrice}  $originAmount',
              style: TextStyle(
                fontSize: 14,
                decoration: TextDecoration.lineThrough,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  discountAmount.substring(0, 1),
                  style: TextStyle(fontSize: 20),
                ),
                Text(
                  discountAmount.replaceRange(0, 1, ""),
                  style: textTheme.titleLarge!.copyWith(
                      fontSize: 32,
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold),
                ),
                SizedBox(
                  width: 4,
                ),
                Text(
                  l10n.ucenterLifetimeMember,
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
            const SizedBox(height: 30),
            _buildFeatureItem(
              icon: Icons.all_inclusive,
              title: l10n.ucenterTips1,
              subtitle: l10n.ucenterTips1Desc,
            ),
            const SizedBox(height: 20),
            _buildFeatureItem(
              icon: Icons.cloud_sync,
              title: l10n.ucenterTips2,
              subtitle: l10n.ucenterTips2Desc,
            ),
            const SizedBox(height: 20),
            _buildFeatureItem(
              icon: Icons.person,
              title: l10n.ucenterTips3,
              subtitle: l10n.ucenterTips3Desc,
            ),
            const SizedBox(height: 20),
            _buildFeatureItem(
              icon: Icons.insert_emoticon,
              title: l10n.ucenterTips4,
              subtitle: l10n.ucenterTips4Desc,
            ),
            const SizedBox(height: 20),
            Divider(),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '购买即代表同意',
                  style: textTheme.titleMedium,
                ),
                GestureDetector(
                  onTap: nav2Service,
                  child: Text(
                    '《用户协议》',
                    style: textTheme.titleSmall!
                        .copyWith(color: colorScheme.primary),
                  ),
                ),
              ],
            ),

            /// 仅安卓非play渠道展示zfb
            Visibility(
                visible: !isGoogle() && Platform.isAndroid,
                child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: AlipayUpgradeConfirm())),
            // IOS  展示苹果支付
            Visibility(visible: Platform.isIOS, child: AppleUpgrade()),
            // 安卓非谷歌支付渠道展示微信支付
            Visibility(visible: !isGoogle(), child: WechatUpgradeConfirm()),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Colors.blue,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
