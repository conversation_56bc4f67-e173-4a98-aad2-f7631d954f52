import 'package:flutter/material.dart';
import 'package:tasks/data/config/config_repo.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/utils/clipboard_utils.dart';

class WechatUpgradeConfirm extends StatefulWidget {
  WechatUpgradeConfirm({super.key});

  @override
  State<WechatUpgradeConfirm> createState() => _WechatUpgradeConfirmState();
}

class _WechatUpgradeConfirmState extends State<WechatUpgradeConfirm> {
  S get l10n => S.of(context);

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: TextButton.icon(
        onPressed: () async {
          ClipboardUtils.copyText(context, ConfigRepo.getAllConfig().devWechat);
          showConfirm(context, content: "已复制开发者微信号，排查问题！");
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        label: Text(
          "无法支付？获取帮助",
          style: textTheme.bodyMedium,
        ),
      ),
    );
  }
}
