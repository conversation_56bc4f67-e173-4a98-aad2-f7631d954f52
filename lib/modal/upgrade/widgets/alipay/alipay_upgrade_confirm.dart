import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/assets.gen.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/upgrade/widgets/alipay/alipay_upgrade_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/router_utils.dart';
import 'package:tasks/utils/toast_utils.dart';

class AlipayUpgradeConfirm extends StatefulWidget {
  AlipayUpgradeConfirm({super.key});

  @override
  State<AlipayUpgradeConfirm> createState() => _AlipayUpgradeConfirmState();
}

class _AlipayUpgradeConfirmState extends State<AlipayUpgradeConfirm> {
  late AlipayUpgradeState state;

  S get l10n => S.of(context);

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    super.initState();
    state = AlipayUpgradeState();
    reaction((_) => state.event, (e) {
      if (e == null) {
        return;
      }
      state.clearEvent();
      switch (e) {
        case AlipayEvent.waitForResult:
          ToastUtils.success(context, "正在获取支付结果,请稍后..");
          state.surePay();
          break;
        case AlipayEvent.payFail:
          ToastUtils.error(context, "支付失败或未支付，请退出应用重新进入再试试吧");
          break;
        case AlipayEvent.paySuccess:
          ToastUtils.success(context, "激活会员成功");
          context.pop();
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return StateWarp(
      store: state,
      child: SizedBox(
        width: double.infinity,
        height: 50,
        child: ElevatedButton.icon(
          onPressed: () async {
            // 未登录时，跳转登录页面
            if (!RouterUtils.checkLogin(context)) return;
            // 调用支付逻辑
            state.pay();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          icon: Image.asset(
            Assets.images.icAli.path,
            width: 36,
            height: 36,
          ),
          label: Text(
            "支付宝支付",
            style: textTheme.titleSmall!.copyWith(color: colorScheme.onPrimary),
          ),
        ),
      ),
    );
  }
}
