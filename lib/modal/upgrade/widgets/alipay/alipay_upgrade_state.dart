import 'package:mobx/mobx.dart';
import 'package:tasks/data/pay/order_remote_source.dart';
import 'package:tasks/data/pay/order_repo.dart';
import 'package:tasks/data/user/models/user_model.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/user_store.dart';
import 'package:tobias/tobias.dart';

part 'alipay_upgrade_state.g.dart';

class AlipayUpgradeState = _AlipayUpgradeState with _$AlipayUpgradeState;

enum AlipayEvent {
  waitForResult,
  paySuccess,
  payFail,
}

abstract class _AlipayUpgradeState extends BaseStore with Store {
  UserStore userStore = getIt.get();
  OrderRepo repo = getIt.get();

  OrderRemoteSource _dataSource = getIt.get();

  @observable
  AlipayEvent? event;

  @action
  Future<void> pay() async {
    runWithLoading(() async {
      final tobias = Tobias();
      if (!(await tobias.isAliPayInstalled)) {
        setError("请先安装支付宝");
        return;
      }
      // 获取再次点击支付时判断一下用户状态，如果已经是vip就不再创建订单了
      try {
        UserModel? userModel = await userStore.syncFetchUserInfo();
        if (userModel?.isVip == true) {
          event = AlipayEvent.paySuccess;
          return;
        }
      } catch (e) {}
      // 走接口创建订单
      final result = await _dataSource.createOrder();
      // 创建订单成功后调用surePay
      final payResult =
          await tobias.pay(result.data?.orderStr ?? "", evn: AliPayEvn.online);
      // final tradeNo =
      //     jsonDecode(payResult["result"])["alipay_trade_app_pay_response"]
      //         ["trade_no"];
      // 存在结果时告知用户正在获取支付结果
      event = AlipayEvent.waitForResult;
    });
  }

  @action
  Future<void> surePay() async {
    runWithLoading(() async {
      await Future.delayed(Duration(seconds: 2));
      UserModel? userModel = await userStore.syncFetchUserInfo();
      if (userModel?.isVip == true) {
        event = AlipayEvent.paySuccess;
      } else {
        event = AlipayEvent.payFail;
      }
    });
  }

  @action
  void clearEvent() {
    event = null;
  }
}
