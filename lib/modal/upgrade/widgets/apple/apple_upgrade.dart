import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/modal/upgrade/widgets/apple/apple_upgrade_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/router_utils.dart';
import 'package:tasks/utils/toast_utils.dart';

class AppleUpgrade extends StatefulWidget {
  AppleUpgrade({super.key});

  @override
  State<AppleUpgrade> createState() => _AppleUpgradeState();
}

class _AppleUpgradeState extends State<AppleUpgrade> {
  S get l10n => S.of(context);

  AppleUpgradeState state = getIt.get();

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    super.initState();
    // 监听事件
    reaction((_) => state.event, (event) {
      if (event == null) {
        return;
      }
      state.clearEvent();
      switch (event) {
        case ApplePayEvent.payPending:
          ToastUtils.info(context, "正在处理支付请求，请稍后..");
          context.loaderOverlay.show();
          break;
        case ApplePayEvent.waitForResult:
          ToastUtils.success(context, "正在获取支付结果,请稍后..");
          state.surePay();
          break;
        case ApplePayEvent.payFail:
          context.loaderOverlay.hide();
          ToastUtils.error(context, "支付失败或未支付，如您已支付，请联系客服处理");
          break;
        case ApplePayEvent.paySuccess:
          context.loaderOverlay.hide();
          ToastUtils.success(context, "激活会员成功");
          context.pop();
          break;
      }
    });
    // 获取端口数据
    state.loadProducts();
  }

  @override
  void dispose() {
    super.dispose();
    state.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StateWarp(
      store: state,
      child: Column(
        children: [
          SizedBox(
            height: 2,
          ),
          Text(
            "首次购买加载较慢，请耐心等待....",
            style: textTheme.labelSmall,
          ),
          InkWell(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 6.0),
              child: Text(
                "已购买，恢复购买",
                style: textTheme.bodyMedium,
              ),
            ),
            onTap: () {
              state.repay();
            },
          ),
          Observer(builder: (context) {
            return Visibility(
              visible: state.products.isNotEmpty,
              child: SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () async {
                    // 未登录时，跳转登录页面
                    if (!RouterUtils.checkLogin(context)) return;
                    // 调用支付逻辑
                    state.pay();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    l10n.ucenterVipUpgrade,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
