import 'dart:async';

import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/pay/apple_order_repo.dart';
import 'package:tasks/data/user/models/user_model.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/user_store.dart';
import 'package:tasks/utils/string_ext.dart';

part 'apple_upgrade_state.g.dart';

@lazySingleton
class AppleUpgradeState = _AppleUpgradeState with _$AppleUpgradeState;

enum ApplePayEvent { payPending, paySuccess, payFail, waitForResult }

abstract class _AppleUpgradeState extends BaseStore with Store {
  final AppleOrderRepo repo;
  final UserStore userStore;

  _AppleUpgradeState(this.repo, this.userStore);

  List<String> _kIds = [];

  late StreamSubscription<List<PurchaseDetails>> _subscription;
  late InAppPurchase _inAppPurchase;

  @observable
  List<ProductDetails> products = [];

  @observable
  ApplePayEvent? event;

  Future<void> init() async {
    _inAppPurchase = InAppPurchase.instance;
    //监听购买的事件
    final Stream<List<PurchaseDetails>> purchaseUpdated =
        _inAppPurchase.purchaseStream;
    _subscription = purchaseUpdated.listen((purchaseDetailsList) {
      _listenToPurchaseUpdated(purchaseDetailsList);
    }, onDone: () {
      _subscription.cancel();
    }, onError: (error) {
      error.printError();
    });

    final bool available = await _inAppPurchase.isAvailable();
    if (!available) {
      setError("当前设备不支持支付");
      // The store cannot be reached or accessed. Update the UI accordingly.
      return;
    }
  }

  /// 加载商品列表
  @action
  Future<void> loadProducts() async {
    try {
      runWithLoading(() async {
        // 从接口处获取商品列表
        final productList = await repo.getProductList();
        _kIds = productList.map((i) => i.id).toList();
        final ProductDetailsResponse response =
            // 从苹果端获取商品列表
            await _inAppPurchase.queryProductDetails(_kIds.toSet());
        products.addAll(response.productDetails);
      });
    } catch (e) {
      // doNothing
    }
  }

  /// 监听支付事件
  void _listenToPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) {
    for (PurchaseDetails purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        event = ApplePayEvent.payPending;
        return;
      }
      // 非pending 的支付事件，完成购买
      completePurchase(purchaseDetails);
      if (purchaseDetails.status == PurchaseStatus.canceled) {
        // 订单支付失败
        event = ApplePayEvent.payFail;
        return;
      }
      if (purchaseDetails.status == PurchaseStatus.error) {
        final msg = purchaseDetails.error?.message;
        if (msg == "BillingResponse.itemAlreadyOwned") {
          setError("异常流程，正在尝试恢复订单..");
          repay();
          return;
        }
        setError(purchaseDetails.error?.message ?? "");
      }
      // 恢复购买
      if (purchaseDetails.status == PurchaseStatus.restored ||
          purchaseDetails.status == PurchaseStatus.purchased) {
        runWithLoading(() async {
          final result = await repo.restorePurchase(
              purchaseDetails.verificationData.serverVerificationData);
          if (result.isSuccess) {
            event = ApplePayEvent.waitForResult;
          }
        });
      }
    }
  }

  /// 执行支付逻辑
  @action
  Future<void> pay() async {
    var productDetails = products.firstOrNull;
    if (productDetails == null) {
      await loadProducts();
      productDetails = products.firstOrNull;
    }
    if (productDetails == null) {
      setError("商品加载失败");
      return;
    }
    runWithLoading(() async {
      // 从接口获取订单id,用于校验
      final result = await repo.createOrder(productDetails!.id);
      final orderId = result.data?.orderId;
      if (orderId.isNullOrEmpty) {
        setError("订单创建失败");
        return;
      }
      // 通过创建的订单发起调用支付
      final PurchaseParam purchaseParam = PurchaseParam(
          productDetails: productDetails, applicationUserName: orderId);
      try {
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      } catch (e) {
        print('pay error: $e');
        event = ApplePayEvent.payFail;
      }
    });
  }

  /// 恢复购买记录
  @action
  Future<void> repay() async {
    runWithLoading(() async {
      UserModel? userModel = await userStore.syncFetchUserInfo();
      if (userModel?.isVip == true) {
        event = ApplePayEvent.paySuccess;
        return;
      }
      // 恢复购买记录
      await _inAppPurchase.restorePurchases();
    });
  }

  @action
  Future<void> surePay() async {
    runWithLoading(() async {
      await Future.delayed(Duration(seconds: 1));
      UserModel? userModel = await userStore.syncFetchUserInfo();
      if (userModel?.isVip == true) {
        event = ApplePayEvent.paySuccess;
      } else {
        event = ApplePayEvent.payFail;
      }
    });
  }

  Future<void> completePurchase(PurchaseDetails purchaseDetails) async {
    // 完成待处理的购买
    await _inAppPurchase.completePurchase(purchaseDetails);
  }

  @action
  void dispose() {
    _subscription.cancel();
  }

  @action
  void clearEvent() {
    event = null;
  }
}
