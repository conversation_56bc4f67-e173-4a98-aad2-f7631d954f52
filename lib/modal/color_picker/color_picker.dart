import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';

class ColorPickerItem extends StatelessWidget {
  final String title;
  final Color color;
  final ValueChanged<Color> onChanged;

  const ColorPickerItem({
    Key? key,
    required this.title,
    required this.color,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return ListTile(
      leading: Icon(Icons.color_lens),
      title: Text(
        title,
        style: textTheme.bodyMedium,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          Icon(Icons.chevron_right),
        ],
      ),
      onTap: () async {
        Color? newColor = await openColorPicker(context, color);
        if (newColor != null) {
          onChanged(newColor);
        }
      },
    );
  }
}

// 打开颜色选择器
Future<Color?> openColorPicker(BuildContext context, Color value) {
  final textTheme = Theme.of(context).textTheme;
  Color color = value;

  return showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text('选择颜色'),
        content: SingleChildScrollView(
          child: HueRingPicker(
            pickerColor: color,
            onColorChanged: (c) {
              color = c;
            },
            enableAlpha: true,
            displayThumbColor: true,
          ),
        ),
        actions: <Widget>[
          TextButton(
            child: Text(
              S.of(context).confirm,
              style: textTheme.titleMedium,
            ),
            onPressed: () {
              context.pop(color);
            },
          ),
        ],
      );
    },
  );
}
