import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/pages/asset/widgets/select_date_item.dart';
import 'package:tasks/utils/reg_constans.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';

Future<ExtraFeesEntity?> showEditExtraFeesModal(BuildContext context,
    {ExtraFeesEntity? extraFees}) async {
  return await showModalBottomSheet(
    context: context,
    showDragHandle: false,
    isScrollControlled: true,
    enableDrag: false,
    constraints:
        BoxConstraints(minWidth: double.infinity, maxWidth: double.infinity),
    builder: (context) {
      return Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: SingleChildScrollView(
          child: EditExtraFeesModal(
            extraFees: extraFees,
          ),
        ),
      );
    },
  );
}

class EditExtraFeesModal extends StatefulWidget {
  final ExtraFeesEntity? extraFees;

  EditExtraFeesModal({this.extraFees});

  @override
  _EditExtraFeesModalState createState() => _EditExtraFeesModalState();
}

class _EditExtraFeesModalState extends State<EditExtraFeesModal> {
  String name = "";
  String value = "";
  String note = "";

  bool _paid = true;
  DateTime _createAt = DateTime.now();

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    if (widget.extraFees != null) {
      setState(() {
        name = widget.extraFees!.name;
        value = widget.extraFees!.value;
        note = widget.extraFees!.note;
        _paid = widget.extraFees!.paid;
        _createAt = widget.extraFees!.createAt;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 费用类型
            Text(
              l10n.assetAdditionalItemsTypeLabel,
              style: textTheme.titleMedium,
            ),
            SizedBox(
              height: 8,
            ),
            Container(
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainer,
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              child: Row(
                children: [
                  Radio(
                    value: true,
                    groupValue: _paid,
                    onChanged: (value) {
                      setState(() {
                        _paid = value!;
                      });
                    },
                  ),
                  Text(l10n.assetAdditionalItemsConsume),
                  Radio(
                    value: false,
                    groupValue: _paid,
                    onChanged: (value) {
                      setState(() {
                        _paid = value!;
                      });
                    },
                  ),
                  Text(l10n.assetAdditionalItemsIncome),
                ],
              ),
            ),
            SizedBox(
              height: 8,
            ),
            // 费用名
            Text(
              l10n.assetAdditionalItemsNameLabel,
              style: textTheme.titleMedium,
            ),
            SizedBox(
              height: 8,
            ),
            ClearInputTextField(
              hintText: l10n.assetAdditionalItemsNamePlaceholder,
              value: name,
              textInputAction: TextInputAction.next,
              onChange: (v) {
                setState(() {
                  name = v;
                });
              },
              fillColor: colorScheme.surfaceContainer,
            ),
            SizedBox(
              height: 8,
            ),
            // 费用金额
            Text(
              l10n.assetAdditionalItemsAmountLabel,
              style: textTheme.titleMedium,
            ),
            SizedBox(
              height: 8,
            ),
            ClearInputTextField(
              hintText: l10n.assetAdditionalItemsAmountPlaceholder,
              value: value,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              filterPattern: RegFilterConstant.regAmount,
              textInputAction: TextInputAction.next,
              onChange: (v) {
                setState(() {
                  value = v;
                });
              },
              fillColor: colorScheme.surfaceContainer,
            ),
            SizedBox(
              height: 8,
            ),
            // 费用日期
            Text(
              l10n.assetAdditionalItemsDateLabel,
              style: textTheme.titleMedium,
            ),
            Container(
              margin: EdgeInsets.symmetric(vertical: 8.0),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainer,
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 12),
              child: SelectDateItem(
                title: l10n.assetAdditionalItemsDateLabel,
                date: _createAt,
                maxDate: DateTime.now(),
                padding: EdgeInsets.zero,
                onChange: (v) {
                  setState(() {
                    _createAt = v ?? DateTime.now();
                  });
                },
              ),
            ),
            // 备注信息
            Text(
              l10n.assetAdditionalItemsRemarkLabel,
              style: textTheme.titleMedium,
            ),
            SizedBox(
              height: 8,
            ),
            ClearInputTextField(
              hintText: l10n.assetAdditionalItemsRemarkPlaceholder,
              value: note,
              textInputAction: TextInputAction.done,
              onChange: (v) {
                setState(() {
                  note = v;
                });
              },
              fillColor: colorScheme.surfaceContainer,
            ),
            SizedBox(
              height: 8,
            ),
            Row(
              children: [
                Expanded(
                  child: FilledButton(
                    onPressed: onConfirm,
                    child: Text(l10n.commonSave,
                        style: textTheme.bodyMedium!
                            .copyWith(color: colorScheme.onPrimary)),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  /// 通过当前数据生成 ExtraFeesEntity 对象
  ExtraFeesEntity generateExtraFeesEntity() {
    if (widget.extraFees != null) {
      return widget.extraFees!.copyWith(
        name: name,
        value: value,
        note: note,
        paid: _paid,
        createAt: _createAt,
      );
    }
    return ExtraFeesEntity(
      name: name,
      value: value,
      note: note,
      paid: _paid,
      createAt: _createAt,
    );
  }

  void onConfirm() {
    final extraFee = generateExtraFeesEntity();
    if (extraFee.valid) {
      Navigator.of(context).pop(extraFee);
      return;
    }
    if (extraFee.name.isEmpty) {
      ToastUtils.error(context, l10n.assetAdditionalItemsNamePlaceholder);
      return;
    }
  }
}
