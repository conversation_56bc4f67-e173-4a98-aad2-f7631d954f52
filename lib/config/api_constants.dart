import 'package:tasks/flavor.dart';

class ApiConstants {
  // Base URL
  static String getBaseUrl() {
    return getFlavorBaseUrl();
  }

  static String baseImageUrl() => getBaseUrl();
  static const int connectTimeout = 2000; // 连接超时时间（毫秒）
  static const int receiveTimeout = 5000; // 接收超时时间（毫秒）

  // API endpoints

  // 通用
  static const String config = "config/v1/all";

  // 缓存相关
  static const String cache = "asset/v2/cache";
  static const String fetchCacheList = "asset/v1/fetchCacheList";
  static const String fetchCache = "asset/v1/fetchCache";
  static const String deleteCache = "asset/v1/deleteCache";

  // User相关接口
  static const String checkUpdate = 'user/v1/checkVersion';
  static const String login = 'user/v1/login';
  static const String emailLogin = 'user/v1/emailLogin';
  static const String wechatLogin = 'user/v2/wechatLogin';
  static const String wechatRegister = 'user/v1/wechatRegister';
  static const String wechatBind = 'user/v1/bindWechat';
  static const String wechatUnbind = 'user/v1/unbindWechat';
  static const String register = 'user/v2/register';
  static const String userInfo = 'user/v1/me/info';
  static const String editNickName = 'user/v1/updateNickName';
  static const String exchangeVip = 'user/v1/exchangeVip';
  static const String sendEmailCode = 'verifyCode/v1/email';
  static const String bindEmail = 'user/v1/bindEmail';

  // 会员活动相关
  static const String activityList = 'activity/v1/list';
  static const String submitActivity = 'activity/v1/submit';
  static const String unbindEmail = 'user/v1/unbindEmail';
  static const String getThirdList = 'user/v1/me/thirdAccount';

  // Message
  static const String messageList = "message/v1/list";

  // Icons接口
  static const String getIcons = "icon/v2/category/list";

  // order
  static const String createOrder = "pay/v2/createOrder";
  static const String surePay = "pay/v1/surePay";
}
