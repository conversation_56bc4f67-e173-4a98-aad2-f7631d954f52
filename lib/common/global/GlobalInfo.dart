import 'package:package_info_plus/package_info_plus.dart';
import 'package:tasks/utils/device_utils.dart';

///缓存全局信息
class GlobalInfo {
  GlobalInfo._() {}

  static Future<void> init() async {
    // 获取包信息
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    GlobalInfo.appName = packageInfo.appName;
    GlobalInfo.appVersion = packageInfo.version;
    GlobalInfo.appId = packageInfo.packageName;
    // 获取设备信息
    GlobalInfo.appArch = await DeviceUtils.getArchitecture() ?? "";
  }

  static var appName = "";
  static var appVersion = "";
  static var appArch = "";
  static var appId = "";
}
