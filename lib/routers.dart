import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/dialog/adaptive_dialog_page.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/pages/asset/asset_add/asset_add_page.dart';
import 'package:tasks/pages/asset/asset_settings/asset_settings_page.dart';
import 'package:tasks/pages/asset/batch_manage/batch_manage_page.dart';
import 'package:tasks/pages/asset/custom_icon/add_custom_icon_page.dart';
import 'package:tasks/pages/asset/edit/edit_asset.dart';
import 'package:tasks/pages/asset/extra_fees/extra_fees_manage_page.dart';
import 'package:tasks/pages/asset/recycle_bin/recycle_bin_page.dart';
import 'package:tasks/pages/buy_channel/buy_channel_edit.dart';
import 'package:tasks/pages/cache_center/cache_center.dart';
import 'package:tasks/pages/category/category_edit.dart';
import 'package:tasks/pages/category/manage/category_manage.dart';
import 'package:tasks/pages/debug/debug_page.dart';
import 'package:tasks/pages/login/login.dart';
import 'package:tasks/pages/main/main_pages.dart';
import 'package:tasks/pages/membership_activity/membership_activity_page.dart';
import 'package:tasks/pages/message/message_page.dart';
import 'package:tasks/pages/register/register.dart';
import 'package:tasks/pages/search/search_page.dart';
import 'package:tasks/pages/setting/aboub/about.dart';
import 'package:tasks/pages/setting/custom/custom_theme_page.dart';
import 'package:tasks/pages/setting/general/currency/currency_setting_page.dart';
import 'package:tasks/pages/setting/general/general_setting.dart';
import 'package:tasks/pages/setting/general/language/lanage_setting.dart';
import 'package:tasks/pages/setting/security/security_blur_setting.dart';
import 'package:tasks/pages/setting/setting.dart';
import 'package:tasks/pages/splash/splash_page.dart';
import 'package:tasks/pages/userinfo/bind_email/bind_email.dart';
import 'package:tasks/pages/userinfo/edit_user_info.dart';
import 'package:tasks/services/exception_handler_service.dart';
import 'package:tasks/utils/keyboard_utils.dart';
import 'package:tasks/utils/platform.dart';

import 'package:tasks/pages/asset/remark/asset_remark_edit.dart';
import 'package:tasks/pages/buy_channel/manage/buy_channel_manage.dart';
import 'package:tasks/pages/cache_center/webdav_config/webdav_config.dart';
import 'package:tasks/pages/share/share_page.dart';
import 'package:tasks/pages/storage_location/manage/storage_location_manage.dart';
import 'package:tasks/pages/storage_location/storage_location_edit.dart';

class Routers {
  static const String main = "/";
  static const String splash = "/splash";
  static const String setting = "/setting";
  static const String language = "/language";
  static const String currencySetting = "/currency_setting";

  // 资产
  static var addAsset = "/asset/add_asset";
  static var editAsset = "/asset/edit_asset";
  static var assetSettings = "/asset/asset_settings";
  static var customIcon = "/asset/custom_icon";
  static var batchManage = "/asset/batch_manage";
  static var recycleBin = "/asset/recycle_bin";
  static var extraFeesManage = "/asset/extra_fees_manage";
  static var assetRemarkEdit = "/asset/remark_edit";

  // 用户相关
  static var login = "/login";
  static var register = "/register";
  static var bindEmail = "/user/bind_email";
  static var generalSetting = "/setting/general";
  static var languageSetting = "/setting/general/language";
  static var customTheme = "/setting/custom_theme";
  static var securityBlurSetting = "/setting/security/blur";

  static var aboutUs = "/setting/aboutus";
  static var editUserInfo = "/edit_user_info";

  //分类
  static var categoryEdit = "/category/edit_category";
  static var categoryManage = "/category/manage";

  //购买渠道
  static var buyChannelEdit = "/buy_channel/edit_buy_channel";
  static var buyChannelManage = "/buy_channel/manage";

  //存放位置
  static var storageLocationEdit = "/storage_location/edit_storage_location";
  static var storageLocationManage = "/storage_location/manage";

  // 数据管理
  static var cacheCenter = "/cache/cache_center";

  // 消息
  static var messageList = "/message_list";

  static var search = "/search";

  static var share = "/share";

  static const String webdavConfig = '/webdav_config';

  static const String membershipActivity = '/membership_activity';

  static const String debug = '/debug';

  static List<String> forceLoginPage() {
    return [Routers.cacheCenter, Routers.editUserInfo];
  }
}

final router = GoRouter(
    navigatorKey: ExceptionHandlerService.navigatorKey,
    initialLocation: Routers.splash,
    routes: [
      GoRoute(
          path: Routers.splash,
          builder: (context, state) => SplashPage(
                path: state.uri.queryParameters['path'] ?? "",
              )),
      GoRoute(
        path: Routers.main,
        builder: (context, state) => MainPage(
          path: state.uri.queryParameters['path'] ?? "",
        ),
      ),
      GoRoute(
          path: Routers.addAsset,
          pageBuilder: (context, state) {
            return AdaptiveDialogPage(builder: (_) => AssetAddPage());
          }),
      GoRoute(
          path: Routers.customIcon,
          builder: (context, state) => AddCustomIconPage()),
      GoRoute(
          path: Routers.editAsset,
          pageBuilder: (context, state) => _buildPageWithTransition(
              state,
              AssetEditPage(
                assetId: state.uri.queryParameters['assetId'] ?? "",
              ))),
      GoRoute(
          path: Routers.assetSettings,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, AssetSettingsPage())),
      GoRoute(
          path: Routers.batchManage,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, BatchManagePage())),
      GoRoute(
          path: Routers.recycleBin,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, RecycleBinPage())),
      GoRoute(
          path: Routers.extraFeesManage,
          pageBuilder: (context, state) => _buildPageWithTransition(
              state,
              ExtraFeesManagePage(
                extraFees: state.extra != null &&
                        state.extra is Map<String, dynamic> &&
                        (state.extra as Map<String, dynamic>)['extraFees'] !=
                            null
                    ? ((state.extra as Map<String, dynamic>)['extraFees']
                        as List<ExtraFeesEntity>)
                    : [],
              ))),
      GoRoute(
        path: Routers.categoryManage,
        pageBuilder: (context, state) =>
            _buildPageWithTransition(state, CategoryManagePage()),
      ),
      GoRoute(
        path: Routers.categoryEdit,
        pageBuilder: (context, state) => _buildPageWithTransition(
            state,
            CategoryEditPage(
              categoryId: state.uri.queryParameters['categoryId'],
            )),
      ),
      GoRoute(
        path: Routers.login,
        pageBuilder: (context, state) =>
            _buildPageWithTransition(state, LoginPage()),
      ),
      GoRoute(
        path: Routers.register,
        pageBuilder: (context, state) => _buildPageWithTransition(
            state,
            RegisterPage(
              wechatId: state.uri.queryParameters['wechatId'],
            )),
      ),
      GoRoute(
        path: Routers.setting,
        pageBuilder: (context, state) =>
            _buildPageWithTransition(state, SettingPage()),
      ),
      GoRoute(
        path: Routers.cacheCenter,
        pageBuilder: (context, state) =>
            _buildPageWithTransition(state, CacheCenter()),
      ),
      GoRoute(
        path: Routers.editUserInfo,
        pageBuilder: (context, state) =>
            _buildPageWithTransition(state, EditUserInfo()),
      ),
      GoRoute(
          path: Routers.aboutUs,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, AboutUs())),
      GoRoute(
          path: Routers.customTheme,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, CustomThemePage())),
      GoRoute(
          path: Routers.securityBlurSetting,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, SecurityBlurSettingPage())),
      GoRoute(
          path: Routers.messageList,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, MessagePage())),
      GoRoute(
          path: Routers.generalSetting,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, GeneralSettingPage())),
      GoRoute(
          path: Routers.languageSetting,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, LanguageSettingPage())),
      GoRoute(
          path: Routers.bindEmail,
          pageBuilder: (context, state) => _buildPageWithTransition(
              state,
              BindEmailPage(
                type: state.uri.queryParameters["type"],
              ))),
      GoRoute(
          path: Routers.search,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, SearchPage())),
      GoRoute(
          path: Routers.share,
          pageBuilder: (context, state) =>
              _buildPageWithTransition(state, SharePage())),
      GoRoute(
        path: Routers.webdavConfig,
        builder: (context, state) => WebdavConfigPage(),
      ),
      GoRoute(
        path: Routers.currencySetting,
        builder: (context, state) => const CurrencySettingPage(),
      ),
      GoRoute(
        path: Routers.membershipActivity,
        pageBuilder: (context, state) =>
            _buildPageWithTransition(state, MembershipActivityPage()),
      ),
      GoRoute(
        path: Routers.buyChannelManage,
        pageBuilder: (context, state) =>
            _buildPageWithTransition(state, BuyChannelManagePage()),
      ),
      GoRoute(
        path: Routers.buyChannelEdit,
        pageBuilder: (context, state) => _buildPageWithTransition(
            state,
            BuyChannelEditPage(
              channelId: state.uri.queryParameters['channelId'],
            )),
      ),
      GoRoute(
        path: Routers.storageLocationManage,
        pageBuilder: (context, state) =>
            _buildPageWithTransition(state, StorageLocationManagePage()),
      ),
      GoRoute(
        path: Routers.storageLocationEdit,
        pageBuilder: (context, state) => _buildPageWithTransition(
            state,
            StorageLocationEditPage(
              locationId: state.uri.queryParameters['locationId'],
            )),
      ),
      GoRoute(
        path: Routers.assetRemarkEdit,
        pageBuilder: (context, state) => _buildPageWithTransition(
            state,
            AssetRemarkEditPage(
              assetName: state.uri.queryParameters['assetName'],
              initialRemark: state.uri.queryParameters['initialRemark'],
            )),
      ),
      GoRoute(
        path: Routers.debug,
        pageBuilder: (context, state) =>
            _buildPageWithTransition(state, DebugPage()),
      ),
    ],
    redirect: (context, state) {
      // 新增拦截器，拦截未登录用户不能进入数据备份页面
      if (Routers.forceLoginPage().contains(state.matchedLocation)) {
        if (!UserRepo.isLogin()) {
          return Routers.login;
        }
      }
      // 判断是否为了添加资产
      return null;
    });

// 统一的页面构建函数
Page _buildPageWithTransition(GoRouterState state, Widget child) {
  if (isIos()) {
    return MaterialPage(
      key: state.pageKey,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => systemHideKeyboard(),
        child: child,
      ),
      // 保持iOS原生过渡效果
      fullscreenDialog: false,
    );
  }
  return CustomTransitionPage(
    key: state.pageKey,
    child: child,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      // 页面进入时从右侧滑入
      var enterAnimation = Tween<Offset>(
        begin: Offset(1.0, 0.0), // 从右侧进入
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Curves.easeInOut,
      ));

      // 页面退出时从左侧滑出
      var exitAnimation = Tween<Offset>(
        begin: Offset.zero,
        end: Offset(-1.0, 0.0), // 向左侧退出
      ).animate(CurvedAnimation(
        parent: secondaryAnimation,
        curve: Curves.easeInOut,
      ));

      // 使用 SlideTransition 实现滑动效果
      return SlideTransition(
        position: enterAnimation,
        child: SlideTransition(
          position: exitAnimation,
          child: child,
        ),
      );
    },
  );
}
