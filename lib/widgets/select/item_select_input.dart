import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/cdkey_input/cdkey_input_utils.dart';
import 'package:tasks/utils/string_ext.dart';

class ItemSelectInput extends StatefulWidget {
  const ItemSelectInput(
      {super.key,
      required this.title,
      required this.value,
      required this.onValueChanged,
      this.tail,
      this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 16)});

  final String title;

  final String value;

  final ValueChanged<String> onValueChanged;

  final EdgeInsetsGeometry? padding;

  final Widget? tail;

  @override
  State<ItemSelectInput> createState() => _ItemSelectInputState();
}

class _ItemSelectInputState extends State<ItemSelectInput> {
  int index = 0;

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final showTips = widget.value.isNullOrEmpty;
    return InkWell(
      onTap: () async {
        final value = await showBottomInputDialog(context,
            title: widget.title,
            hintText: widget.title,
            initialValue: widget.value);
        if (value.isFailure) return;
        widget.onValueChanged(value.data ?? "");
      },
      child: Container(
        padding: widget.padding,
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer, // 使用相同的背景色
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 1,
              child: showTips
                  ? Text(
                      widget.title,
                      style: textTheme.bodyMedium,
                      maxLines: 1,
                    )
                  : Text(
                      widget.value,
                      style: textTheme.bodyMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
            ),
            SizedBox(
              width: 4,
            ),
            SizedBox(
              width: 6,
            ),
            if (widget.tail != null) widget.tail!,
          ],
        ),
      ),
    );
  }
}
