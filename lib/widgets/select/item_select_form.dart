import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';

class ItemSelectForm extends StatefulWidget {
  const ItemSelectForm(
      {super.key,
      required this.title,
      required this.value,
      required this.onTap,
      this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 16)});

  final String title;

  final String value;

  final VoidCallback onTap;

  final EdgeInsetsGeometry? padding;

  @override
  State<ItemSelectForm> createState() => _ItemSelectFormState();
}

class _ItemSelectFormState extends State<ItemSelectForm> {
  int index = 0;

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return InkWell(
      onTap: widget.onTap,
      child: Container(
        padding: widget.padding,
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer, // 使用相同的背景色
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              widget.title,
              style: textTheme.bodyMedium,
            ),
            SizedBox(
              width: 4,
            ),
            Spacer(),
            SizedBox(
              width: 6,
            ),
            Text(widget.value),
            SizedBox(
              width: 6,
            ),
            Icon(
              Icons.chevron_right,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
