import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/utils/clipboard_utils.dart';

/// 异常报告弹窗
class ExceptionReportDialog extends StatefulWidget {
  final Object error;
  final StackTrace? stackTrace;
  final String errorType;

  const ExceptionReportDialog({
    Key? key,
    required this.error,
    this.stackTrace,
    required this.errorType,
  }) : super(key: key);

  @override
  State<ExceptionReportDialog> createState() => _ExceptionReportDialogState();
}

class _ExceptionReportDialogState extends State<ExceptionReportDialog> {
  bool _showDetails = false;

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainer,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: colorScheme.error,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      l10n.exceptionDialogTitle,
                      style: textTheme.headlineSmall?.copyWith(
                        color: colorScheme.onSurface,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // 描述文本
              Text(
                l10n.exceptionDialogDescription,
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
              const SizedBox(height: 20),

              // 错误类型
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: colorScheme.errorContainer.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: colorScheme.error.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  '${l10n.exceptionDialogErrorType}: ${widget.errorType}',
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // 显示/隐藏详情按钮
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _showDetails = !_showDetails;
                  });
                },
                icon: Icon(
                  _showDetails ? Icons.expand_less : Icons.expand_more,
                  color: colorScheme.primary,
                ),
                label: Text(
                  _showDetails
                      ? l10n.exceptionDialogHideDetails
                      : l10n.exceptionDialogShowDetails,
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.primary,
                  ),
                ),
              ),

              // 错误详情
              if (_showDetails) ...[
                const SizedBox(height: 12),
                Flexible(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainerHighest
                          .withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colorScheme.outline.withValues(alpha: 0.3),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${l10n.exceptionDialogErrorMessage}:',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.error.toString(),
                            style: textTheme.bodySmall?.copyWith(
                              color:
                                  colorScheme.onSurface.withValues(alpha: 0.8),
                              fontFamily: 'monospace',
                            ),
                          ),
                          if (widget.stackTrace != null) ...[
                            const SizedBox(height: 12),
                            Text(
                              '${l10n.exceptionDialogStackTrace}:',
                              style: textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              widget.stackTrace.toString(),
                              style: textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurface
                                    .withValues(alpha: 0.8),
                                fontFamily: 'monospace',
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // 按钮区域
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // 复制按钮
                  TextButton.icon(
                    onPressed: () => _copyErrorInfo(context),
                    icon: Icon(
                      Icons.copy,
                      size: 18,
                      color: colorScheme.primary,
                    ),
                    label: Text(
                      l10n.exceptionDialogCopy,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // 关闭按钮
                  FilledButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      l10n.exceptionDialogClose,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 复制错误信息到剪贴板
  void _copyErrorInfo(BuildContext context) {
    final errorInfo = StringBuffer();
    errorInfo.writeln('Error Type: ${widget.errorType}');
    errorInfo.writeln('Error Message: ${widget.error}');
    if (widget.stackTrace != null) {
      errorInfo.writeln('Stack Trace:');
      errorInfo.writeln(widget.stackTrace.toString());
    }

    ClipboardUtils.copyText(context, errorInfo.toString());
  }
}
