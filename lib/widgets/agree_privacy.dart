import 'package:flutter/material.dart';
import 'package:tasks/utils/privacy_utils.dart';

class AgreePrivacy extends StatelessWidget {
  AgreePrivacy({Key? key, required this.value, required this.onChanged})
      : super(key: key);

  final bool value;
  final ValueChanged<bool?> onChanged;

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    ColorScheme colorScheme = Theme.of(context).colorScheme;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center, // 确保顶部对齐
      children: [
        GestureDetector(
          onTap: () {
            onChanged(!value);
          },
          child: Stack(
            children: [
              if (value)
                Icon(
                  Icons.check_circle_rounded,
                  color: colorScheme.primary,
                  size: 18,
                ),
              if (!value)
                Icon(
                  Icons.check_circle_outline,
                  size: 18,
                ),
            ],
          ),
        ),
        SizedBox(
          width: 4,
        ),
        GestureDetector(
          onTap: () => onChanged(!value),
          child: Row(
            children: [
              Text(
                '已阅读并同意',
                style: textTheme.bodySmall,
              ),
              GestureDetector(
                onTap: nav2Service,
                child: Text(
                  '《用户协议》',
                  style: textTheme.titleSmall!
                      .copyWith(color: colorScheme.primary),
                ),
              ),
              Text(
                '和',
                style: textTheme.bodySmall,
              ),
              GestureDetector(
                onTap: nav2Privacy,
                child: Text('《隐私协议》',
                    style: textTheme.titleSmall!
                        .copyWith(color: colorScheme.primary)),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
