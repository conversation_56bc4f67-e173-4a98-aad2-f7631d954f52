import 'package:flutter/material.dart';

enum BtnType { primary, normal, error }

class SolidBtn extends StatelessWidget {
  final BtnType type;

  final String label;

  final VoidCallback? onPressed;

  const SolidBtn(
      {super.key, required this.type, required this.label, this.onPressed});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    Color backgroundColor;
    switch (type) {
      case BtnType.primary:
        backgroundColor = colorScheme.primary;
        break;
      case BtnType.normal:
        backgroundColor = colorScheme.surfaceContainer;
        break;
      case BtnType.error:
        backgroundColor = colorScheme.error;
        break;
    }
    Color textColor;
    switch (type) {
      case BtnType.primary:
        textColor = colorScheme.onPrimary;
        break;
      case BtnType.normal:
        textColor = colorScheme.onSurface;
        break;
      case BtnType.error:
        textColor = colorScheme.onError;
        break;
    }

    return FilledButton(
      style: FilledButton.styleFrom(backgroundColor: backgroundColor),
      onPressed: () {
        onPressed?.call();
      },
      child: Text(label,
          style: (type == BtnType.primary
                  ? textTheme.titleMedium
                  : textTheme.bodyMedium)!
              .copyWith(color: textColor)),
    );
  }
}
