import 'package:flutter/material.dart';
import 'package:tasks/widgets/filter_options/filter_item.dart';

class FilterOptions extends StatefulWidget {
  final String title;
  final List<FilterItem> options;
  final bool isMultiSelect;
  final List<String> value; // 改为使用id作为标识
  final ValueChanged<List<String>> onValueChange;
  final bool showAllOption;

  FilterOptions({
    required this.title,
    required this.options,
    this.isMultiSelect = false,
    this.value = const [],
    required this.onValueChange,
    this.showAllOption = true,
  });

  @override
  _FilterOptionsState createState() => _FilterOptionsState();
}

class _FilterOptionsState extends State<FilterOptions> {
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  void _handleSelection(FilterItem item, bool selected) {
    if (widget.isMultiSelect) {
      _handleMultiSelect(item, selected);
      return;
    }
    _handleSingleSelect(item, selected);
  }

  void _handleMultiSelect(FilterItem item, bool selected) {
    final value = widget.value;
    if (item.id == FilterItem.filterAllId) {
      // 多选模式：选中/取消所有选项
      widget.onValueChange(
          selected ? widget.options.map((e) => e.id).toList() : []);
      return;
    }
    var newValue = [...value];
    if (selected) {
      newValue.add(item.id);
    } else {
      newValue.remove(item.id);
    }
    widget.onValueChange(newValue);
  }

  void _handleSingleSelect(FilterItem item, bool selected) {
    if (selected) {
      widget.onValueChange([item.id]);
    }
  }

  bool _isItemSelected(FilterItem item) {
    final value = widget.value;
    if (item.id == FilterItem.filterAllId) {
      return widget.isMultiSelect
          ? value.length == widget.options.length
          : value.contains(FilterItem.filterAllId);
    }
    return value.contains(item.id);
  }

  @override
  Widget build(BuildContext context) {
    final allOptions = [
      if (widget.showAllOption) FilterItem.filterAll,
      ...widget.options,
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding:
              const EdgeInsets.only(top: 16.0, bottom: 12, left: 16, right: 16),
          child: Text(widget.title, style: textTheme.titleLarge),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: LayoutBuilder(
            builder: (context, constraints) {
              final itemWidth = (constraints.maxWidth - 16) / 3; // 减去总间隔(8*2)
              return Wrap(
                spacing: 8, // 横向间隔
                runSpacing: 8.0,
                children: allOptions.map((item) {
                  final isSelected = _isItemSelected(item);
                  return SizedBox(
                    width: itemWidth,
                    height: 36,
                    child: FilterChip(
                      label: Container(
                        width: double.infinity,
                        alignment: Alignment.center,
                        child: Text(
                          item.displayName,
                          style: textTheme.bodyMedium!.copyWith(
                            color: isSelected
                                ? colorScheme.onPrimary
                                : textTheme.bodyMedium!.color,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      padding: EdgeInsets.zero,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      selected: isSelected,
                      showCheckmark: false,
                      selectedColor: colorScheme.primary,
                      side: null,
                      backgroundColor: colorScheme.surface,
                      onSelected: (selected) =>
                          _handleSelection(item, selected),
                    ),
                  );
                }).toList(),
              );
            },
          ),
        ),
      ],
    );
  }
}
