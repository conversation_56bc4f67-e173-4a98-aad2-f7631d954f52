import 'package:flutter/material.dart';

class EmptyStateWidget extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final IconData? icon;
  final double? iconSize;
  final Color? iconColor;
  final Widget? actionButton;

  const EmptyStateWidget({
    Key? key,
    required this.title,
    this.subtitle,
    this.backgroundColor,
    this.borderRadius,
    this.icon = Icons.inbox_outlined,
    this.iconSize = 64,
    this.iconColor,
    this.actionButton,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: iconColor ?? colorScheme.onSurface.withOpacity(0.4),
            ),
            SizedBox(height: 16),
            Text(
              title,
              style: textTheme.bodyMedium,
            ),
            Visibility(
                visible: subtitle != null,
                child: Column(
                  children: [
                    SizedBox(height: 8),
                    Text(
                      subtitle ?? "",
                      style: textTheme.labelMedium,
                    ),
                  ],
                )),
            if (actionButton != null) ...[
              SizedBox(height: 24),
              actionButton!,
            ],
          ],
        ),
      ),
    );
  }
}
