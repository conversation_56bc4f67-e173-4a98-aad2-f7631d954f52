import 'dart:async';

import 'package:flutter/material.dart';
import 'package:tasks/utils/reg_constans.dart';
import 'package:tasks/widgets/app_textfield.dart';

class VerifyCodeTextField extends StatefulWidget {
  final String hintText;
  final IconData? icon;
  final Widget? tailIcon;
  final ValueChanged<String> onChange;
  final bool obscureText;
  final RegExp? filterPattern;
  final TextEditingController? controller;
  final TextInputType keyboardType;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;
  final Future<bool> Function() onGetCode;
  final InputBorder? border;
  final Color? fillColor;

  const VerifyCodeTextField({
    super.key,
    required this.hintText,
    this.icon,
    this.controller,
    required this.onChange,
    this.filterPattern,
    this.textInputAction,
    this.focusNode,
    this.tailIcon,
    required this.onGetCode,
    this.fillColor,
    bool? obscureText,
    TextInputType? keyboardType,
    InputBorder? border,
  })  : obscureText = obscureText ?? false,
        keyboardType = keyboardType ?? TextInputType.text,
        border = const OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
          borderSide: BorderSide.none,
        );

  @override
  State<VerifyCodeTextField> createState() => _VerifyCodeTextFieldState();
}

class _VerifyCodeTextFieldState extends State<VerifyCodeTextField> {
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;
  late TextEditingController _controller;

  final FocusNode _focusNode = FocusNode();
  bool _showClearButton = false;
  bool _isCounting = false; // 添加: 控制倒计时按钮的状态
  int _secondsRemaining = 59; // 添加: 剩余秒数
  Timer? _timer; // 添加: 倒计时定时器

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _focusNode.removeListener(_onFocusChanged);
    _focusNode.dispose();
    _timer?.cancel(); // 添加: 取消定时器
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {
      _showClearButton = _controller.text.isNotEmpty && _focusNode.hasFocus;
    });
  }

  void _onFocusChanged() {
    setState(() {
      _showClearButton = _controller.text.isNotEmpty && _focusNode.hasFocus;
    });
  }

  void _startCountdown() {
    // 添加: 启动倒计时
    setState(() {
      _isCounting = true;
      _secondsRemaining = 59;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_secondsRemaining > 0) {
          _secondsRemaining--;
        } else {
          _onCountdownEnd();
        }
      });
    });
  }

  void _onCountdownEnd() {
    // 添加: 倒计时结束后的逻辑
    _timer?.cancel();
    setState(() {
      _isCounting = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppTextField(
      hintText: widget.hintText,
      icon: widget.icon,
      filterPattern: RegFilterConstant.regPassword,
      tailIcon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _isCounting // 修改: 根据倒计时状态显示不同的尾部图标
              ? Text('$_secondsRemaining s')
              : TextButton(
                  onPressed: () async {
                    final result = await widget.onGetCode();
                    if (result) {
                      _startCountdown();
                    }
                  },
                  child: Text(
                    "获取验证码",
                    style: textTheme.bodyMedium!
                        .copyWith(color: colorScheme.primary),
                  ),
                )
        ],
      ),
      onChange: widget.onChange,
      focusNode: _focusNode,
      controller: _controller,
      textInputAction: widget.textInputAction,
      border: widget.border,
      fillColor: widget.fillColor,
    );
  }
}
