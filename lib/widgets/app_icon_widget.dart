import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/models/category_icon.dart';

class AppIconWidget extends StatefulWidget {
  final AppIcon icon;
  final double size;
  final Color? color;
  final BoxFit fit;

  AppIconWidget({
    Key? key,
    this.size = 24,
    this.color,
    this.fit = BoxFit.cover,
    AppIcon? icon,
  })  : icon = icon ?? AppIcon.defaultIcon(),
        super(key: key) {}

  @override
  State<AppIconWidget> createState() => _AppIconWidgetState();
}

class _AppIconWidgetState extends State<AppIconWidget> {
  @override
  Widget build(BuildContext context) {
    if (widget.icon.type == IconType.local && widget.icon.value != null) {
      return SizedBox(
        width: widget.size,
        height: widget.size,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: Image.file(
            File(widget.icon.value!),
            width: 24,
            height: 24,
            fit: widget.fit,
            errorBuilder: (context, error, stackTrace) {
              return Icon(Icons.broken_image);
            },
          ),
        ),
      );
    }

    if (widget.icon.type == IconType.remote && widget.icon.value != null) {
      final url = widget.icon.value?.startsWith("http") == true
          ? widget.icon.value
          : Uri.encodeFull(
              "${ApiConstants.baseImageUrl()}${widget.icon.value}");

      // Only use CachedNetworkImage if url is not null and not empty
      if (url != null && url.isNotEmpty) {
        return CachedNetworkImage(
          imageUrl: url,
          progressIndicatorBuilder: (context, url, downloadProgress) =>
              CircularProgressIndicator(value: downloadProgress.progress),
          errorWidget: (context, url, error) => Icon(Icons.broken_image),
          width: widget.size,
          height: widget.size,
          fit: widget.fit,
          cacheKey: url,
        );
      }
    }
    return Container();
  }
}
