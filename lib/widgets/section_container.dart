import 'package:flutter/material.dart';

class SectionWarp extends StatelessWidget {
  const SectionWarp({super.key, required this.title, required this.children});

  final List<Widget> children;
  final String title;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return Container(
      margin: EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible: title.isNotEmpty,
            child: Padding(
              padding: EdgeInsets.only(left: 6, bottom: 8, top: 24),
              child: Text(
                title,
                style: textTheme.bodyMedium,
              ),
            ),
          ),
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Container(
              color: colorScheme.surfaceContainer,
              child: Column(
                children: children,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
