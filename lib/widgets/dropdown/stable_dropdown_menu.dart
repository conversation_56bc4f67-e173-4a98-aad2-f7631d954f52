import 'package:flutter/material.dart';

/// 稳定的下拉菜单项
class StableDropdownMenuItem<T> {
  final T value;
  final String title;
  final IconData? icon;
  final VoidCallback? onTap;
  final bool isSelected;

  const StableDropdownMenuItem({
    required this.value,
    required this.title,
    this.icon,
    this.onTap,
    this.isSelected = false,
  });
}

/// 稳定的下拉菜单组件
/// 使用 Flutter 原生组件实现，避免第三方包的兼容性问题
class StableDropdownMenu<T> extends StatelessWidget {
  final Widget child;
  final List<StableDropdownMenuItem<T>> items;
  final double? menuWidth;
  final double? menuOffset;

  const StableDropdownMenu({
    Key? key,
    required this.child,
    required this.items,
    this.menuWidth = 200,
    this.menuOffset = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<T>(
      offset: Offset(menuOffset ?? 0, 0),
      constraints: BoxConstraints(
        minWidth: menuWidth ?? 200,
        maxWidth: menuWidth ?? 200,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 8,
      itemBuilder: (context) {
        return items.map((item) {
          return PopupMenuItem<T>(
            value: item.value,
            onTap: item.onTap,
            child: Row(
              children: [
                if (item.icon != null) ...[
                  Icon(
                    item.icon,
                    size: 20,
                    color: item.isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Text(
                    item.title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: item.isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                  ),
                ),
                if (item.isSelected)
                  Icon(
                    Icons.check_rounded,
                    size: 20,
                    color: Theme.of(context).colorScheme.primary,
                  ),
              ],
            ),
          );
        }).toList();
      },
      child: child,
    );
  }
}

/// 底部弹窗式下拉菜单
/// 适用于选项较多或需要更好用户体验的场景
class StableBottomDropdownMenu<T> extends StatelessWidget {
  final Widget child;
  final List<StableDropdownMenuItem<T>> items;
  final String? title;

  const StableBottomDropdownMenu({
    Key? key,
    required this.child,
    required this.items,
    this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showBottomMenu(context),
      child: child,
    );
  }

  void _showBottomMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _BottomMenuSheet(
        items: items,
        title: title,
      ),
    );
  }
}

class _BottomMenuSheet<T> extends StatelessWidget {
  final List<StableDropdownMenuItem<T>> items;
  final String? title;

  const _BottomMenuSheet({
    required this.items,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (title != null) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                title!,
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Divider(
                height: 1, color: colorScheme.outline.withValues(alpha: 0.2)),
          ],
          ...items.map((item) {
            return ListTile(
              leading: item.icon != null
                  ? Icon(
                      item.icon,
                      color: item.isSelected
                          ? colorScheme.primary
                          : colorScheme.onSurface,
                    )
                  : null,
              title: Text(
                item.title,
                style: textTheme.bodyLarge?.copyWith(
                  color: item.isSelected
                      ? colorScheme.primary
                      : colorScheme.onSurface,
                ),
              ),
              trailing: item.isSelected
                  ? Icon(
                      Icons.check_rounded,
                      color: colorScheme.primary,
                    )
                  : null,
              onTap: () {
                Navigator.of(context).pop();
                item.onTap?.call();
              },
            );
          }).toList(),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
