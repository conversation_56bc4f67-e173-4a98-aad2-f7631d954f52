import 'dart:ui';

import 'package:flutter/material.dart';

/// 玻璃拟态容器组件
///
/// 提供高级的玻璃拟态效果，使用外部传入的原始颜色
class GlassmorphismContainer extends StatelessWidget {
  /// 子组件
  final Widget child;

  /// 渐变颜色列表
  final List<Color> gradientColors;

  /// 边框颜色
  final Color borderColor;

  /// 阴影颜色列表
  final List<Color> shadowColors;

  /// 光晕颜色（可选）
  final Color? glowColor;

  /// 高光颜色（可选）
  final Color? highlightColor;

  /// 圆角半径
  final double borderRadius;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 外边距
  final EdgeInsetsGeometry? margin;

  /// 边框宽度
  final double borderWidth;

  /// 模糊强度
  final double blurIntensity;

  /// 宽度
  final double? width;

  /// 高度
  final double? height;

  const GlassmorphismContainer({
    Key? key,
    required this.child,
    required this.gradientColors,
    required this.borderColor,
    required this.shadowColors,
    this.glowColor,
    this.highlightColor,
    this.borderRadius = 20.0,
    this.padding,
    this.margin,
    this.borderWidth = 1.5,
    this.blurIntensity = 30.0,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      child: Stack(
        children: [
          // 背景光晕效果
          if (glowColor != null) _buildGlowEffect(),

          // 主玻璃容器
          _buildGlassContainer(),

          // 顶部装饰性高光
          if (highlightColor != null) _buildTopHighlight(),
        ],
      ),
    );
  }

  /// 构建背景光晕效果
  Widget _buildGlowEffect() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius + 4),
        boxShadow: [
          BoxShadow(
            color: glowColor!,
            blurRadius: 40,
            spreadRadius: 0,
            offset: Offset(0, 0),
          ),
        ],
      ),
    );
  }

  /// 构建主玻璃容器
  Widget _buildGlassContainer() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blurIntensity, sigmaY: blurIntensity),
        child: Container(
          decoration: BoxDecoration(
            gradient: _buildGradient(),
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color: borderColor,
              width: borderWidth,
            ),
            boxShadow: _buildBoxShadows(),
          ),
          padding: padding,
          child: child,
        ),
      ),
    );
  }

  /// 构建渐变背景
  LinearGradient _buildGradient() {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: gradientColors,
    );
  }

  /// 构建阴影效果
  List<BoxShadow> _buildBoxShadows() {
    return shadowColors.asMap().entries.map((entry) {
      int index = entry.key;
      Color color = entry.value;

      // 根据索引设置不同的阴影参数
      switch (index) {
        case 0: // 主阴影
          return BoxShadow(
            color: color,
            blurRadius: 32,
            spreadRadius: 0,
            offset: Offset(0, 16),
          );
        case 1: // 内阴影效果
          return BoxShadow(
            color: color,
            blurRadius: 8,
            spreadRadius: -2,
            offset: Offset(0, 4),
          );
        case 2: // 顶部高光
          return BoxShadow(
            color: color,
            blurRadius: 2,
            spreadRadius: 0,
            offset: Offset(0, -1),
          );
        default: // 额外阴影
          return BoxShadow(
            color: color,
            blurRadius: 16,
            spreadRadius: 0,
            offset: Offset(0, 8),
          );
      }
    }).toList();
  }

  /// 构建顶部装饰性高光
  Widget _buildTopHighlight() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 1,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(borderRadius),
            topRight: Radius.circular(borderRadius),
          ),
          gradient: LinearGradient(
            colors: [
              Colors.transparent,
              highlightColor!,
              Colors.transparent,
            ],
          ),
        ),
      ),
    );
  }
}

/// 玻璃拟态容器的预设样式
class GlassmorphismPresets {
  /// 卡片样式
  static GlassmorphismContainer card({
    required BuildContext context,
    required Color primaryColor,
    required Color secondaryColor,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    required Widget child,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GlassmorphismContainer(
      gradientColors: isDark
          ? [
              Colors.white.withValues(alpha: 0.12),
              Colors.white.withValues(alpha: 0.06),
              Colors.white.withValues(alpha: 0.08),
            ]
          : [
              primaryColor.withValues(alpha: 0.85),
              secondaryColor.withValues(alpha: 0.65),
              primaryColor.withValues(alpha: 0.75),
            ],
      borderColor: isDark
          ? Colors.white.withValues(alpha: 0.2)
          : Colors.white.withValues(alpha: 0.4),
      shadowColors: [
        isDark
            ? Colors.black.withValues(alpha: 0.4)
            : Colors.black.withValues(alpha: 0.12),
        isDark
            ? Colors.black.withValues(alpha: 0.2)
            : Colors.black.withValues(alpha: 0.06),
        isDark
            ? Colors.white.withValues(alpha: 0.08)
            : Colors.white.withValues(alpha: 0.6),
      ],
      glowColor: isDark
          ? Colors.white.withValues(alpha: 0.03)
          : primaryColor.withValues(alpha: 0.2),
      highlightColor: Colors.white.withValues(alpha: isDark ? 0.3 : 0.8),
      borderRadius: 20.0,
      padding: padding ?? EdgeInsets.all(20),
      margin: margin ?? EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: child,
    );
  }

  /// 按钮样式
  static GlassmorphismContainer button({
    required BuildContext context,
    required Color primaryColor,
    required Color secondaryColor,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    required Widget child,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GlassmorphismContainer(
      gradientColors: isDark
          ? [
              Colors.white.withValues(alpha: 0.15),
              Colors.white.withValues(alpha: 0.08),
            ]
          : [
              primaryColor.withValues(alpha: 0.9),
              secondaryColor.withValues(alpha: 0.8),
            ],
      borderColor: isDark
          ? Colors.white.withValues(alpha: 0.25)
          : Colors.white.withValues(alpha: 0.5),
      shadowColors: [
        isDark
            ? Colors.black.withValues(alpha: 0.3)
            : Colors.black.withValues(alpha: 0.1),
        isDark
            ? Colors.white.withValues(alpha: 0.1)
            : Colors.white.withValues(alpha: 0.7),
      ],
      highlightColor: Colors.white.withValues(alpha: isDark ? 0.4 : 0.9),
      borderRadius: 16.0,
      padding: padding ?? EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      margin: margin,
      blurIntensity: 20.0,
      child: child,
    );
  }

  /// 简约样式
  static GlassmorphismContainer minimal({
    required BuildContext context,
    required Color primaryColor,
    required Color secondaryColor,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    required Widget child,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GlassmorphismContainer(
      gradientColors: isDark
          ? [
              Colors.white.withValues(alpha: 0.08),
              Colors.white.withValues(alpha: 0.04),
            ]
          : [
              primaryColor.withValues(alpha: 0.6),
              secondaryColor.withValues(alpha: 0.5),
            ],
      borderColor: isDark
          ? Colors.white.withValues(alpha: 0.15)
          : Colors.white.withValues(alpha: 0.3),
      shadowColors: [
        isDark
            ? Colors.black.withValues(alpha: 0.2)
            : Colors.black.withValues(alpha: 0.08),
      ],
      borderRadius: 12.0,
      padding: padding ?? EdgeInsets.all(16),
      margin: margin,
      blurIntensity: 15.0,
      child: child,
    );
  }
}
