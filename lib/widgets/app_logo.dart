import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:tasks/generated/assets.gen.dart';

class AppLogo extends StatelessWidget {
  final String? avatarUrl;
  final double size;
  final VoidCallback? onTap;

  const AppLogo({super.key, this.avatarUrl, this.size = 64, this.onTap});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    Widget logoWidget = Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(size / 4),
      ),
      clipBehavior: Clip.antiAlias,
      child: (avatarUrl != null && avatarUrl!.isNotEmpty)
          ? CachedNetworkImage(
              imageUrl: avatarUrl!,
              width: size,
              height: size,
              fit: BoxFit.cover,
              placeholder: (context, url) => Image.asset(
                Assets.images.logo.path, // 替换为你的 logo 图片路径
                width: size,
                height: size,
              ),
              errorWidget: (context, url, error) => Image.asset(
                Assets.images.logo.path, // 替换为你的 logo 图片路径
                width: size,
                height: size,
              ),
            )
          : Image.asset(
              Assets.images.logo.path, // 替换为你的 logo 图片路径
              width: size,
              height: size,
            ),
    );

    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: logoWidget,
      );
    }

    return logoWidget;
  }
}
