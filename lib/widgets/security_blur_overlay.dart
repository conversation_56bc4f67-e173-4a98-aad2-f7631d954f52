import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/services/app_lifecycle_service.dart';

/// 安全模糊遮罩组件
/// 在应用切换到后台时显示模糊效果，保护用户隐私
class SecurityBlurOverlay extends StatelessWidget {
  final Widget child;
  final Color? overlayColor;
  final Widget? customOverlayContent;
  final bool showAppIcon;
  final bool showSecurityText;

  const SecurityBlurOverlay({
    super.key,
    required this.child,
    this.overlayColor,
    this.customOverlayContent,
    this.showAppIcon = true,
    this.showSecurityText = true,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final l10n = S.of(context);

    return Observer(
      builder: (context) {
        final lifecycleService = AppLifecycleManager.instance;

        return Stack(
          children: [
            // 主要内容
            child,

            // 模糊遮罩
            if (lifecycleService.showBlurOverlay)
              AnimatedContainer(
                duration: Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: lifecycleService.blurSigma,
                    sigmaY: lifecycleService.blurSigma,
                  ),
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      color: overlayColor ??
                          colorScheme.surface.withValues(alpha: 0.8),
                    ),
                    child: customOverlayContent ??
                        _buildDefaultOverlay(
                            context, colorScheme, textTheme, l10n),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// 构建默认遮罩内容
  Widget _buildDefaultOverlay(
    BuildContext context,
    ColorScheme colorScheme,
    TextTheme textTheme,
    S l10n,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 应用图标
          if (showAppIcon) ...[
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: colorScheme.primary,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                Icons.security,
                size: 40,
                color: colorScheme.onPrimary,
              ),
            ),
            SizedBox(height: 24),
          ],

          // 安全提示文本
          if (showSecurityText) ...[
            Text(
              '极简记物',
              style: textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '为保护您的隐私，内容已隐藏',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),

            // 解锁提示
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.touch_app,
                    size: 16,
                    color: colorScheme.onPrimaryContainer,
                  ),
                  SizedBox(width: 8),
                  Text(
                    '点击返回应用',
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// 简化版模糊遮罩组件
/// 只提供基础的模糊效果，不显示额外内容
class SimpleBlurOverlay extends StatelessWidget {
  final Widget child;
  final double? blurSigma;
  final Color? overlayColor;

  const SimpleBlurOverlay({
    super.key,
    required this.child,
    this.blurSigma,
    this.overlayColor,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Observer(
      builder: (context) {
        final lifecycleService = AppLifecycleManager.instance;

        return Stack(
          children: [
            child,
            if (lifecycleService.showBlurOverlay)
              BackdropFilter(
                filter: ImageFilter.blur(
                  sigmaX: blurSigma ?? lifecycleService.blurSigma,
                  sigmaY: blurSigma ?? lifecycleService.blurSigma,
                ),
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: overlayColor ??
                      colorScheme.surface.withValues(alpha: 0.5),
                ),
              ),
          ],
        );
      },
    );
  }
}
