import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:tasks/providers/theme_provider.dart';

/// 系统UI样式包装器
///
/// 负责根据主题自动设置状态栏和导航栏样式，支持：
/// - 动态色主题和静态主题
/// - 明暗模式自动切换
/// - 状态栏和导航栏颜色同步
///
/// 使用方式：
/// ```dart
/// SystemUiWrapper(
///   child: YourPageWidget(),
/// )
/// ```
class SystemUiWrapper extends StatelessWidget {
  /// 需要包装的子组件
  final Widget child;

  const SystemUiWrapper({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, _) {
        final overlayStyle = _buildSystemUiOverlayStyle(context, themeProvider);
        
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: overlayStyle,
          child: child,
        );
      },
    );
  }

  /// 根据当前主题构建系统UI样式
  SystemUiOverlayStyle _buildSystemUiOverlayStyle(
    BuildContext context,
    ThemeProvider themeProvider,
  ) {
    final brightness = Theme.of(context).brightness;
    final seedColor = themeProvider.customTheme.seedColor;

    if (themeProvider.customTheme.enableDynamicColor) {
      return _buildDynamicColorOverlayStyle(brightness, seedColor);
    } else {
      return _buildStaticOverlayStyle(brightness);
    }
  }

  /// 构建动态色主题的系统UI样式
  SystemUiOverlayStyle _buildDynamicColorOverlayStyle(
    Brightness brightness,
    Color seedColor,
  ) {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: brightness,
    );

    return SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: brightness == Brightness.dark
          ? Brightness.light
          : Brightness.dark,
      systemNavigationBarColor: colorScheme.surface,
      systemNavigationBarIconBrightness: brightness,
    );
  }

  /// 构建静态主题的系统UI样式
  SystemUiOverlayStyle _buildStaticOverlayStyle(Brightness brightness) {
    return brightness == Brightness.dark
        ? const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            systemNavigationBarColor: Colors.black,
            systemNavigationBarIconBrightness: Brightness.light,
          )
        : const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
          );
  }
}

/// 扩展方法：为任何Widget添加系统UI样式包装
extension SystemUiWrapperExtension on Widget {
  /// 为当前Widget包装系统UI样式
  ///
  /// 使用方式：
  /// ```dart
  /// MyWidget().withSystemUi()
  /// ```
  Widget withSystemUi() {
    return SystemUiWrapper(child: this);
  }
}
