import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';

class SearchAppbar extends StatelessWidget implements PreferredSizeWidget {
  final String hintText;
  final String searchText;
  final ValueChanged<String> onChange;
  final ValueChanged<String>? onSubmitted;
  final FocusNode? focusNode;

  const SearchAppbar(
      {super.key,
      required this.hintText,
      required this.searchText,
      required this.onChange,
      this.onSubmitted,
      this.focusNode});

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    TextTheme textTheme = Theme.of(context).textTheme;
    S l10n = S.of(context);
    return AppBar(
      leadingWidth: 24,
      backgroundColor: colorScheme.surface,
      flexibleSpace: Container(
        color: colorScheme.surface,
      ),
      elevation: 0,
      scrolledUnderElevation: 0,
      title: Container(
        height: 40,
        alignment: Alignment.center,
        child: ClearInputTextField(
          icon: Icons.search,
          autoFocus: true,
          value: searchText,
          hintText: hintText,
          onChange: onChange,
          onSubmitted: onSubmitted,
          focusNode: focusNode,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(20)),
            borderSide: BorderSide.none,
          ),
          fillColor: colorScheme.surfaceContainer,
        ),
      ),
      actions: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            onSubmitted?.call(searchText);
          },
          child: Padding(
            padding:
                const EdgeInsets.only(left: 0, right: 16, top: 12, bottom: 12),
            child: Text(
              l10n.confirm,
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.primary,
              ),
            ),
          ),
        )
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
