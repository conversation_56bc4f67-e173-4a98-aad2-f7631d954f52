import 'package:flutter/material.dart';

class AdaptiveDialogPage<T> extends Page<T> {
  final Offset? anchorPoint;
  final Color? barrierColor;
  final bool barrierDismissible;
  final String? barrierLabel;
  final bool useSafeArea;
  final CapturedThemes? themes;
  final WidgetBuilder builder;

  AdaptiveDialogPage({
    required this.builder,
    this.anchorPoint,
    this.barrierColor = Colors.black54,
    this.barrierDismissible = true,
    this.barrierLabel,
    this.useSafeArea = true,
    this.themes,
    super.key,
    super.name,
    super.arguments,
    super.restorationId,
  });

  @override
  AdaptiveDialogRoute<T> createRoute(BuildContext context) {
    return AdaptiveDialogRoute<T>(
        context: context,
        settings: this,
        builder: builder,
        anchorPoint: anchorPoint,
        barrierColor: barrierColor,
        barrierDismissible: barrierDismissible,
        barrierLabel: barrierLabel,
        useSafeArea: useSafeArea,
        themes: themes);
  }
}

class AdaptiveDialogRoute<T> extends DialogRoute<T> {
  final double? maxHeightFactor;
  final double? minHeight;
  final EdgeInsets padding;
  final WidgetBuilder _builder; // 添加这个字段

  AdaptiveDialogRoute({
    required BuildContext context,
    required WidgetBuilder builder, // 不再使用 super.builder
    super.settings,
    super.anchorPoint,
    super.barrierColor,
    super.barrierDismissible,
    super.barrierLabel,
    super.useSafeArea,
    super.themes,
    this.maxHeightFactor,
    this.minHeight,
    this.padding = EdgeInsets.zero,
  })  : _builder = builder,
        // 初始化_builder
        super(context: context, builder: builder) {} // 显式调用父类构造函数

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    final Widget dialog = _builder(context); // 使用_builder而不是builder
    final MediaQueryData mediaQuery = MediaQuery.of(context);

    return MediaQuery.removePadding(
      context: context,
      removeTop: true,
      removeBottom: true,
      removeLeft: true,
      removeRight: true,
      child: Builder(
        builder: (BuildContext context) {
          return CustomSingleChildLayout(
            delegate: _AdaptiveDialogLayout(
              mediaQuery: mediaQuery,
              padding: padding,
              maxHeightFactor: maxHeightFactor ?? 0.9,
              minHeight: minHeight,
            ),
            child: dialog,
          );
        },
      ),
    );
  }
}

class _AdaptiveDialogLayout extends SingleChildLayoutDelegate {
  final MediaQueryData mediaQuery;
  final EdgeInsets padding;
  final double maxHeightFactor;
  final double? minHeight;

  _AdaptiveDialogLayout({
    required this.mediaQuery,
    required this.padding,
    required this.maxHeightFactor,
    this.minHeight,
  });

  @override
  BoxConstraints getConstraintsForChild(BoxConstraints constraints) {
    final double maxHeight = mediaQuery.size.height * maxHeightFactor;
    final double minHeight = this.minHeight ?? 0.0;

    return BoxConstraints(
      maxWidth: constraints.maxWidth - padding.horizontal,
      minWidth: constraints.maxWidth - padding.horizontal,
      maxHeight: maxHeight - padding.vertical,
      minHeight: minHeight > 0 ? minHeight - padding.vertical : 0.0,
    );
  }

  @override
  Offset getPositionForChild(Size size, Size childSize) {
    return Offset(
      padding.left,
      size.height - childSize.height - padding.bottom,
    );
  }

  @override
  bool shouldRelayout(_AdaptiveDialogLayout oldDelegate) {
    return oldDelegate.mediaQuery != mediaQuery ||
        oldDelegate.padding != padding ||
        oldDelegate.maxHeightFactor != maxHeightFactor ||
        oldDelegate.minHeight != minHeight;
  }
}
