import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tasks/utils/platform.dart';

class AppTheme {
  // 定义主色调和辅助色
  static const Color primaryColor = Color(0xFF0092FF);
  static const Color primaryDarkColor = Color(0xFF007AD6);
  static const Color errorColor = Color(0xFFE53935);

  static ThemeData buildTheme(BuildContext context,
      {required bool isDynamic,
      required Color seedColor,
      required Brightness brightness}) {
    if (!isDynamic) {
      final curTheme = brightness == Brightness.dark ? darkTheme : lightTheme;
      return curTheme;
    }
    final colorScheme =
        ColorScheme.fromSeed(seedColor: seedColor, brightness: brightness);

    return ThemeData(
            useMaterial3: true,
            platform: isIos() ? TargetPlatform.iOS : TargetPlatform.android,
            // 沉浸的关键代码
            appBarTheme: AppBarTheme(
              systemOverlayStyle: SystemUiOverlayStyle(
                // 去除状态栏遮罩
                statusBarColor: Colors.transparent,
                statusBarBrightness: brightness == Brightness.dark
                    ? Brightness.dark
                    : Brightness.light,
                statusBarIconBrightness: brightness == Brightness.dark
                    ? Brightness.light
                    : Brightness.dark,
                // 状态栏图标字体颜色
                systemNavigationBarColor: colorScheme.surface,
                // 底部导航栏颜色
                systemNavigationBarIconBrightness: brightness == Brightness.dark
                    ? Brightness.light
                    : Brightness.dark, // 底部导航栏图标颜色
              ),
              backgroundColor: Colors.white,
              elevation: 0,
            ),
            textTheme:
                brightness == Brightness.light ? lightTextTheme : darkTextTheme,
            colorScheme: colorScheme)
        .useSystemChineseFont(brightness);
  }

  static ThemeData lightTheme = ThemeData(
    platform: isIos() ? TargetPlatform.iOS : TargetPlatform.android,
    brightness: Brightness.light,
    useMaterial3: true,
    // 沉浸的关键代码
    appBarTheme: const AppBarTheme(
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        // 去除状态栏遮罩
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        // 状态栏图标字体颜色
        systemNavigationBarColor: Colors.black,
        // 底部导航栏颜色
        systemNavigationBarIconBrightness: Brightness.dark, // 底部导航栏图标颜色
      ),
      backgroundColor: Colors.white,
      elevation: 0,
    ),
    colorScheme: ColorScheme.light(
      primary: primaryColor,
      primaryContainer: primaryColor.withValues(alpha: 0.4),
      onPrimaryContainer: primaryDarkColor,
      secondary: primaryColor,
      onPrimary: Colors.white,
      surface: Colors.white,
      surfaceContainer: Color(0xFFF4F4F4),
      // 纯白色
      inverseSurface: Color(0xFF303030),
      onInverseSurface: Colors.white,
      surfaceContainerHigh: Color(0xFFEEEEEE),
      error: errorColor,
      onSurface: Color(0xFF202020),
      onSurfaceVariant: Color(0xFF505050),
      outline: Color(0xFFE0E0E0),
      outlineVariant: Color(0xFFEAEAEA),
      secondaryContainer: Color(0xFFF2F2F7),
    ),
    // 浅灰背景色 #f4f4f4
    textTheme: lightTextTheme,
  );

  static ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    useMaterial3: true,
    platform: isIos() ? TargetPlatform.iOS : TargetPlatform.android,
    // 沉浸的关键代码
    appBarTheme: const AppBarTheme(
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        // 去除状态栏遮罩
        statusBarBrightness: Brightness.dark,
        statusBarIconBrightness: Brightness.light,
        // 状态栏图标字体颜色
        systemNavigationBarColor: Colors.white,
        // 底部导航栏颜色
        systemNavigationBarIconBrightness: Brightness.light, // 底部导航栏图标颜色
      ),
      backgroundColor: Colors.black,
      elevation: 0,
    ),
    colorScheme: ColorScheme.dark(
      primary: primaryColor,
      primaryContainer: primaryColor.withOpacity(0.15),
      onPrimaryContainer: primaryColor.withOpacity(0.8),
      secondary: primaryColor,
      onPrimary: Colors.white,
      surface: Colors.black,
      onSurface: Colors.white,
      surfaceContainer: Color(0xFF1A1A1C),
      // 深灰色 #1a1a1c
      surfaceBright: Colors.black,
      surfaceContainerHigh: Color(0xFF2A2A2C),
      error: errorColor,
      onSurfaceVariant: Colors.white70,
      outline: Color(0xFF3A3A3A),
      outlineVariant: Color(0xFF505050),
      secondaryContainer: Colors.white60,
    ),
    // 纯黑色背景
    textTheme: darkTextTheme,
  );

  static TextStyle text21 = TextStyle(
    fontSize: 21,
    height: 1.4,
    fontWeight: FontWeight.w500,
    color: Colors.black.withValues(alpha: (0.85)),
  );

  static TextStyle text17 = TextStyle(
    fontSize: 17,
    height: 1.4,
    fontWeight: FontWeight.w500,
    color: Colors.black.withValues(alpha: (0.85)),
  );

  static TextStyle text15 = TextStyle(
    fontSize: 15,
    height: 1.4,
    fontWeight: FontWeight.w500,
    color: Colors.black.withValues(alpha: (0.85)),
  );

  static TextStyle text13 = TextStyle(
    fontSize: 13,
    height: 1.4,
    fontWeight: FontWeight.w500,
    color: Colors.black.withValues(alpha: (0.85)),
  );

  static TextTheme lightTextTheme = TextTheme(
    headlineMedium: text21,
    titleLarge: text17,
    titleMedium: text15,
    titleSmall: text13,
    bodyLarge: text17.copyWith(
        color: Colors.black.withValues(alpha: (0.65)),
        fontWeight: FontWeight.w500),
    bodyMedium: text15.copyWith(
        color: Colors.black.withValues(alpha: (0.65)),
        fontWeight: FontWeight.w500),
    bodySmall: text13.copyWith(
        color: Colors.black.withValues(alpha: (0.65)),
        fontWeight: FontWeight.w500),
    labelLarge: text17.copyWith(
        color: Colors.black.withValues(alpha: (0.45)),
        fontWeight: FontWeight.w400),
    labelMedium: text15.copyWith(
        color: Colors.black.withValues(alpha: (0.45)),
        fontWeight: FontWeight.w400),
    labelSmall: text13.copyWith(
        color: Colors.black.withValues(alpha: (0.45)),
        fontWeight: FontWeight.w400),
  ).useSystemChineseFont(Brightness.light);

  static TextTheme darkTextTheme = TextTheme(
    headlineMedium:
        text21.copyWith(color: Colors.white.withValues(alpha: (0.85))),
    titleLarge: text17.copyWith(color: Colors.white.withValues(alpha: (0.85))),
    titleMedium: text15.copyWith(color: Colors.white.withValues(alpha: (0.85))),
    titleSmall: text13.copyWith(color: Colors.white.withValues(alpha: (0.85))),
    bodyLarge: text17.copyWith(
        color: Colors.white.withValues(alpha: (0.65)),
        fontWeight: FontWeight.w500),
    bodyMedium: text15.copyWith(
        color: Colors.white.withValues(alpha: (0.65)),
        fontWeight: FontWeight.w500),
    bodySmall: text13.copyWith(
        color: Colors.white.withValues(alpha: (0.65)),
        fontWeight: FontWeight.w500),
    labelLarge: text17.copyWith(
        color: Colors.white.withValues(alpha: (0.45)),
        fontWeight: FontWeight.w400),
    labelMedium: text15.copyWith(
        color: Colors.white.withValues(alpha: (0.45)),
        fontWeight: FontWeight.w400),
    labelSmall: text13.copyWith(
        color: Colors.white.withValues(alpha: (0.45)),
        fontWeight: FontWeight.w400),
  ).useSystemChineseFont(Brightness.dark);
}
