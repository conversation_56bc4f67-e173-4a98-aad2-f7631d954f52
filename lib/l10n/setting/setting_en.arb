{"settingGenerateSetting": "General Setting", "settingAccountManage": "Account and security", "settingCheckVersion": "Check for Update", "settingNoUpdate": "No update yet", "settingStatisticalSettings": "Statistical Setting", "settingRetiredAsset": "Exclude Retired Assets from Total", "settingAppearance": "Appearance", "settingAppearanceMode": "Appearance Mode", "settingCustomTheme": "Custom Theme", "settingDynamicColor": "Dynamic Color", "settingSeedColor": "Seed Color", "settingSupport": "Support & Feedback", "settingFeedback": "<PERSON><PERSON><PERSON>", "settingOfficialWebsite": "Official Website", "settingShare": "Share with Friends", "settingShare2FriendTips": "I found a great asset management app. Check it out! {shareUrl}", "@settingShare2FriendTips": {"placeholders": {"shareUrl": {"type": "String"}}}, "settingDonate": "Donate", "settingAboutMe": "About Me", "settingFollowMe": "Follow <PERSON>", "settingLogoutTips": "Log out?", "settingLogout": "Log Out", "settingSystemAppearance": "Follow System", "settingAppearanceLight": "Light Mode", "settingAppearanceDark": "Dark Mode", "settingLanguage": "Language", "settingLanguageSetting": "Language setting", "settingSelectLanguage": "Select Language", "settingSystemLanguage": "Follow System", "settingStatisticalColorStart": "Statistics Gradient Start", "settingStatisticalColorEnd": "Statistics Gradient End", "settingNormalColorStart": "Normal Card Gradient Start", "settingNormalColorEnd": "Normal Card Gradient End", "settingCollectColorStart": "Favorite Card Gradient Start", "settingCollectColorEnd": "Favorite Card Gradient End", "settingRetriedColorStart": "Retired Card Gradient Start", "settingRetriedColorEnd": "Retired Card Gradient End", "settingCustomThemeSaved": "Theme saved", "settingCustomThemeResetTips": "Confirm reset topic?", "settingCustomThemeResetSuccess": "Theme has been reset", "settingWebdavServerUrl": "Please enter server URL", "settingWebdavUsername": "Please enter username", "settingWebdavPassword": "Please enter password", "settingWebdavSaveFailed": "Save failed", "settingWebdavConfigIncomplete": "Please fill in complete WebDAV configuration", "settingWebdavTestFailed": "Connection test failed", "settingWebdavTestSuccess": "Connection test successful", "settingWebdavConfig": "WebDAV Configuration", "settingWebdavServerConfig": "Server Configuration", "settingWebdavServerUrlHint": "Server URL", "settingWebdavUsernameHint": "Username", "settingWebdavPasswordHint": "Password", "settingWebdavDirectoryHint": "File Directory", "settingWebdavTestConnection": "Test", "settingWebdavSaveConfig": "Save", "settingCacheWebdavBackup": "WebDAV Backup", "settingCacheCloudBackup": "Cloud Backup", "settingCacheRestoreConfirm": "Confirm to overwrite data with current version?", "settingCacheDeleteConfirm": "Confirm to delete this version?", "settingCacheBackupToServerConfirm": "Backup current data to server? Daily limit 3 times.Note that custom pictures cannot be backed up across devices. If you need to back up images, please use WebDav to back up!", "settingCacheBackupToServer": "Backup to Server", "settingCacheBackupToWebdavConfirm": "Backup current data to WebDAV?", "settingCacheBackupToWebdav": "Backup to WebDAV", "settingThemeShare": "Share", "settingThemeImport": "Import", "settingThemeImportSuccess": "Import shared configuration successful", "settingThemeImportFailed": "Import failed", "settingThemePureWhite": "Pure White", "settingThemePureBlack": "Pure Black", "settingThemeClassic": "Classic", "settingCurrencySetting": "<PERSON><PERSON><PERSON><PERSON>", "settingCurrencyUpdated": "Currency setting updated", "settingCustomCurrency": "Custom Currency", "settingCurrencyCodeLabel": "Currency Code", "settingCurrencyCodeHint": "e.g.: USD, CNY, etc.", "settingCurrencySymbolLabel": "Currency Symbol", "settingCurrencySymbolHint": "e.g.: $, ¥, etc.", "settingCurrencyCodeError": "Currency code cannot be empty and must not exceed 10 characters", "settingCurrencySymbolError": "Currency symbol cannot be empty and must not exceed 5 characters", "settingSave": "Save", "settingCustomCurrencyHint": "Tap to create custom currency", "settingCancel": "Cancel"}