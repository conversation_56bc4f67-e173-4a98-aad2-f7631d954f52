{"homeSplashTips": "Let recording become your long-term habit", "homeSplashPrivacy1": "Welcome to \"{name}\". We highly value your personal information and privacy protection. Before using \"{name}\" services, please carefully read our Privacy Policy and User Agreement.", "@homeSplashPrivacy1": {"placeholders": {"name": {"type": "String"}}}, "homeSplashPrivacy2": "We will use your personal information in accordance with the terms you agree to, in order to provide you with services.", "homeSplashPrivacy3": "If you agree to this policy, please click \"Agree\" and start using our products and services. We will do our utmost to protect your personal information security.", "homePrivacyDesc": "让记录，成为你的长期习惯", "homeEmptyListTitle": "No Assets", "homeEmptyListDesc": "Please add assets first", "homeEmptyStarListTitle": "No Favorites Yet", "homeEmptyStarListDesc": "Swipe right to add assets to favorites", "homeWarrantyCountdown": "{day} days out of warranty", "@homeWarrantyCountdown": {"placeholders": {"day": {"type": "String"}}}, "homeStatusExpired": "Expired", "homeStatusRetired": "Retired", "homeDeleteAssetConfirmation": "Are you sure to delete {name}?", "@homeDeleteAssetConfirmation": {"placeholders": {"name": {"type": "String"}}}, "homeDeleteSuccess": "Delete Success!", "homeMyAsset": "Assets", "homeSearchTips": "Asset Name", "homeTotalAsset": "Total Assets", "homeDailyPrice": "Total Daily Price", "homeFilterAll": "All", "homeFilterInService": "In Service", "homeFilterNotInService": "Retired", "homeFilterCollect": "Favorites", "homeFilterNoCategory": "Uncategorized", "homeCardDayFormat": "{day} days", "@homeCardDayFormat": {"placeholders": {"day": {"type": "String"}}}, "homeSearchIcon": "Search", "homeSearchAssetHint": "Enter asset name or notes", "searchHistoryTitle": "Search History", "searchHistoryEmpty": "No search history", "searchHistoryEmptyDesc": "Your search history will appear here", "searchClearHistory": "Clear", "searchClearHistoryConfirm": "Clear Search History", "searchClearHistoryConfirmDesc": "Are you sure you want to clear all search history?", "homeTotalAssetValue": "Total Asset Value", "homeAssetCount": "Asset Count", "homeMaxDaily": "Max Daily", "homeMinDaily": "Min Daily", "homeShowAmount": "Show Amount", "homeHideAmount": "<PERSON><PERSON> Amount", "homeAmountHidden": "****", "homeMore": "More", "homeAssetSettings": "<PERSON><PERSON> Settings", "homeBatchManagement": "Batch Management", "homeBatchSelectAll": "Select All", "homeBatchCancelAll": "Cancel All", "homeBatchSort": "Sort", "homeBatchSelectedCount": "Selected {count} assets", "@homeBatchSelectedCount": {"placeholders": {"count": {"type": "int"}}}, "homeBatchCategory": "Category", "homeBatchRetire": "Retire", "homeBatchDeleteConfirm": "Are you sure to delete {count} selected assets?", "@homeBatchDeleteConfirm": {"placeholders": {"count": {"type": "int"}}}, "homeBatchRetireConfirm": "Are you sure to retire {count} selected assets?", "@homeBatchRetireConfirm": {"placeholders": {"count": {"type": "int"}}}, "homeBatchCategorySuccess": "Category updated successfully", "homeBatchRetireSuccess": "Batch retirement successful", "homeBatchSelectCategory": "Select Category", "homeBatchUncategorized": "Uncategorized"}