{"tips": "Reminder", "tipsContent": "This is Tips Content", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "reset": "Reset", "saveAndExit": "Save&Exit", "confirmDeleteTips": "Confirm delete?", "commonAdd": "Add", "commonSave": "Save", "commonClear": "Clear", "commonReset": "Reset", "commonHome": "Home", "commonStar": "Star", "commonMine": "Mine", "commonDelimiter": " · ", "commonAll": "All", "commonAgree": "Agree", "commonDisagree": "Disagree", "commonPleaseSelect": "Please Select", "commonDayFormat": "{days} Days", "@commonDayFormat": {"placeholders": {"days": {"type": "String"}}}, "commonMonthFormat": "{days} months", "@commonMonthFormat": {"placeholders": {"days": {"type": "String"}}}, "commonYearFormat": "{days} years", "@commonYearFormat": {"placeholders": {"days": {"type": "String"}}}, "commonYearsFormat": "more than 3 years", "commonTimeFormat": "{times} times", "@commonTimeFormat": {"placeholders": {"times": {"type": "String"}}}, "commonOfDay": "{days}/day", "@commonOfDay": {"placeholders": {"days": {"type": "String"}}}, "commonOfCount": "{count} Per Use", "@commonOfCount": {"placeholders": {"count": {"type": "String"}}}, "commonEmptyContent": "No data yet", "commonLifetimeMember": "Lifetime Member", "commonPrivacyPolicy": "Privacy Policy", "commonServiceAgreement": "Service Agreement", "commonBackTips": "Press again to exit the application", "commonImageType": "Image Type", "commonImageTypeCamera": "Take a photo", "commonImageTypeGallery": "Select from gallery", "commonImageTypeIconLibrary": "Official icons (hundreds of types)", "commonCategoryName": "Category Name", "commonCategoryNameHint": "Please enter category name", "commonSelectIcon": "Select Icon", "commonIcon": "Icon", "commonPleaseInput": "Please Input", "commonInput": "Input", "commonCopied": "<PERSON>pied", "commonClipboardEmpty": "Clipboard is empty", "exceptionDialogTitle": "Application Exception", "exceptionDialogDescription": "The application encountered an unexpected error. You can choose to report this exception to help us improve the app.", "exceptionDialogErrorType": "Error Type", "exceptionDialogErrorMessage": "Error Message", "exceptionDialogStackTrace": "Stack Trace", "exceptionDialogShowDetails": "Show Details", "exceptionDialogHideDetails": "Hide Details", "exceptionDialogCopy": "Copy Info", "exceptionDialogClose": "Close", "periodDatePickerOneMonth": "1 Month", "periodDatePickerThreeMonths": "3 Months", "periodDatePickerSixMonths": "6 Months", "periodDatePickerNineMonths": "9 Months", "periodDatePickerOneYear": "1 Year", "periodDatePickerThreeYears": "3 Years", "periodDatePickerCustom": "Custom"}