{"assetAssetNameInputPlaceholder": "请输入资产名称", "assetPriceInputPlaceholder": "请输入购买价格", "assetPriceValid": "请输入有效的购买价格", "assetPurchaseDate": "购买日期", "assetSelectCategory": "选择分类", "assetSelectIcon": "选择图标", "assetLimitTips": "资产数量已达到上限，升级vip即享无限资产", "assetEditAsset": "编辑资产", "assetBasicInfo": "基本信息", "assetWarrantyStatus": "保修状态", "assetSetWarrantyStatus": "设置保修状态", "assetWarrantyExpirationDate": "保修截止日期", "assetSelectPriceMethod": "选择计价方式", "assetPriceMethodDefault": "日均计价", "assetPriceMethodNoPrice": "不计价", "assetPriceMethodUseCount": "使用次数", "assetPriceMethodCycle": "循环计价", "assetDailyPriceLabel": "日均价格", "assetSetDailyPrice": "指定日均价格", "assetDailyPricePlaceHolder": "请输入日均价格（未填写自动计算）", "assetDailyPriceValid": "请输入有效的日均价格", "assetUsageCountValid": "请输入有效的使用次数", "assetExpectedRetirementDate": "预计退役日期", "assetCycleValueType": "请选择循环类型", "assetCycleValueTypeValid": "请选择循环类型", "assetCycleValueTypeDay": "日循环", "assetCycleValueTypeWeek": "周循环", "assetCycleValueTypeMonth": "月循环", "assetCycleValueTypeYear": "年循环", "assetCycleValuePlaceHolder": "请输入循环计价量", "assetCycleValueValid": "请输入有效的循环计价量", "assetCycleCountFormat": "计价{count}次", "@assetCycleCountFormat": {"placeholders": {"count": {"type": "int"}}}, "assetCycleNextDays": "{days}天后续费", "@assetCycleNextDays": {"placeholders": {"days": {"type": "int"}}}, "assetCyclePayNow": "续费日", "assetRetirementFormat": "{days}后退役", "@assetRetirementFormat": {"placeholders": {"days": {"type": "String"}}}, "assetServiceStatus": "服役状态", "assetRetirementDate": "退役日期", "assetSecondhandPricePlaceholder": "请输入二手价格", "assetSecondhandPriceValid": "请输入有效的二手价格", "assetExtra": "额外信息", "assetBuyChannelPlaceholder": "输入购买渠道", "assetBuyChannelLabel": "购买渠道", "assetBuyChannelSelect": "选择购买渠道", "assetBuyChannelManage": "渠道管理", "assetBuyChannelAdd": "添加渠道", "assetBuyChannelEdit": "编辑渠道", "assetBuyChannelName": "渠道名称", "assetBuyChannelNamePlaceholder": "请输入渠道名称", "assetBuyChannelNameRequired": "渠道名称不能为空", "assetBuyChannelDeleteConfirm": "确定删除渠道？", "assetBuyChannelAddSuccess": "添加渠道成功", "assetBuyChannelEditSuccess": "修改渠道成功", "assetBuyChannelNone": "不选择", "assetStorageLocationPlaceholder": "输入存放位置", "assetStorageLocationLabel": "存放位置", "assetStorageLocationSelect": "选择存放位置", "assetStorageLocationManage": "位置管理", "assetStorageLocationAdd": "添加位置", "assetStorageLocationEdit": "编辑位置", "assetStorageLocationName": "位置名称", "assetStorageLocationNamePlaceholder": "请输入位置名称", "assetStorageLocationNameRequired": "位置名称不能为空", "assetStorageLocationDeleteConfirm": "确定删除位置？", "assetStorageLocationAddSuccess": "添加位置成功", "assetStorageLocationEditSuccess": "修改位置成功", "assetStorageLocationNone": "不选择", "assetNotePlaceholder": "添加备注信息", "assetRemarkLabel": "备注", "assetRemarkEdit": "编辑备注", "assetRemarkHistory": "历史备注", "assetRemarkPlaceholder": "请输入备注信息...", "assetSelectPricingMethod": "计价方式", "assetPricingMethodDaily": "日均计价", "assetPricingMethodTimes": "使用次数计价", "assetTimesLabel": "使用次数", "assetTimesPlaceholder": "请输入使用次数", "assetUsageAllCount": "请输入预计使用次数", "assetAddSuccess": "添加成功，可继续添加下一个资产！", "assetAddRetentionTips": "还有未保存的数据，是否退出？", "assetEditRetentionTips": "还有未保存的数据，是否保存后退出？", "assetAdditionalLabel": "附加费用", "assetAdditionalItemsName": "额外项名称", "assetAdditionalItemsFeesNumber": "输入费用值", "assetAdditionalItemsIncome": "收入", "assetAdditionalItemsConsume": "支出", "assetAdditionalItemsTypeLabel": "费用类型", "assetAdditionalItemsNameLabel": "费用名", "assetAdditionalItemsNamePlaceholder": "请输入费用名", "assetAdditionalItemsAmountLabel": "费用金额", "assetAdditionalItemsAmountPlaceholder": "请输入费用金额", "assetAdditionalItemsDateLabel": "费用日期", "assetAdditionalItemsRemarkLabel": "备注", "assetAdditionalItemsRemarkPlaceholder": "请输入备注", "assetAdditionalItemsEmptyTips": "还没添加附加费用哦", "assetAdditionalItemsCount": "{count} 项附加费用", "@assetAdditionalItemsCount": {"placeholders": {"count": {"type": "int"}}}, "assetAdditionalManageTitle": "管理附加费用", "assetAdditionalManageTips": "支出和收入分别管理，可设置是否计入物品总价值", "assetAdditionalIncludeInTotalLabel": "计入物品总价值", "assetAdditionalIncludeInTotalHint": "开启后此费用将计入资产的总价值计算", "assetDefaultIncludeExtraFeesInTotal": "附加费用默认计入总价值", "assetDefaultIncludeExtraFeesInTotalHint": "新建附加费用时的默认设置", "assetAdditionalIncludeInTotalShort": "计入", "assetAdditionalIncludeInTotalSectionTitle": "计入总价值设置", "assetAdditionalIncludeInTotalSectionHint": "选择哪些附加费用计入资产的总价值计算", "assetExampleDailyPrice": "（示例）默认日均计价", "assetExampleStar": "（示例）右滑收藏资产", "assetExampleRetirement": "（示例）左滑退役资产", "assetExampleUsagePrice": "（示例）使用次数计价", "assetExampleCyclePrice": "（示例）周期计价", "assetExampleNoPrice": "（示例）无计价", "assetCacheTips": "您的资产信息已更新，是否要保存云端（需先登录）？", "assetRecycleBin": "回收站", "assetRecycleBinEmpty": "回收站为空", "assetRecycleBinEmptyDesc": "删除的资产会暂时保存在这里", "assetRecycleBinRestore": "恢复", "assetRecycleBinDelete": "永久删除", "assetRecycleBinRestoreConfirm": "确定要恢复这个资产吗？", "assetRecycleBinDeleteConfirm": "确定要永久删除这个资产吗？删除后无法恢复", "assetRecycleBinRestoreSuccess": "资产已恢复", "assetRecycleBinDeleteSuccess": "资产已永久删除", "assetRecycleBinBatchRestore": "批量恢复", "assetRecycleBinBatchDelete": "批量删除", "assetRecycleBinBatchRestoreConfirm": "确定要恢复选中的 {count} 个资产吗？", "assetRecycleBinBatchDeleteConfirm": "确定要永久删除选中的 {count} 个资产吗？删除后无法恢复", "assetRecycleBinDeletedAt": "删除时间"}