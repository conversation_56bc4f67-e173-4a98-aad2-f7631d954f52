{"assetAssetNameInputPlaceholder": "Please enter asset name", "assetPriceInputPlaceholder": "Please enter asset price", "assetPriceValid": "Please enter a valid purchase price", "assetPurchaseDate": "Purchase Date", "assetSelectCategory": "Category", "assetSelectIcon": "Icon", "assetLimitTips": "The number of assets has reached the upper limit. Upgrade to VIP to enjoy unlimited assets", "assetEditAsset": "Edit Asset", "assetBasicInfo": "Basic Info", "assetWarrantyStatus": "Warranty Status", "assetSetWarrantyStatus": "Set Warranty Status", "assetWarrantyExpirationDate": "Expiration Date", "assetSelectPriceMethod": "Pricing type", "assetPriceMethodDefault": "Daily Price", "assetPriceMethodNoPrice": "No price", "assetPriceMethodUseCount": "Number of usages", "assetPriceMethodCycle": "Circular Pricing", "assetDailyPriceLabel": "Daily Price", "assetSetDailyPrice": "Specified Daily Price", "assetDailyPricePlaceHolder": "Daily price (automatically calculated if blank)", "assetDailyPriceValid": "Please enter a valid average daily price", "assetUsageCountValid": "Please enter the valid number of usage", "assetExpectedRetirementDate": "Expected Date", "assetCycleValueType": "Select the cycle type", "assetCycleValueTypeValid": "Select the cycle type", "assetCycleValueTypeDay": "Daily", "assetCycleValueTypeWeek": "Weekly", "assetCycleValueTypeMonth": "Monthly", "assetCycleValueTypeYear": "Yearly", "assetCycleValuePlaceHolder": "Please enter the cycle valuation amount", "assetCycleValueValid": "Please enter a valid cycle valuation amount", "assetCycleCountFormat": "{count} times", "@assetCycleCountFormat": {"placeholders": {"count": {"type": "int"}}}, "assetCycleNextDays": "{days} days", "@assetCycleNextDays": {"placeholders": {"days": {"type": "int"}}}, "assetCyclePayNow": "<PERSON>", "assetRetirementFormat": "{days} Retired", "@assetRetirementFormat": {"placeholders": {"days": {"type": "String"}}}, "assetServiceStatus": "Service Status", "assetRetirementDate": "Retirement Date", "assetSecondhandPricePlaceholder": "Second-hand Price", "assetSecondhandPriceValid": "Please enter a valid second-hand price", "assetExtra": "Extra", "assetBuyChannelPlaceholder": "Buy Channel", "assetBuyChannelLabel": "Buy Channel", "assetBuyChannelSelect": "Select Buy Channel", "assetBuyChannelManage": "Channel Management", "assetBuyChannelAdd": "Add Channel", "assetBuyChannelEdit": "Edit Channel", "assetBuyChannelName": "Channel Name", "assetBuyChannelNamePlaceholder": "Please enter channel name", "assetBuyChannelNameRequired": "Channel name cannot be empty", "assetBuyChannelDeleteConfirm": "Are you sure to delete this channel?", "assetBuyChannelAddSuccess": "Channel added successfully", "assetBuyChannelEditSuccess": "Channel updated successfully", "assetBuyChannelNone": "None", "assetStorageLocationPlaceholder": "Storage Location", "assetStorageLocationLabel": "Storage Location", "assetStorageLocationSelect": "Select Storage Location", "assetStorageLocationManage": "Location Management", "assetStorageLocationAdd": "Add Location", "assetStorageLocationEdit": "Edit Location", "assetStorageLocationName": "Location Name", "assetStorageLocationNamePlaceholder": "Please enter location name", "assetStorageLocationNameRequired": "Location name cannot be empty", "assetStorageLocationDeleteConfirm": "Are you sure to delete this location?", "assetStorageLocationAddSuccess": "Location added successfully", "assetStorageLocationEditSuccess": "Location updated successfully", "assetStorageLocationNone": "None", "assetNotePlaceholder": "Add notes", "assetRemarkLabel": "Remark", "assetRemarkEdit": "Edit Remark", "assetRemarkHistory": "Remark History", "assetRemarkPlaceholder": "Please enter remark...", "assetSelectPricingMethod": "Pricing Type", "assetPricingMethodDaily": "Daily Price", "assetPricingMethodTimes": "Number of usages", "assetTimesLabel": "Number of usages", "assetTimesPlaceholder": "Number of usages", "assetUsageAllCount": "All usages", "assetAddSuccess": "Added successfully, continue to add the next asset!", "assetAddRetentionTips": "There is still unsaved data, will you exit?", "assetEditRetentionTips": "There is still unsaved data, will it be saved and exited?", "assetAdditionalLabel": "Additional Fees", "assetAdditionalItemsName": "Additional", "assetAdditionalItemsFeesNumber": "Cost Value", "assetAdditionalItemsIncome": "Income", "assetAdditionalItemsConsume": "Consume", "assetAdditionalItemsTypeLabel": "Cost Type", "assetAdditionalItemsNameLabel": "Fee Name", "assetAdditionalItemsNamePlaceholder": "Please enter a fee name", "assetAdditionalItemsAmountLabel": "Fee amount", "assetAdditionalItemsAmountPlaceholder": "Please enter the fee amount", "assetAdditionalItemsDateLabel": "Fee date", "assetAdditionalItemsRemarkLabel": "Remark", "assetAdditionalItemsRemarkPlaceholder": "Please enter a note", "assetAdditionalItemsEmptyTips": "Not add extra fees yet", "assetAdditionalItemsCount": "{count} additional fees", "@assetAdditionalItemsCount": {"placeholders": {"count": {"type": "int"}}}, "assetAdditionalManageTitle": "Manage Additional Fees", "assetAdditionalManageTips": "Expenses and incomes are managed separately, and you can set whether to include them in the total value of items", "assetAdditionalIncludeInTotalLabel": "Include in Total Value", "assetAdditionalIncludeInTotalHint": "When enabled, this fee will be included in the asset's total value calculation", "assetDefaultIncludeExtraFeesInTotal": "Include Additional Fees in Total by <PERSON><PERSON><PERSON>", "assetDefaultIncludeExtraFeesInTotalHint": "Default setting when creating new additional fees", "assetAdditionalIncludeInTotalShort": "Include", "assetAdditionalIncludeInTotalSectionTitle": "Include in Total Value Settings", "assetAdditionalIncludeInTotalSectionHint": "Select which additional fees to include in the asset's total value calculation", "assetExampleDailyPrice": "(Example) Default daily pricing", "assetExampleStar": "(Example) Swipe right to collect assets", "assetExampleRetirement": "(Example) Left Sliding Retired Assets", "assetExampleUsagePrice": "(Example) Usage-based pricing", "assetExampleCyclePrice": "(Example) Cycle-based pricing", "assetExampleNoPrice": "(Example) No pricing", "assetCacheTips": "Your asset information has been updated, do you want to save it in the cloud (login required)?", "assetRecycleBin": "Recycle Bin", "assetRecycleBinEmpty": "Recycle bin is empty", "assetRecycleBinEmptyDesc": "Deleted assets will be temporarily saved here", "assetRecycleBinRestore": "Rest<PERSON>", "assetRecycleBinDelete": "Delete Permanently", "assetRecycleBinRestoreConfirm": "Are you sure you want to restore this asset?", "assetRecycleBinDeleteConfirm": "Are you sure you want to permanently delete this asset? This action cannot be undone", "assetRecycleBinRestoreSuccess": "<PERSON><PERSON> restored successfully", "assetRecycleBinDeleteSuccess": "Asset permanently deleted", "assetRecycleBinBatchRestore": "<PERSON><PERSON>", "assetRecycleBinBatchDelete": "<PERSON><PERSON> Delete", "assetRecycleBinBatchRestoreConfirm": "Are you sure you want to restore {count} selected assets?", "assetRecycleBinBatchDeleteConfirm": "Are you sure you want to permanently delete {count} selected assets? This action cannot be undone", "assetRecycleBinDeletedAt": "Deleted At"}