import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/common/global/GlobalInfo.dart';
import 'package:tasks/data/config/app_config.dart';
import 'package:tasks/generated/assets.gen.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/utils/privacy_utils.dart';

class SplashPage extends StatefulWidget {
  final String? path;

  const SplashPage({Key? key, this.path}) : super(key: key);

  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  AppConfigDataSource get dataSource => getIt.get();

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  bool agreePrivacy = false;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    final agreePrivacy = dataSource.getAgreePrivacy();
    setState(() {
      this.agreePrivacy = agreePrivacy;
    });
    // 如果同意过协议，则初始化全局信息
    if (agreePrivacy) {
      GlobalInfo.init();
    }
    // 延迟显示弹窗，确保界面先加载完成
    Future.delayed(Duration(milliseconds: 500), () {
      if (agreePrivacy == true && mounted) {
        final newPath = "${Routers.main}?path=${widget.path}";
        context.replace(newPath);
        return;
      }
      // 显示隐私政策弹窗
      _showPrivacyDialog();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(0),
        child: AppBar(
          backgroundColor: colorScheme.surface,
          toolbarHeight: 0,
          elevation: 0,
          flexibleSpace: Container(
            color: colorScheme.surface,
          ),
          surfaceTintColor: colorScheme.surface,
        ),
      ),
      body: Container(
        color: colorScheme.surface,
        child: Column(
          children: [
            SizedBox(
              height: 24,
            ),
            if (agreePrivacy) Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.asset(
                    Assets.images.logo.path,
                    width: 48,
                    height: 48,
                  ),
                ),
                SizedBox(
                  width: 8,
                ),
                Flexible(
                  flex: 1,
                  child: Text(
                    l10n.homeSplashTips,
                    style: textTheme.labelMedium,
                    maxLines: 2,
                    softWrap: true,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            Spacer(),
          ],
        ),
      ),
    );
  }

  Future<void> _showPrivacyDialog() async {
    bool? result = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // 用户必须做出选择
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        return Dialog(
            backgroundColor: colorScheme.surfaceContainer,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.0),
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight:
                    MediaQuery.of(context).size.height * 0.8, // 限制最大高度为屏幕高度的80%
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.tips,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    SizedBox(height: 16),
                    Flexible(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              l10n.homeSplashPrivacy1("极简记物"),
                              style: textTheme.bodyMedium,
                            ),
                            SizedBox(height: 12),
                            Text(
                              l10n.homeSplashPrivacy2,
                              style: textTheme.bodyMedium,
                            ),
                            SizedBox(height: 12),
                            Text(
                              l10n.homeSplashPrivacy3,
                              style: textTheme.labelSmall,
                            ),
                            SizedBox(height: 12),
                            Wrap(
                              children: [
                                Text(
                                  '请您阅读',
                                  style: textTheme.bodySmall,
                                ),
                                GestureDetector(
                                  onTap: nav2Service,
                                  child: Text(
                                    '《${l10n.commonServiceAgreement}》',
                                    style: textTheme.titleSmall!
                                        .copyWith(color: colorScheme.primary),
                                  ),
                                ),
                                Text(
                                  '和',
                                  style: textTheme.bodySmall,
                                ),
                                GestureDetector(
                                  onTap: nav2Privacy,
                                  child: Text('《${l10n.commonPrivacyPolicy}》',
                                      style: textTheme.titleSmall!.copyWith(
                                          color: colorScheme.primary)),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 24),
                    Column(
                      children: [
                        FilledButton(
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20.0),
                            ),
                          ),
                          onPressed: () {
                            context.pop(true);
                          },
                          child: Center(
                              child: Text(
                            l10n.commonAgree,
                            style: textTheme.titleMedium!
                                .copyWith(color: colorScheme.onPrimary),
                          )),
                        ),
                        SizedBox(height: 4),
                        TextButton(
                          child: Center(
                            child: Text(
                              l10n.commonDisagree,
                              style: textTheme.labelSmall,
                            ),
                          ),
                          onPressed: () {
                            context.pop(false);
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ));
      },
    );

    if (result == true) {
      // 用户同意
      await dataSource.setAgreePrivacy(true);
      if (!mounted) return;
      // 获取包信息
      GlobalInfo.init();
      context.replace(Routers.main);
    } else {
      // 用户不同意，退出应用
      SystemNavigator.pop();
    }
  }
}
