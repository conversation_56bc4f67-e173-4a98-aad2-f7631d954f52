import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/cdkey_input/cdkey_input_utils.dart';
import 'package:tasks/models/activity/activity_info.dart';
import 'package:tasks/pages/membership_activity/membership_activity_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/router_utils.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

class MembershipActivityPage extends StatefulWidget {
  @override
  State<MembershipActivityPage> createState() => _MembershipActivityPageState();
}

class _MembershipActivityPageState extends State<MembershipActivityPage> {
  late MembershipActivityState state;

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = MembershipActivityState();

    reaction((_) => state.submitResult, (result) {
      if (result != null) {
        if (result.isSuccess) {
          ToastUtils.success(context, l10n.activitySubmitSuccess);
        } else {
          ToastUtils.error(context, result.msg ?? l10n.activitySubmitFailed);
        }
        state.clearSubmitResult();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return StateWarp(
      store: state,
      child: Scaffold(
        appBar: CommonAppBar(
          title: l10n.activityTitle,
          backgroundColor: colorScheme.surface,
        ),
        body: Observer(builder: (context) {
          if (!state.showActivity) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.event_busy,
                    size: 64,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(height: 16),
                  Text(
                    l10n.activityNotAvailable,
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView(
            padding: EdgeInsets.all(16),
            children: [
              // 活动规则
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.activityRules,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      l10n.activityRulesContent,
                      style: textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 24),

              // 动态活动列表
              Observer(builder: (context) {
                return Column(
                  children: [
                    ...state.activities
                        .map((activity) => _buildActivityCard(activity))
                        .toList()
                  ],
                );
              }),

              SizedBox(height: 24),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildActivityCard(ActivityInfo activity) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: SectionWarp(
        title: activity.title,
        children: [
          Container(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.card_giftcard,
                      color: colorScheme.primary,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        activity.title,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Text(
                  activity.content,
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                SizedBox(height: 12),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: colorScheme.secondaryContainer,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    activity.award,
                    style: textTheme.labelMedium?.copyWith(
                      color: colorScheme.onSecondaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: FilledButton(
                    onPressed: state.isLoading
                        ? null
                        : () => _submitActivity(activity),
                    child: Text(l10n.activitySubmit),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _submitActivity(ActivityInfo activity) async {
    if (!RouterUtils.checkLogin(context)) return;

    final result = await showBottomInputDialog(
      context,
      title: activity.title,
      hintText: activity.submitTips ?? "请输入链接地址",
    );

    if (result.isSuccess && result.data != null && result.data!.isNotEmpty) {
      state.submitActivity(activity.id, result.data!);
    }
  }
}
