import 'package:mobx/mobx.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/config/config_repo.dart';
import 'package:tasks/data/membership_activity/membership_activity_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/activity/activity_info.dart';
import 'package:tasks/pages/base_store.dart';

part 'membership_activity_state.g.dart';

class MembershipActivityState = _MembershipActivityState
    with _$MembershipActivityState;

abstract class _MembershipActivityState extends BaseStore with Store {
  final ConfigRepo _configRepo = getIt.get();
  final MembershipActivityRepo _activityRepo = getIt.get();

  @computed
  bool get showActivity => _configRepo.getConfig().showMembershipActivity;

  @observable
  List<ActivityInfo> activities = [];

  @observable
  Result<void>? submitResult;

  _MembershipActivityState() {
    loadActivities();
  }

  @action
  Future<void> loadActivities() async {
    runWithLoading(() async {
      final result = await _activityRepo.getActivityList();
      if (result.isSuccess && result.data != null) {
        activities = result.data!;
      }
    });
  }

  @action
  Future<void> submitActivity(String activityId, String link) async {
    runWithLoading(() async {
      final result = await _activityRepo.submitActivity(activityId, link);
      submitResult = result;
    });
  }

  @action
  void clearSubmitResult() {
    submitResult = null;
  }
}
