import 'package:mobx/mobx.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/category_store.dart';

part 'edit_category_state.g.dart';

class EditCategoryState = _EditCategoryState with _$EditCategoryState;

enum EditCategoryEvent {
  addSuccess,
  editSuccess,
}

abstract class _EditCategoryState extends BaseStore with Store {
  CategoryLocalDataSource _dataSource = getIt.get();

  CategoryStore categoryStore = getIt.get();

  @observable
  String? id;

  @observable
  String name = "";

  @observable
  AppIcon icon = AppIcon.defaultIcon();

  @computed
  bool get isEditMode => id != null;

  @computed
  String get title => isEditMode ? "编辑分类" : "添加分类";

  @computed
  String get actionText => isEditMode ? "保存" : "添加";

  @observable
  EditCategoryEvent? event;

  _EditCategoryState(String? categoryId) {
    init(categoryId);
  }

  Future<void> init(String? categoryId) async {
    if (categoryId == null) {
      return;
    }
    final category = _dataSource.getCategory(categoryId);
    if (category == null) {
      return;
    }
    runInAction(() {
      id = categoryId;
      name = category.name;
      if (category.icon != null) {
        icon = category.icon!;
      }
    });
  }

  @action
  void onNameChanged(String value) {
    name = value;
  }

  @action
  void onIconSelected(AppIcon? icon) {
    if (icon == null) {
      return;
    }
    this.icon = icon;
  }

  @action
  Future<void> confirm() async {
    if (name.isEmpty) {
      setError("分类名称不能为空");
      return;
    }
    // 添加
    if (!isEditMode) {
      await _dataSource.addCategory(name, icon);
      categoryStore.fetchCategories();
      event = EditCategoryEvent.addSuccess;
      return;
    }
    // 编辑
    await _dataSource.updateCategory(id!, name, icon);
    categoryStore.fetchCategories();
    event = EditCategoryEvent.editSuccess;
  }
}
