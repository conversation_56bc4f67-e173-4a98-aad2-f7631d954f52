import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/iconselect/icon_select_utils.dart';
import 'package:tasks/pages/category/edit_category_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/app_icon_widget.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';

class CategoryEditPage extends StatefulWidget {
  final String? categoryId;

  const CategoryEditPage({
    Key? key,
    this.categoryId,
  }) : super(key: key);

  @override
  _CategoryEditPageState createState() => _CategoryEditPageState();
}

class _CategoryEditPageState extends State<CategoryEditPage> {
  late EditCategoryState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  final nameController = TextEditingController();

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = EditCategoryState(
      widget.categoryId,
    );
    // 事件监听
    reaction((_) => state.event, (r) async {
      state.clearError();
      // 编辑成功
      if (r == EditCategoryEvent.editSuccess) {
        ToastUtils.success(context, "修改分类成功");
        context.pop(true);
        return;
      }
      // 添加成功
      if (r == EditCategoryEvent.addSuccess) {
        ToastUtils.success(context, "添加分类成功");
        context.pop(true);
      }
    });
  }

  @override
  void dispose() {
    nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: CommonAppBar(
          backgroundColor: colorScheme.surface,
          title: state.title,
          actions: [
            TextButton(
              onPressed: state.confirm,
              child: Text(
                state.actionText,
                style: TextStyle(color: colorScheme.primary),
              ),
            ),
          ]),
      body: StateWarp(
        store: state,
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            _buildTitle(l10n.commonCategoryName),
            SizedBox(height: 12),
            ClearInputTextField(
              value: state.name,
              hintText: l10n.commonCategoryNameHint,
              onChange: (v) {
                state.onNameChanged(v);
              },
              fillColor: colorScheme.surfaceContainer,
            ),
            SizedBox(height: 24),
            _buildTitle(l10n.commonSelectIcon),
            SizedBox(height: 12),
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                color: colorScheme.surfaceContainer,
                child: ListTile(
                  onTap: () {
                    showIconSelect(context, state.icon, (v) {
                      state.onIconSelected(v);
                    });
                  },
                  title: Text(l10n.commonIcon),
                  trailing: Observer(builder: (context) {
                    return AppIconWidget(
                      icon: state.icon,
                      size: 36,
                    );
                  }),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
      ),
    );
  }
}
