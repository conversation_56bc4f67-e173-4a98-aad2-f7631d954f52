import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/providers/category_store.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/widgets/app_icon_widget.dart';

class CategoryGridSelectSheet extends StatelessWidget {
  final CategoryEntity? selectedCategory;

  const CategoryGridSelectSheet({
    Key? key,
    this.selectedCategory,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final CategoryStore categoryStore = getIt.get();
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final S l10n = S.of(context);

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Safe<PERSON><PERSON>(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Text(
                    l10n.assetSelectCategory,
                    style: textTheme.titleMedium,
                  ),
                  const SizedBox(
                    width: 12,
                  ),
                  InkWell(
                    child: Text(
                      l10n.ucenterManageCategory,
                      style: textTheme.titleMedium!
                          .copyWith(color: colorScheme.primary),
                    ),
                    onTap: () {
                      context.push(Routers.categoryManage);
                    },
                  ),
                  Spacer(),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => context.pop(),
                  ),
                ],
              ),
            ),
            Divider(height: 0.5),
            // 分类网格
            Observer(builder: (context) {
              return Flexible(
                child: GridView.builder(
                  padding: EdgeInsets.all(16),
                  shrinkWrap: true,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    childAspectRatio: 0.9,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: categoryStore.categories.length,
                  itemBuilder: (context, index) {
                    final category = categoryStore.categories[index];
                    final isSelected = selectedCategory?.id == category.id;

                    return InkWell(
                      onTap: () => context.pop(category),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 56,
                            height: 56,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? colorScheme.primary.withOpacity(0.2)
                                  : colorScheme.surfaceContainer,
                              shape: BoxShape.circle,
                              border: isSelected
                                  ? Border.all(
                                      color: colorScheme.primary, width: 2)
                                  : null,
                            ),
                            child: Center(
                              child: AppIconWidget(
                                icon: category.icon,
                                size: 28,
                                color: isSelected ? colorScheme.primary : null,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            category.name,
                            style: textTheme.bodySmall,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}

/// 选择分类
Future<CategoryEntity?> showCategoryGridSelect(
    BuildContext context, CategoryEntity? value) async {
  final result = await showModalBottomSheet<CategoryEntity>(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    constraints: BoxConstraints(
      minWidth: double.infinity,
      maxWidth: double.infinity,
    ),
    builder: (context) => CategoryGridSelectSheet(
      selectedCategory: value,
    ),
  );

  return result;
}
