import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/pages/category/select_category/category_grid_select.dart';
import 'package:tasks/widgets/app_icon_widget.dart';

class SelectCategoryWidget extends StatelessWidget {
  final CategoryEntity? value;

  final ValueChanged<CategoryEntity> onChange;

  SelectCategoryWidget(
      {super.key, required this.value, required this.onChange}) {}

  @override
  Widget build(BuildContext context) {
    S l10n = S.of(context);

    ColorScheme colorScheme = Theme.of(context).colorScheme;

    TextTheme textTheme = Theme.of(context).textTheme;

    String displayValue = value?.name ?? l10n.assetSelectCategory;
    return InkWell(
      onTap: () async {
        // 使用新的网格选择器
        final result = await showCategoryGridSelect(context, value);
        if (result == null) return;
        onChange(result);
      },
      child: Container(
          alignment: Alignment.center,
          constraints: BoxConstraints(maxWidth: 100),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: colorScheme.surfaceContainer),
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              AppIconWidget(
                icon: value?.icon,
                size: 16,
              ),
              SizedBox(width: 4),
              Flexible(
                flex: 1,
                child: Text(
                  displayValue,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: textTheme.bodySmall!
                      .copyWith(color: colorScheme.onSurface),
                ),
              ),
            ],
          )),
    );
  }
}
