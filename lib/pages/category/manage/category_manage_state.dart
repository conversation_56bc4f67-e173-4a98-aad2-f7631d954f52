import 'package:mobx/mobx.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/providers/category_store.dart';

part 'category_manage_state.g.dart';

class CategoryManageState = _CategoryManageState with _$CategoryManageState;

abstract class _CategoryManageState with Store {
  final CategoryLocalDataSource _dataSource = getIt.get();

  CategoryStore categoryStore = getIt.get();

  @computed
  ObservableList<CategoryEntity> get categories => categoryStore.categories;

  @action
  Future<void> reorderCategories(int oldIndex, int newIndex) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    // 在内存中直接更新顺序
    final CategoryEntity item = categories.removeAt(oldIndex);
    categories.insert(newIndex, item);

    // 更新所有项的order
    for (int i = 0; i < categories.length; i++) {
      categories[i].order = i;
    }

    // 异步保存到本地
    await _dataSource.saveCategories(categories);
    categoryStore.fetchCategories();
  }

  @action
  Future<void> deleteCategory(String id) async {
    await _dataSource.deleteCategory(id);
    categoryStore.fetchCategories();
  }
}
