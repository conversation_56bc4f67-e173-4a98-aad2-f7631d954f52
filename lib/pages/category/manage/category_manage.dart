import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/pages/category/manage/category_manage_state.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/widgets/app_icon_widget.dart';
import 'package:tasks/widgets/common_app_bar.dart';

class CategoryManagePage extends StatefulWidget {
  @override
  _CategoryManagePageState createState() => _CategoryManagePageState();
}

class _CategoryManagePageState extends State<CategoryManagePage> {
  late CategoryManageState state;

  S get l10n => S.of(context);

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    super.initState();
    state = CategoryManageState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surfaceContainer,
        title: "分类管理",
        actions: [
          TextButton(
            child: Text(
              l10n.commonAdd,
              style:
                  textTheme.titleMedium!.copyWith(color: colorScheme.primary),
            ),
            onPressed: () {
              context.push(Routers.categoryEdit);
            },
          ),
        ],
      ),
      body: Observer(builder: (context) {
        return ReorderableListView.builder(
          itemCount: state.categories.length,
          onReorder: (oldIndex, newIndex) {
            state.reorderCategories(oldIndex, newIndex);
          },
          itemBuilder: (context, index) {
            final item = state.categories[index];
            return _buildCategoryItem(item, index);
          },
        );
      }),
    );
  }

  Widget _buildCategoryItem(CategoryEntity item, int index) {
    return Stack(
      key: Key(item.id.toString()),
      children: [
        Container(
          color: index % 2 == 0
              ? colorScheme.surface
              : colorScheme.surfaceContainer,
          child: ListTile(
            leading: InkWell(
                onTap: () {
                  showConfirm(
                    context,
                    title: "删除分类",
                    content: "确定删除分类？",
                    onConfirm: () {
                      state.deleteCategory(item.id);
                    },
                  );
                },
                child: Icon(Icons.remove_circle, color: colorScheme.error)),
            title: Row(
              children: [
                SizedBox(width: 8),
                AppIconWidget(
                  icon: item.icon,
                ),
                SizedBox(width: 8),
                Text(item.name),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [Icon(Icons.drag_indicator)],
            ),
            onTap: () {
              context.push("${Routers.categoryEdit}?categoryId=${item.id}");
            },
          ),
        ),
      ],
    );
  }
}
