import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/routers.dart';

class AssetSearchItem extends StatefulWidget {
  final Color color;

  AssetSearchItem({
    super.key,
    this.color = Colors.white,
  }) {}

  @override
  State<AssetSearchItem> createState() => _AssetSearchItemState();
}

class _AssetSearchItemState extends State<AssetSearchItem> {
  // 添加 TextEditingController
  final TextEditingController _searchController = TextEditingController();

  // final FocusNode _searchFocusNode = FocusNode();

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void dispose() {
    super.dispose();
    _searchController.dispose();
    // _searchFocusNode.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // final filterText = widget.filter.searchText ?? "";

    return InkWell(
      onTap: nav2Search,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            color: widget.color.withValues(alpha: 0.8),
            size: 20,
          ),
        ],
      ),
    );
  }

  void nav2Search() async {
    context.push(Routers.search);
  }
}
