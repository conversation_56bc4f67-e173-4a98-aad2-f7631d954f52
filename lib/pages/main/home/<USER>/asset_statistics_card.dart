import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/models/asset/asset_statistics.dart';
import 'package:tasks/models/asset_filter.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/custom_theme/custom_card_theme.dart';
import 'package:tasks/models/user_preferences/user_preferences_entity.dart';
import 'package:tasks/pages/main/home/<USER>/toggleable_value_display.dart';
import 'package:tasks/utils/double_ext.dart';
import 'package:tasks/widgets/glassmorphism_container.dart';

class AssetStatisticsCard extends StatelessWidget {
  final List<AssetCardEntity> assets;

  final List<CategoryEntity> categories;

  final AssetFilter filter;

  final AssetStatistics value;

  final CustomCardTheme customTheme;

  final UserPreferencesEntity preferencesEntity;
  final ValueChanged<UserPreferencesEntity> onPreferenceChanged;

  AssetStatisticsCard(
      {Key? key,
      required this.assets,
      required this.categories,
      required this.filter,
      required this.value,
      required this.customTheme,
      required this.preferencesEntity,
      required this.onPreferenceChanged})
      : super(key: key);

  Color _getDividerColor(context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? colorScheme.onSurface.withValues(alpha: 0.2)
        : customTheme.onContainerColor(context).withValues(alpha: 0.3);
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;

    return GlassmorphismContainer(
      margin: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      padding: EdgeInsets.all(12),
      borderRadius: 20,
      blurIntensity: 20,
      borderWidth: 2,
      gradientColors: [customTheme.startColor, customTheme.endColor],
      borderColor: colorScheme.surfaceContainer.withValues(alpha: 0.2),
      shadowColors: [
        Colors.black.withValues(alpha: 0.1),
        customTheme.endColor.withValues(alpha: 0.2),
      ],
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildMainRow(context),
          SizedBox(height: 12),
          _buildDivider(context),
          SizedBox(height: 12),
          _buildStatisticsRow(context),
        ],
      ),
    );
  }

  Widget _buildMainRow(BuildContext context) {
    S l10n = S.of(context);
    return Row(
      children: [
        // 总物品价值
        Expanded(
          child: ToggleableValueDisplay(
            label: l10n.homeTotalAssetValue,
            value: value.totalPrice.toDisplayMoney(maxDigits: 1),
            customTheme: customTheme,
            isVisible: preferencesEntity.showTotalAssetValue,
            onValueChanged: (value) => onPreferenceChanged(
                preferencesEntity.copyWith(showTotalAssetValue: value)),
          ),
        ),
        Container(
          width: 1,
          height: 50,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                _getDividerColor(context),
                Colors.transparent,
              ],
            ),
          ),
        ),
        // 总日均
        Expanded(
          child: _buildMainStatItem(
            label: l10n.homeDailyPrice,
            value: value.dailyPrice.toDisplayMoney(),
            context: context,
          ),
        ),
      ],
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      height: 1,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            _getDividerColor(context),
            Colors.transparent,
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsRow(BuildContext context) {
    S l10n = S.of(context);
    return Row(
      children: [
        // 物品总数
        Expanded(
          child: _buildStatItem(
            label: l10n.homeAssetCount,
            value: "${assets.length}",
            context: context,
          ),
        ),
        Container(
          width: 1,
          height: 50,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                _getDividerColor(context),
                Colors.transparent,
              ],
            ),
          ),
        ),
        // 最高日均
        Expanded(
          child: _buildStatItem(
            label: l10n.homeMaxDaily,
            value: value.maxDailyPrice.toDisplayMoney(),
            context: context,
          ),
        ),
        Container(
          width: 1,
          height: 50,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                _getDividerColor(context),
                Colors.transparent,
              ],
            ),
          ),
        ),
        // 最低日均
        Expanded(
          child: _buildStatItem(
            label: l10n.homeMinDaily,
            value: value.minDailyPrice.toDisplayMoney(),
            context: context,
          ),
        ),
      ],
    );
  }

  Widget _buildMainStatItem({
    required String label,
    required String value,
    required BuildContext context,
  }) {
    TextTheme textTheme = Theme.of(context).textTheme;
    final textColor = customTheme.onContainerColor(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Column(
        children: [
          Text(
            label,
            style: textTheme.bodyMedium!.copyWith(
              color: textColor.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
              letterSpacing: 0.5,
            ),
          ),
          SizedBox(height: 6),
          Text(
            value,
            style: textTheme.headlineMedium!.copyWith(
              color: textColor,
              fontWeight: FontWeight.bold,
              letterSpacing: -0.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required String label,
    required String value,
    required BuildContext context,
  }) {
    final textColor = customTheme.onContainerColor(context);
    TextTheme textTheme = Theme.of(context).textTheme;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Column(
        children: [
          Text(
            label,
            style: textTheme.bodySmall!.copyWith(
              color: textColor.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 4),
          Text(
            value,
            style: textTheme.titleMedium!.copyWith(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}
