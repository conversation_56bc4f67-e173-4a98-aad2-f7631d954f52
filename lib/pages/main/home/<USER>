import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_reorderable_grid_view/widgets/custom_draggable.dart';
import 'package:flutter_reorderable_grid_view/widgets/reorderable_builder.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/pages/main/home/<USER>/asset_card.dart';

/// 可以拖拽排序的资产卡片列表
class RecordAssetList extends StatefulWidget {
  final List<AssetCardEntity> assets;
  final ValueChanged<AssetCardEntity> onFavorite;
  final ValueChanged<AssetCardEntity> switchInService;
  final ValueChanged<List<String>>? onSwap;
  final bool enableSwap;
  final ScrollController? scrollController;

  const RecordAssetList({
    super.key,
    required this.assets,
    required this.onFavorite,
    required this.switchInService,
    required this.enableSwap,
    this.scrollController,
    this.onSwap,
  });

  @override
  State<RecordAssetList> createState() => _RecordAssetListState();
}

class _RecordAssetListState extends State<RecordAssetList> {
  List<AssetCardEntity> assets = [];

  final _gridViewKey = GlobalKey();

  ScrollController get _scrollController =>
      widget.scrollController ?? ScrollController();

  @override
  void initState() {
    super.initState();
    setState(() {
      assets = widget.assets;
    });
  }

  @override
  void didUpdateWidget(covariant RecordAssetList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.assets != widget.assets) {
      setState(() {
        assets = widget.assets;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕的宽度来判断每行显示多少个卡片
    final width = MediaQuery.of(context).size.width;
    // 根据屏幕宽度计算每行显示的卡片数量，最小为1
    final crossAxisCount = max((width / 400).toInt(), 1);
    final gridDelegate = SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount, // 每行显示多少个卡片
        crossAxisSpacing: 0,
        mainAxisSpacing: 0,
        childAspectRatio: 4);
    return ReorderableBuilder.builder(
      scrollController: _scrollController,
      enableDraggable: widget.enableSwap,
      onReorder: _handleReorder,
      itemCount: assets.length,
      childBuilder: (itemBuilder) {
        return GridView.builder(
          key: _gridViewKey,
          scrollDirection: Axis.vertical,
          padding: const EdgeInsets.all(4),
          controller: _scrollController,
          gridDelegate: gridDelegate,
          itemCount: assets.length,
          itemBuilder: (context, index) {
            return itemBuilder(
              _getChild(index: index),
              index,
            );
          },
        );
      },
    );
  }

  Widget _getChild({required int index}) {
    final element = assets[index];
    return CustomDraggable(
      key: Key(element.id),
      data: index,
      child: Container(
        key: Key(element.id),
        margin: EdgeInsets.all(4),
        child: AssetCard(
          asset: element,
          switchInService: widget.switchInService,
          onFavorite: widget.onFavorite,
        ),
      ),
    );
  }

  void _handleReorder(
      ReorderedListFunction<AssetCardEntity> reorderedListFunction) {
    setState(() {
      assets = reorderedListFunction(assets);
      widget.onSwap?.call(assets.map((e) => e.id).toList());
    });
  }
}
