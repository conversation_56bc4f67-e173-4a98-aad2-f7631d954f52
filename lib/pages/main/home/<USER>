import 'dart:async';
import 'dart:math';

import 'package:debounce_throttle/debounce_throttle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:mobx/mobx.dart';
import 'package:provider/provider.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/modal/version_update/UpdateDialog.dart';
import 'package:tasks/pages/main/home/<USER>';
import 'package:tasks/pages/main/home/<USER>/asset_card.dart';
import 'package:tasks/pages/main/home/<USER>/asset_statistics_card.dart';
import 'package:tasks/pages/main/home/<USER>/home_asset_header.dart';
import 'package:tasks/providers/theme_provider.dart';
import 'package:tasks/utils/keyboard_utils.dart';
import 'package:tasks/utils/string_ext.dart';
import 'package:tasks/widgets/empty_state_widget.dart';

class HomePage extends StatefulWidget {
  final Function(bool)? onScrollStateChanged;

  const HomePage({
    Key? key,
    this.onScrollStateChanged,
  }) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

// 全局键用于访问HomePage的状态
final GlobalKey<_HomePageState> homePageKey = GlobalKey<_HomePageState>();

class _HomePageState extends State<HomePage> {
  late HomeState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  final AssetRepo repo = getIt.get();

  Timer? _scrollStopTimer; // 滚动停止检测定时器

  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 首页让键盘默认隐藏一次
    systemHideKeyboard();
    state = HomeState();
    // 检查更新逻辑
    state.checkVersion();
    reaction((_) => state.checkUpdateResult, (r) {
      r?.onSuccess((r) {
        if (r != null && !r.downloadUrl.isNullOrEmpty) {
          showUpdateDialog(context, r);
        }
      });
    });
    // 滚动监听
    _onScroll();
  }

  // 滚动监听
  void _onScroll() async {
    final throttle =
        Throttle<double>(Duration(milliseconds: 100), initialValue: 0);
    _scrollController.addListener(
        () => throttle.setValue(_scrollController.position.pixels));
    throttle.values.listen((v) {
      // 检测滚动停止
      _checkScrollStop(v);
    });
  }

  // 检测滚动停止
  void _checkScrollStop(double scrollPosition) {
    // 取消之前的定时器
    _scrollStopTimer?.cancel();

    // 设置新的定时器，500毫秒后检测是否停止滚动
    _scrollStopTimer = Timer(Duration(milliseconds: 500), () {
      // 滚动停止后通知父组件
      widget.onScrollStateChanged?.call(scrollPosition > 1000);
    });
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
    _scrollStopTimer?.cancel(); // 取消定时器
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = context.watch<ThemeProvider>();
    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // 主要内容
          CustomScrollView(
            controller: _scrollController,
            slivers: [
              Observer(builder: (context) {
                return HomeAssetHeader(
                  filter: state.filter,
                  onValueChanged: state.updateFilterStatus,
                );
              }),
              // 统计信息头部
              Observer(builder: (context) {
                return SliverToBoxAdapter(
                  child: AssetStatisticsCard(
                    assets: state.assets,
                    categories: state.categories,
                    filter: state.filter,
                    value: state.assetStatistics,
                    customTheme: themeProvider.customTheme.topCard,
                    preferencesEntity: state.preferencesEntity,
                    onPreferenceChanged: state.updatePreferences,
                  ),
                );
              }),
              // 数据为空时展示
              Observer(builder: (context) {
                if (state.assets.isEmpty) {
                  return SliverToBoxAdapter(
                    child: SizedBox(
                      height: MediaQuery.of(context).size.height - 300,
                      child: EmptyStateWidget(
                        title: l10n.homeEmptyListTitle,
                        subtitle: l10n.homeEmptyListDesc,
                        backgroundColor: colorScheme.surface,
                        icon: Icons.account_balance_wallet_outlined,
                      ),
                    ),
                  );
                }
                return SliverToBoxAdapter(child: SizedBox.shrink());
              }),
              // 资产列表
              Observer(builder: (context) {
                if (state.assets.isEmpty) {
                  return SliverToBoxAdapter(child: SizedBox.shrink());
                }

                // 获取屏幕的宽度来判断每行显示多少个卡片
                final width = MediaQuery.of(context).size.width;
                final crossAxisCount = max(1, (width / 400).toInt());

                return SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 6.0),
                  sliver: SliverGrid(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: crossAxisCount,
                      crossAxisSpacing: 0,
                      mainAxisSpacing: 0,
                      childAspectRatio: 4,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final asset = state.assets[index];
                        return Container(
                          margin: EdgeInsets.all(4),
                          child: AssetCard(
                              asset: asset,
                              enableSlide: true,
                              onFavorite: (a) => state.toggleFavorite(a.id),
                              switchInService: (a) =>
                                  state.changeAssetInService(a.id),
                              delete: (a) => state.deleteAsset(a.id)),
                        );
                      },
                      childCount: state.assets.length,
                    ),
                  ),
                );
              }),
            ],
          ),
        ],
      ),
    );
  }

  /// 滑动到顶部
  void _scrollToTop() {
    _scrollController.jumpTo(0);
  }

  /// 公共方法：供外部调用的回到顶部功能
  void scrollToTop() {
    _scrollToTop();
  }
}
