import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/asset/asset_local_data_source.dart';
import 'package:tasks/data/user_preferences/user_preferences_lds.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/app_update_info.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/models/asset/asset_statistics.dart';
import 'package:tasks/models/asset_filter.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/user_preferences/user_preferences_entity.dart';
import 'package:tasks/providers/user_store.dart';
import 'package:tasks/providers/asset_store.dart';
import 'package:tasks/providers/category_store.dart';
import 'package:tasks/providers/user_preferences_store.dart';

part 'home_state.g.dart';

@injectable
class HomeState = HomeStateBase with _$HomeState;

abstract class HomeStateBase with Store {
  UserStore userStore = getIt.get();
  AssetStore assetStore = getIt.get();
  CategoryStore categoryStore = getIt.get();
  UserPreferencesStore userPreferencesStore = getIt.get();
  final _assetRepo = getIt.get<AssetRepo>();
  final _datasource = getIt.get<AssetLocalDataSource>();
  final repo = getIt.get<AssetRepo>();
  final userRepo = getIt.get<UserRepo>();

  @observable
  AssetFilter filter = AssetFilter();

  @computed
  List<CategoryEntity> get categories => categoryStore.categories;

  @computed
  UserPreferencesEntity get preferencesEntity =>
      userPreferencesStore.preferencesEntity;

  @computed
  List<AssetCardEntity> get assets {
    final assets = _assetRepo.getFilterList(
        assetStore.assets, categoryStore.categories,
        filter: filter);
    return assets;
  }

  @computed
  String get titleText {
    final retireLength = assets.where((asset) => !asset.isInService).length;
    return "${assets.length}/$retireLength";
  }

  @computed
  AssetStatistics get assetStatistics {
    return repo.getAssetStatistics(
        assets: assets, preferencesEntity: preferencesEntity);
  }

  @observable
  Result<AppUpdateInfo?>? checkUpdateResult;

  HomeStateBase() {
    // 获取初始数据
    _init();
  }

  _init() async {
    final filter = _datasource.getAssetFilter();

    runInAction(() {
      this.filter = filter;
    });
  }

  @action
  Future<void> checkVersion() async {
    checkUpdateResult = await userRepo.checkUpdate("home");
  }

  @action
  Future<void> updateFilterStatus(AssetFilter assetFilter) async {
    _datasource.saveAssetFilter(assetFilter);
    filter = assetFilter;
  }

  @action
  Future<void> changeAssetInService(String id) async {
    final asset = await _datasource.getAssetById(id);
    if (asset != null) {
      final updated = asset.copyWith(
          isInService: !asset.isInService, retireDate: DateTime.now());
      assetStore.updateAsset(updated);
    }
  }

  @action
  Future<void> toggleFavorite(String id) async {
    final asset = await _datasource.getAssetById(id);
    if (asset != null) {
      final updated = asset.copyWith(isFavorite: !asset.isFavorite);
      assetStore.updateAsset(updated);
    }
  }

  /// 删除资产
  @action
  Future<void> deleteAsset(String id) async {
    await repo.deleteAsset(id);
    assetStore.fetchAssets();
  }

  /// 更新用户偏好设置
  @action
  Future<void> updatePreferences(UserPreferencesEntity preferences) async {
    await userPreferencesStore.updatePreferences(preferences);
  }
}
