import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset_filter.dart';
import 'package:tasks/pages/main/home/<USER>/filter_state.dart';
import 'package:tasks/widgets/filter_options/filter_options.dart';

class AssetFilterPopup extends StatefulWidget {
  final AssetFilter value;

  AssetFilterPopup({required this.value});

  @override
  State<AssetFilterPopup> createState() => _AssetFilterPopupState();
}

class _AssetFilterPopupState extends State<AssetFilterPopup> {
  late FilterState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = FilterState(value: widget.value);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainer,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            Flexible(
              flex: 1,
              child: ListView(
                padding: EdgeInsets.zero,
                children: <Widget>[
                  // 资产类型
                  Observer(builder: (context) {
                    return FilterOptions(
                      title: "资产类型",
                      value: state.selectStatusIds,
                      options: state.statusFilterOptions,
                      isMultiSelect: false,
                      onValueChange: (value) {
                        state.onAssetStatusChanged(value);
                      },
                    );
                  }),
                  // 资产分类
                  Observer(builder: (context) {
                    return FilterOptions(
                      title: "分类",
                      value: state.selectCategoryIds,
                      options: state.categoriesFilterOptions,
                      isMultiSelect: false,
                      onValueChange: (value) {
                        state.onAssetCategoryChanged(value);
                      },
                    );
                  }),
                  // 计价类型
                  Observer(builder: (context) {
                    return FilterOptions(
                      title: "计价类型",
                      value: state.selectPriceMethodIds,
                      options: state.priceMethodFilterOptions,
                      isMultiSelect: false,
                      onValueChange: (value) {
                        state.onAssetPriceMethodChanged(value);
                      },
                    );
                  }),
                  SizedBox(
                    height: 24,
                  )
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  FilledButton(
                    onPressed: () {
                      state.reset();
                    },
                    style: FilledButton.styleFrom(
                      backgroundColor: colorScheme.surface,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: Text(
                        l10n.reset,
                        style: textTheme.titleMedium,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 12,
                  ),
                  Expanded(
                    child: FilledButton(
                      onPressed: () {
                        Navigator.of(context).pop(state.filter);
                      },
                      child: Text(
                        l10n.confirm,
                        style: textTheme.titleMedium!
                            .copyWith(color: colorScheme.onPrimary),
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

Future<AssetFilter?> showFilterMenu(
    BuildContext context, AssetFilter filter) async {
  final result = await showModalBottomSheet<AssetFilter?>(
    context: context,
    constraints:
        BoxConstraints(minWidth: double.infinity, maxWidth: double.infinity),
    showDragHandle: false,
    builder: (context) {
      return AssetFilterPopup(
        value: filter,
      );
    },
  );
  return result;
}
