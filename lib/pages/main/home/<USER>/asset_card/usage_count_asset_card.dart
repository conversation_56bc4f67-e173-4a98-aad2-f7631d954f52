import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/utils/double_ext.dart';
import 'package:tasks/widgets/app_icon_widget.dart';

class UsageCountAssetCard extends StatelessWidget {
  final Color contentColor;

  final AssetCardEntity assetCard;

  const UsageCountAssetCard(
      {super.key, required this.contentColor, required this.assetCard});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    S l10n = S.of(context);
    ColorScheme colorScheme = Theme.of(context).colorScheme;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 图标
        AppIconWidget(
          icon: assetCard.icon,
          size: 48,
          color: Colors.black87,
          fit: BoxFit.contain,
        ),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      assetCard.title,
                      maxLines: 1,
                      style: textTheme.titleMedium!.copyWith(
                          color: contentColor.withValues(alpha: 0.85),
                          overflow: TextOverflow.ellipsis),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 2),
              RichText(
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                text: TextSpan(
                    style: textTheme.titleMedium!.copyWith(
                      color: contentColor.withValues(alpha: 0.85),
                    ),
                    children: [
                      TextSpan(
                        text: assetCard.measureAmount.toDisplayMoney(),
                      ),
                      if (assetCard.showUsageCount)
                        TextSpan(
                          text:
                              " · ${l10n.commonOfCount(assetCard.asset.measurePriceOfUsage?.toDisplayMoney() ?? "")}",
                          style: textTheme.bodySmall!.copyWith(
                            color: contentColor.withValues(alpha: 0.65),
                          ),
                        ),
                    ]),
              ),
              SizedBox(height: 2),
              Row(
                children: [
                  Text(
                    assetCard.timeFormat,
                    style: textTheme.labelSmall!.copyWith(
                      color: contentColor.withValues(alpha: 0.45),
                    ),
                  ),
                  Visibility(
                    visible: assetCard.showWarrantyTag,
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: 4),
                      padding: EdgeInsets.only(
                        left: 6,
                        right: 6,
                        top: 1.5,
                        bottom: 3,
                      ),
                      decoration: BoxDecoration(
                        color: assetCard.inWarranty
                            ? Color(0xFF3672FF)
                            : Color(0xFF9736FF),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        assetCard.inWarranty
                            ? l10n.homeWarrantyCountdown(
                                assetCard.warrantyCountdown.toString())
                            : l10n.homeStatusExpired,
                        style: textTheme.bodySmall!.copyWith(
                            fontSize: 10, color: colorScheme.onPrimary),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (!assetCard.isInService) ...[
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  l10n.homeStatusRetired,
                  style: textTheme.bodySmall,
                ),
              ),
              SizedBox(width: 4),
            ],
            // 服役时间
            Row(
              children: [
                Text(
                  l10n.commonDayFormat(assetCard.holdingDays.toString()),
                  style: textTheme.bodySmall!.copyWith(
                    color: contentColor.withValues(alpha: 0.65),
                  ),
                ),
                Text(
                  l10n.commonDelimiter,
                  style: textTheme.bodySmall!.copyWith(
                    color: contentColor.withValues(alpha: 0.65),
                  ),
                ),
                // 使用次数
                Text(
                  l10n.commonTimeFormat(
                      assetCard.asset.usagePriceValue?.displayCount() ?? ""),
                  style: textTheme.bodySmall!.copyWith(
                    color: contentColor.withValues(alpha: 0.65),
                  ),
                ),
              ],
            )
          ],
        ),
        SizedBox(width: 8),
      ],
    );
  }
}
