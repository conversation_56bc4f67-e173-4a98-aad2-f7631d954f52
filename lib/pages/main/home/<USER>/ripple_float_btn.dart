import 'package:flutter/material.dart';

class RippleButton extends StatefulWidget {
  @override
  _RippleButtonState createState() => _RippleButtonState();
}

class _RippleButtonState extends State<RippleButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化 AnimationController，设置动画总时长为 3 秒
    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 3),
    )..repeat(); // 使动画循环播放

    // 定义缩放动画：从小变大
    _scaleAnimation = TweenSequence<double>(
      [
        // 第一阶段：从小变大（0.0 → 1.0），占动画时长的 50%
        TweenSequenceItem(
          tween: Tween(begin: 0.0, end: 1.0),
          weight: 60,
        ),
        // 第二阶段：停留（保持 1.0），占动画时长的 16.67%（0.5 秒）
        TweenSequenceItem(
          tween: ConstantTween<double>(1.0),
          weight: 10,
        ),
        TweenSequenceItem(
          tween: ConstantTween<double>(0.0),
          weight: 30,
        ),
      ],
    ).animate(_controller);

    // 定义透明度动画：从深色到透明
    _opacityAnimation = TweenSequence<double>(
      [
        // 第一阶段：从深色到透明（1.0 → 0.0），占动画时长的 50%
        TweenSequenceItem(
          tween: Tween(begin: 1.0, end: 0.0),
          weight: 60,
        ),
        // 第二阶段：停留（保持 0.0），占动画时长的 16.67%（0.5 秒）
        TweenSequenceItem(
          tween: ConstantTween<double>(0.0),
          weight: 30,
        ),
        // 第三阶段：从透明到深色（0.0 → 1.0），占动画时长的 33.33%
        TweenSequenceItem(
          tween: Tween(begin: 0.0, end: 1.0),
          weight: 10,
        ),
      ],
    ).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 涟漪效果
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value, // 根据缩放动画值缩放
                child: Opacity(
                  opacity: _opacityAnimation.value, // 根据透明度动画值设置透明度
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colorScheme.primary,
                    ),
                  ),
                ),
              );
            },
          ),
          // 按钮
          Container(
            decoration: BoxDecoration(
              color: colorScheme.primary,
              shape: BoxShape.circle,
            ),
            padding: const EdgeInsets.all(16),
            child: Icon(
              Icons.add,
              color: colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
