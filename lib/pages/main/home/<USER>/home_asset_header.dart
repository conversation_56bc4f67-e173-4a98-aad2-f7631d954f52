import 'package:flutter/material.dart';
import 'package:tasks/common/global/GlobalInfo.dart';
import 'package:tasks/models/asset_filter.dart';
import 'package:tasks/pages/main/home/<USER>/asset_filter.dart';
import 'package:tasks/pages/main/home/<USER>/asset_more_item.dart';
import 'package:tasks/pages/main/home/<USER>/asset_search_item.dart';
import 'package:tasks/pages/main/home/<USER>/asset_sort_item.dart';

class HomeAssetHeader extends StatelessWidget {
  final AssetFilter filter;
  final ValueChanged<AssetFilter> onValueChanged;
  final ValueChanged<MoreActionType>? onMoreActionSelected;

  const HomeAssetHeader({
    super.key,
    required this.filter,
    required this.onValueChanged,
    this.onMoreActionSelected,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return SliverAppBar(
      title: Text(
        GlobalInfo.appName.isNotEmpty ? GlobalInfo.appName : "极简记物",
        style: textTheme.titleLarge!.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        AssetSearchItem(color: colorScheme.onSurface),
        SizedBox(width: 12),
        AssetSortItem(
          color: colorScheme.onSurface,
          filter: filter,
          onValueChanged: onValueChanged,
        ),
        SizedBox(width: 12),
        AssetFilterItem(
          color: colorScheme.onSurface,
          value: filter,
          onValueChanged: onValueChanged,
        ),
        SizedBox(width: 12),
        AssetMoreItem(
          color: colorScheme.onSurface,
        ),
        SizedBox(width: 12),
      ],
      backgroundColor: colorScheme.surface,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          color: colorScheme.surface,
        ),
      ),
      pinned: true,
    );
  }
}
