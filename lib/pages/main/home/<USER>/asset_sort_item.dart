import 'package:flutter/material.dart';
import 'package:tasks/models/asset/asset_sort.dart';
import 'package:tasks/models/asset_filter.dart';
import 'package:tasks/widgets/dropdown/stable_dropdown_menu.dart';

class AssetSortItem extends StatefulWidget {
  final Color color;

  final AssetFilter filter;

  final ValueChanged<AssetFilter> onValueChanged;

  const AssetSortItem(
      {super.key,
      this.color = Colors.white,
      required this.filter,
      required this.onValueChanged});

  @override
  State<AssetSortItem> createState() => _AssetSortItemState();
}

class _AssetSortItemState extends State<AssetSortItem> {
  TextTheme get textTheme => Theme.of(context).textTheme;

  /// 处理排序类型变更
  void _handleSortTypeChange(AssetSortType sortType) {
    if (!mounted) return;

    final newFilter = widget.filter.copyWith(sortType: sortType);
    widget.onValueChanged(newFilter);
  }

  @override
  Widget build(BuildContext context) {
    final items = AssetSortType.values.map((sortType) {
      return StableDropdownMenuItem<AssetSortType>(
        value: sortType,
        title: sortType.description,
        icon: null,
        isSelected: sortType == widget.filter.sortType,
        onTap: () => _handleSortTypeChange(sortType),
      );
    }).toList();

    return StableDropdownMenu<AssetSortType>(
      items: items,
      menuWidth: 200,
      menuOffset: 80,
      child: Icon(
        Icons.swap_vert,
        color: widget.color.withValues(alpha: 0.8),
        size: 20,
      ),
    );
  }
}
