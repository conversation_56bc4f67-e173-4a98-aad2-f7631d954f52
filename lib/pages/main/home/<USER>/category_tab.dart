import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset_filter.dart';
import 'package:tasks/models/category.dart';

class CategoryTab extends StatefulWidget {
  final Color contentColor;

  final List<CategoryEntity> categories;

  final AssetFilter filter;

  final ValueChanged<CategoryEntity?> onSelected;

  CategoryTab({
    Key? key,
    required this.contentColor,
    required this.filter,
    required this.onSelected,
    required this.categories,
  }) : super(key: key);

  @override
  _CategoryTabState createState() => _CategoryTabState();
}

class _CategoryTabState extends State<CategoryTab>
    with SingleTickerProviderStateMixin {
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  List<CategoryEntity?> get categories => [null, ...widget.categories];

  int get selectedIndex {
    final index =
        categories.indexWhere((ele) => ele?.id == widget.filter.categoryId);
    if (index < 0) return 0;
    return index;
  }

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      itemCount: categories.length,
      padding: EdgeInsets.symmetric(horizontal: 10),
      itemBuilder: (context, index) {
        final selected = index == selectedIndex;
        return Center(
          child: InkWell(
            onTap: () {
              widget.onSelected(categories[index]);
            },
            child: Text(
              categories[index]?.name ?? l10n.commonAll,
              style: selected
                  ? textTheme.titleLarge!.copyWith(color: widget.contentColor)
                  : textTheme.bodySmall!.copyWith(color: widget.contentColor),
            ),
          ),
        );
      },
      separatorBuilder: (context, index) => SizedBox(width: 8),
    );
  }
}
