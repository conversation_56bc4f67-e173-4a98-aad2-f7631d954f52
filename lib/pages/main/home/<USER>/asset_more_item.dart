import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/widgets/dropdown/stable_dropdown_menu.dart';

enum MoreActionType {
  assetSettings,
  batchManagement,
  recycleBin,
}

extension MoreActionTypeExtension on MoreActionType {
  String description(BuildContext context) {
    final l10n = S.of(context);
    switch (this) {
      case MoreActionType.assetSettings:
        return l10n.homeAssetSettings;
      case MoreActionType.batchManagement:
        return l10n.homeBatchManagement;
      case MoreActionType.recycleBin:
        return l10n.assetRecycleBin;
    }
  }
}

class AssetMoreItem extends StatefulWidget {
  final Color color;

  const AssetMoreItem({
    super.key,
    this.color = Colors.white,
  });

  @override
  State<AssetMoreItem> createState() => _AssetMoreItemState();
}

class _AssetMoreItemState extends State<AssetMoreItem> {
  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  Widget build(BuildContext context) {
    final items = MoreActionType.values.map((action) {
      return StableDropdownMenuItem<MoreActionType>(
        value: action,
        title: action.description(context),
        onTap: () => _handleMoreAction(action),
      );
    }).toList();

    return StableDropdownMenu<MoreActionType>(
      items: items,
      menuWidth: 200,
      menuOffset: 80,
      child: Icon(
        Icons.add_circle_outline_rounded,
        color: widget.color.withValues(alpha: 0.8),
        size: 20,
      ),
    );
  }

  /// 处理更多按钮点击
  void _handleMoreAction(MoreActionType action) {
    switch (action) {
      case MoreActionType.assetSettings:
        context.push(Routers.assetSettings);
        break;
      case MoreActionType.batchManagement:
        context.push(Routers.batchManage);
        break;
      case MoreActionType.recycleBin:
        context.push(Routers.recycleBin);
        break;
    }
  }
}
