import 'package:mobx/mobx.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/asset/asset_price_method.dart';
import 'package:tasks/models/asset_filter.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/widgets/filter_options/filter_item.dart';

part 'filter_state.g.dart';

class FilterState = _FilterState with _$FilterState;

abstract class _FilterState with Store {
  final categoryRepo = getIt.get<CategoryLocalDataSource>();

  AssetFilter value;

  @observable
  List<CategoryEntity> categories = [];

  /// 分类可选项
  @computed
  List<FilterItem> get categoriesFilterOptions => [
        ...categories
            .map((e) => FilterItem(id: e.id, displayName: e.name))
            .toList()
      ];

  /// 状态可选项
  List<FilterItem> get statusFilterOptions => AssetStatus.values
      .map((i) => FilterItem(id: i.name, displayName: i.description))
      .toList();

  /// 计价类型可选项
  List<FilterItem> get priceMethodFilterOptions =>
      PriceMethodEntity.values.map((i) {
        return FilterItem(id: i.method.name, displayName: i.label);
      }).toList();

  @observable
  List<String> selectStatusIds = [];

  @observable
  List<String> selectCategoryIds = [];

  @observable
  List<String> selectPriceMethodIds = [];

  @computed
  AssetFilter get filter {
    final status = AssetStatus.values
        .where((i) => selectStatusIds.contains(i.name))
        .firstOrNull;
    final categoryId = selectCategoryIds.firstOrNull == FilterItem.filterAllId
        ? null
        : selectCategoryIds.firstOrNull;
    final priceMethod = PriceMethod.values
        .where((i) => selectPriceMethodIds.contains(i.name))
        .firstOrNull;
    return AssetFilter(
        status: status, categoryId: categoryId, priceMethod: priceMethod);
  }

  _FilterState({required this.value}) {
    selectStatusIds = value.statusIds;
    selectCategoryIds = value.categoryIds;
    selectPriceMethodIds = value.priceMethodIds;
    sync();
  }

  @action
  Future<void> sync() async {
    runInAction(() async {
      categories = await categoryRepo.getCategories();
    });
  }

  @action
  void onAssetStatusChanged(List<String> ids) {
    selectStatusIds = ids;
  }

  @action
  void onAssetCategoryChanged(List<String> ids) {
    selectCategoryIds = ids;
  }

  @action
  void onAssetPriceMethodChanged(List<String> ids) {
    selectPriceMethodIds = ids;
  }

  @action
  void reset() {
    selectStatusIds = [FilterItem.filterAllId];
    selectCategoryIds = [FilterItem.filterAllId];
    selectPriceMethodIds = [FilterItem.filterAllId];
  }
}
