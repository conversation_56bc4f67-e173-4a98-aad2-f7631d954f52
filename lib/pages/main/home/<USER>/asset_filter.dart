import 'package:flutter/material.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/asset/asset_price_method.dart';
import 'package:tasks/models/asset_filter.dart';
import 'package:tasks/pages/main/home/<USER>/asset_filter_popup.dart';

class AssetFilterItem extends StatefulWidget {
  final Color color;

  final AssetFilter value;

  final ValueChanged<AssetFilter> onValueChanged;

  AssetFilterItem(
      {super.key,
      this.color = Colors.white,
      required this.value,
      required this.onValueChanged}) {}

  @override
  State<AssetFilterItem> createState() => _AssetFilterItemState();
}

class _AssetFilterItemState extends State<AssetFilterItem> {
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  String filterText = "";

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadFilterText();
  }

  @override
  void didUpdateWidget(AssetFilterItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _loadFilterText();
    }
  }

  _loadFilterText() async {
    final text = await _getFilterText(widget.value);
    setState(() {
      filterText = text;
    });
  }

  // 获取分类数据
  _getFilterText(AssetFilter? filter) {
    if (filter?.priceMethod == null &&
        filter?.status == null &&
        filter?.categoryId == null) {
      return l10n.commonAll;
    }
    final statusText = AssetStatus.values
        .where((element) => element.name == filter?.status?.name)
        .firstOrNull
        ?.description;
    final priceMethodText = PriceMethod.values
        .where((element) => element.name == filter?.priceMethod?.name)
        .firstOrNull
        ?.description;
    final categoryText = getIt
        .get<CategoryLocalDataSource>()
        .getCategory(filter?.categoryId)
        ?.name;
    return [
      statusText,
      categoryText,
      priceMethodText,
    ].where((element) => element != null).join(" · ").trim();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        final result = await showFilterMenu(context, widget.value);
        if (result != null) {
          widget
              .onValueChanged(result.copyWith(sortType: widget.value.sortType));
        }
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 160),
            child: Text(
              filterText,
              style: textTheme.bodySmall!.copyWith(
                color: widget.color.withValues(alpha: 0.8),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(
            width: 2,
          ),
          Icon(
            Icons.filter_list_alt,
            color: widget.color.withValues(alpha: 0.8),
            size: 20,
          ),
        ],
      ),
    );
  }
}
