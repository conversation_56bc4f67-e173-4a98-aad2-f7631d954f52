import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/models/custom_theme/custom_theme.dart';
import 'package:tasks/pages/main/home/<USER>/asset_card/cycle_asset_card.dart';
import 'package:tasks/pages/main/home/<USER>/asset_card/daily_asset_card.dart';
import 'package:tasks/pages/main/home/<USER>/asset_card/usage_count_asset_card.dart';
import 'package:tasks/providers/theme_provider.dart';
import 'package:tasks/routers.dart';

class AssetCard extends StatefulWidget {
  final AssetCardEntity asset;

  final bool enableSlide;

  final ValueChanged<AssetCardEntity> onFavorite;

  final ValueChanged<AssetCardEntity> switchInService;

  final ValueChanged<AssetCardEntity>? delete;

  const AssetCard({
    Key? key,
    this.enableSlide = false,
    required this.asset,
    required this.onFavorite,
    required this.switchInService,
    this.delete,
  }) : super(key: key);

  @override
  State<AssetCard> createState() => _AssetCardState();
}

class _AssetCardState extends State<AssetCard> {
  AssetCardEntity get assetCard => widget.asset;

  S get l10n => S.of(context);

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = context.watch<ThemeProvider>();
    final customTheme = themeProvider.customTheme;
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: InkWell(
        onTap: _handleTap,
        child: Slidable(
            enabled: widget.enableSlide,
            startActionPane: ActionPane(
                motion: const ScrollMotion(),
                extentRatio: 0.3,
                children: [
                  SlidableAction(
                    onPressed: (context) {
                      widget.onFavorite(assetCard);
                    },
                    backgroundColor: widget.asset.isFavorite
                        ? Colors.green.withValues(alpha: 0.8)
                        : Color(0xFFf0974d),
                    foregroundColor: Colors.white,
                    icon: widget.asset.isFavorite
                        ? Icons.favorite_border
                        : Icons.favorite,
                  ),
                ]),
            endActionPane: ActionPane(
                motion: const ScrollMotion(),
                extentRatio: 0.6,
                children: [
                  // 退役
                  SlidableAction(
                    onPressed: (context) {
                      widget.switchInService(assetCard);
                    },
                    backgroundColor: widget.asset.isInService
                        ? CustomTheme.classicRetiredColor()
                        : widget.asset.isFavorite
                            ? CustomTheme.classicFavoriteColor()
                            : CustomTheme.classicNormalColor(),
                    foregroundColor: Colors.white,
                    icon: widget.asset.isInService
                        ? Icons.archive_outlined
                        : Icons.archive_rounded,
                  ),
                  // 删除
                  SlidableAction(
                    onPressed: (context) {
                      showConfirm(context, content: l10n.confirmDeleteTips,
                          onConfirm: () {
                        widget.delete?.call(assetCard);
                      });
                    },
                    icon: Icons.delete_rounded,
                    backgroundColor: Color(0xFFea605a),
                  )
                ]),
            child: buildItem(customTheme)),
      ),
    );
  }

  Container buildItem(CustomTheme customTheme) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 12,
      ),
      decoration: BoxDecoration(
        gradient: _getBackgroundGradient(customTheme),
      ),
      child: Stack(
        children: [
          if (assetCard.showDailyPrice || assetCard.showNoPrice)
            DailyAssetCard(
                contentColor: _getOnContainerColor(customTheme),
                assetCard: assetCard),
          if (assetCard.showUsageCountPrice)
            UsageCountAssetCard(
                contentColor: _getOnContainerColor(customTheme),
                assetCard: assetCard),
          if (assetCard.showCyclePrice)
            CycleAssetCard(
                contentColor: _getOnContainerColor(customTheme),
                assetCard: assetCard),
        ],
      ),
    );
  }

  LinearGradient _getBackgroundGradient(CustomTheme theme) {
    // 判断状态并返回对应的颜色
    if (!assetCard.isInService) {
      return _createLinearGradient(
        theme.retiredCard.startColor, // 冷灰色
        theme.retiredCard.endColor, // 深灰绿
      );
    }
    if (assetCard.isFavorite) {
      return _createLinearGradient(
        theme.favoriteCard.startColor,
        theme.favoriteCard.endColor,
      );
    }
    return _createLinearGradient(
      theme.normalCard.startColor, // 淡绿色
      theme.normalCard.endColor, // 草绿色
    );
  }

  Color _getOnContainerColor(CustomTheme theme) {
    // 判断状态并返回对应的颜色
    if (!assetCard.isInService) {
      return theme.retiredCard.onContainerColor(context);
    }
    if (assetCard.isFavorite) {
      return theme.favoriteCard.onContainerColor(context);
    }
    return theme.normalCard.onContainerColor(context);
  }

  // 创建线性渐变的辅助函数
  LinearGradient _createLinearGradient(Color startColor, Color endColor) {
    return LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [startColor, endColor],
    );
  }

  // Future<bool?> _handleDismiss(
  //     BuildContext context, DismissDirection direction) async {
  //   if (direction == DismissDirection.startToEnd) {
  //     widget.onFavorite(assetCard);
  //     return false;
  //   } else {
  //     widget.switchInService(assetCard);
  //     return false;
  //   }
  // }

  Future<void> _handleTap() async {
    context.push("${Routers.editAsset}?assetId=${widget.asset.id}");
  }
}
