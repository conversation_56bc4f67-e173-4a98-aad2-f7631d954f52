import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/custom_theme/custom_card_theme.dart';
import 'package:tasks/utils/value_display_utils.dart';

class ToggleableValueDisplay extends StatefulWidget {
  final String label;
  final String value;
  final CustomCardTheme customTheme;
  final bool isVisible; // 当前是否显示真实值
  final ValueChanged<bool> onValueChanged; // 值变化回调

  const ToggleableValueDisplay({
    Key? key,
    required this.label,
    required this.value,
    required this.customTheme,
    required this.isVisible,
    required this.onValueChanged,
  }) : super(key: key);

  @override
  State<ToggleableValueDisplay> createState() => _ToggleableValueDisplayState();
}

class _ToggleableValueDisplayState extends State<ToggleableValueDisplay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  S get l10n => S.of(context);

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleVisibility() async {
    // 播放点击动画
    await _animationController.forward();
    await _animationController.reverse();

    // 通知外部状态变化
    widget.onValueChanged(!widget.isVisible);

    // 提供触觉反馈
    if (mounted) {
      HapticFeedback.lightImpact();
    }
  }

  String get _displayValue {
    return widget.isVisible
        ? widget.value
        : ValueDisplayUtils.generateHiddenValue(widget.value);
  }

  IconData get _toggleIcon {
    return widget.isVisible ? Icons.visibility : Icons.visibility_off;
  }

  String get _toggleTooltip {
    return widget.isVisible ? l10n.homeHideAmount : l10n.homeShowAmount;
  }

  @override
  Widget build(BuildContext context) {
    final textColor = widget.customTheme.onContainerColor(context);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: _toggleVisibility,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        widget.label,
                        style: textTheme.bodyMedium!.copyWith(
                          color: textColor.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.5,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Tooltip(
                        message: _toggleTooltip,
                        child: Icon(
                          _toggleIcon,
                          size: 16,
                          color: textColor.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 200),
                    transitionBuilder:
                        (Widget child, Animation<double> animation) {
                      return FadeTransition(
                        opacity: animation,
                        child: SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0.0, 0.3),
                            end: Offset.zero,
                          ).animate(animation),
                          child: child,
                        ),
                      );
                    },
                    child: Text(
                      _displayValue,
                      key: ValueKey(widget.isVisible),
                      style: textTheme.headlineMedium!.copyWith(
                        color: textColor,
                        fontWeight: FontWeight.bold,
                        letterSpacing: -0.5,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
