import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/config/config_repo.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/modal/upgrade/upgradle_vip_utils.dart';
import 'package:tasks/modal/upgrade/widgets/apple/apple_upgrade_state.dart';
import 'package:tasks/pages/main/home/<USER>';
import 'package:tasks/pages/main/usercenter/usercenter.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/utils/platform.dart';
import 'package:tasks/utils/string_ext.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/system_ui_wrapper.dart';

import 'main_state.dart';

class MainPage extends StatefulWidget {
  final String? path;

  MainPage({
    this.path,
  });

  @override
  _MainPageState createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  final MainState mainState = getIt.get();

  DateTime? lastPopTime;

  @override
  void initState() {
    super.initState();
    if (!widget.path.isNullOrEmpty) {
      Future.delayed(Duration(microseconds: 300), () {
        if (!context.mounted) return;
        context.push(widget.path!);
      });
    }
    // 同步配置数据
    getIt.get<ConfigRepo>().syncConfig();
    // 加载商品
    if (isIos()) {
      getIt.get<AppleUpgradeState>().loadProducts();
    }
    // 事件
    initEvent();
  }

  void initEvent() {
    reaction((_) => mainState.event, (e) {
      if (e == null) {
        return;
      }
      mainState.clearEvent();
      switch (e) {
        case MainEvent.needVip:
          showUpgradeVip(context);
          break;
        case MainEvent.showAddAsset:
          context.push(Routers.addAsset);
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (canPop, didPop) {
        if (canPop) {
          return;
        }
        if (lastPopTime == null ||
            DateTime.now().difference(lastPopTime!).inSeconds > 2) {
          ToastUtils.success(context, l10n.commonBackTips);
          setState(() {
            lastPopTime = DateTime.now();
          });
        } else {
          // 退出应用
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        appBar: null,
        body: Observer(builder: (context) {
          return IndexedStack(
            index: mainState.selectedIndex,
            children: [
              HomePage(
                key: homePageKey,
                onScrollStateChanged: (showScrollToTop) {
                  mainState.setShowGoTop(showScrollToTop);
                },
              ),
              SizedBox(),
              UserCenterPage(),
            ],
          );
        }),
        bottomNavigationBar: Observer(builder: (context) {
          return NavigationBar(
            selectedIndex: mainState.selectedIndex,
            height: 56,
            backgroundColor: colorScheme.surface,
            labelBehavior: NavigationDestinationLabelBehavior.alwaysHide,
            onDestinationSelected: (index) {
              if (index == 1) {
                return;
              }
              // 如果点击首页图标且当前显示回到顶部状态
              if (index == 0 && mainState.goTop) {
                // 触发回到顶部
                homePageKey.currentState?.scrollToTop();
                return;
              }
              mainState.setSelectedIndex(index);
            },
            destinations: [
              Observer(builder: (context) {
                return NavigationDestination(
                  icon: Icon(mainState.showGoTop
                      ? Icons.arrow_upward
                      : Icons.home_rounded),
                  selectedIcon: Icon(mainState.showGoTop
                      ? Icons.arrow_upward
                      : Icons.home_rounded),
                  label: l10n.commonHome,
                );
              }),
              SizedBox(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: GestureDetector(
                    onTap: mainState.handleAddAsset,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(
                        Icons.add,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ),
              NavigationDestination(
                icon: Icon(Icons.person_2),
                selectedIcon: Icon(Icons.person_2),
                label: l10n.commonMine,
              ),
            ],
          );
        }),
      ),
    ).withSystemUi();
  }
}
