import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/utils/toast_utils.dart';

part 'main_state.g.dart';

enum MainEvent { needVip, showAddAsset }

@injectable
class MainState = _MainState with _$MainState;

abstract class _MainState with Store {
  final AssetRepo repo;

  _MainState({required this.repo});

  @observable
  bool showGoTop = false;

  @observable
  int selectedIndex = 0;

  @computed
  bool get goTop => showGoTop && selectedIndex == 0;

  @observable
  MainEvent? event;

  @action
  void setSelectedIndex(int index) {
    selectedIndex = index;
  }

  @action
  void setShowGoTop(bool value) {
    showGoTop = value;
  }

  @action
  handleAddAsset() async {
    // 资产达到上限时提示
    final enable = await repo.checkAddAvailable();
    if (!enable) {
      ToastUtils.show(S.current.assetLimitTips);
      event = MainEvent.needVip;
      return;
    }
    // 正常跳转添加页面
    event = MainEvent.showAddAsset;
  }

  @action
  void clearEvent() {
    event = null;
  }
}
