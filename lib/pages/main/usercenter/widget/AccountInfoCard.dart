import 'package:flutter/material.dart';
import 'package:tasks/data/user/models/user_model.dart';
import 'package:tasks/generated/assets.gen.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/widgets/app_logo.dart';

class AccountInfoCard extends StatelessWidget {
  final UserModel? userModel;

  final String? avatarUrl;

  final VoidCallback? onTap;

  const AccountInfoCard({
    this.userModel,
    this.onTap,
    this.avatarUrl,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    bool isPaidVip = userModel?.vipInfo?.paidVip == true;
    bool isFreeVip = userModel?.vipInfo?.freeVip == true;
    final expiredAt = userModel?.vipInfo?.expiredAt;
    bool isLogin = userModel != null;

    S l10n = S.of(context);

    ColorScheme colorScheme = Theme.of(context).colorScheme;

    TextTheme textTheme = Theme.of(context).textTheme;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(left: 12, top: 20, right: 12),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              AppLogo(
                avatarUrl: avatarUrl,
              ),
              Flexible(
                flex: 1,
                child: ListTile(
                  title: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        !isLogin
                            ? l10n.ucenterNotLoginTips
                            : (userModel?.nickname ?? userModel?.username) ??
                                '',
                        style: textTheme.titleMedium,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      SizedBox(
                        height: 4,
                      ),
                      // 终生会员
                      Visibility(
                        visible: isPaidVip,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Image.asset(
                              Assets.images.icVip.path,
                              width: 24,
                            ),
                            SizedBox(
                              width: 8,
                            ),
                            Text(
                              l10n.ucenterLifetimeMember,
                              style: textTheme.titleSmall!
                                  .copyWith(color: Color(0xFFD5B05B)),
                            )
                          ],
                        ),
                      ),
                      Visibility(
                        visible: isFreeVip,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              l10n.ucenterFreeMemberFormat(expiredAt ?? "无"),
                              style: textTheme.titleSmall!
                                  .copyWith(color: Color(0xFFD5B05B)),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Visibility(
                visible: isLogin,
                child: Row(
                  children: [
                    Icon(
                      Icons.chevron_right,
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
