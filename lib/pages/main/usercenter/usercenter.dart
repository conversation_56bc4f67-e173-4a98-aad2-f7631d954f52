import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tasks/data/config/config_repo.dart';
import 'package:tasks/generated/assets.gen.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/cdkey_input/cdkey_input_utils.dart';
import 'package:tasks/modal/upgrade/upgradle_vip_utils.dart';
import 'package:tasks/pages/main/usercenter/user_center_state.dart';
import 'package:tasks/pages/main/usercenter/widget/AccountInfoCard.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/providers/theme_provider.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/utils/clipboard_utils.dart';
import 'package:tasks/utils/platform.dart';
import 'package:tasks/utils/url_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

class UserCenterPage extends StatefulWidget {
  @override
  State<UserCenterPage> createState() => _UserCenterPageState();
}

class _UserCenterPageState extends State<UserCenterPage> {
  late UserCenterState state;

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = UserCenterState();
    reaction((_) => state.event, (r) async {
      state.clearEvent();
      switch (r) {
        case UCenterEvent.goLogin:
          context.push(Routers.login);
          break;
        default:
          break;
      }
    });
  }

  @override
  void dispose() {
    state.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return StateWarp(
        store: state,
        child: Scaffold(
          backgroundColor: colorScheme.surface,
          appBar: CommonAppBar(
            centerTitle: false,
            backgroundColor: colorScheme.surface,
            title: l10n.ucenterUserCenter,
            actions: [
              // 免费获取会员活动入口
              Observer(builder: (context) {
                return Visibility(
                  visible: state.showMembershipActivity,
                  child: InkWell(
                    onTap: () {
                      context.push(Routers.membershipActivity);
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Image.asset(
                        Assets.images.freeGift.path,
                        width: 24,
                        height: 24,
                      ),
                    ),
                  ),
                );
              }),
              GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    context.read<ThemeProvider>().toggleThemeMode(isDark);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Icon(
                      isDark
                          ? Icons.light_mode_outlined
                          : Icons.dark_mode_outlined,
                      size: 24,
                      color: textTheme.bodySmall!.color,
                    ),
                  )),
              GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    context.push(Routers.messageList);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Icon(
                      Icons.notifications_outlined,
                      size: 24,
                      color: textTheme.bodySmall!.color,
                    ),
                  )),
              GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    context.push(Routers.setting);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Icon(
                      Icons.settings_outlined,
                      size: 24,
                      color: textTheme.bodySmall!.color,
                    ),
                  )),
            ],
          ),
          body: SmartRefresher(
            onRefresh: state.onRefresh,
            enablePullUp: false,
            controller: state.refreshController,
            child: ListView(
              children: [
                Observer(builder: (context) {
                  return AccountInfoCard(
                    userModel: state.userInfo,
                    avatarUrl: state.avatarUrl,
                    onTap: () async {
                      context.push(Routers.editUserInfo);
                    },
                  );
                }),
                // 订阅信息部分
                Observer(builder: (context) {
                  return Visibility(
                    visible: !state.isVip,
                    child: SectionWarp(
                        title: l10n.ucenterSubscriptionInfo,
                        children: [
                          ListTile(
                            leading: Icon(Icons.emoji_events),
                            title: Text(
                              l10n.ucenterFreeVersion,
                              style: textTheme.bodyMedium,
                            ),
                            trailing: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(l10n.ucenterUpgrade,
                                  style: textTheme.bodyMedium),
                            ),
                            onTap: () async {
                              final isLogin = await state.checkLogin();
                              if (!isLogin) {
                                return;
                              }
                              if (!state.isVip) {
                                showUpgradeVip(context);
                                return;
                              }
                            },
                          ),
                          // 仅非会员展示
                          Observer(builder: (c) {
                            return Visibility(
                              visible: state.isLogin & !isIos(),
                              child: ListTile(
                                leading: Icon(Icons.card_giftcard),
                                title: Text(
                                  l10n.ucenterRedeemCode,
                                  style: textTheme.bodyMedium,
                                ),
                                trailing: Icon(Icons.chevron_right),
                                onTap: () async {
                                  final isLogin = await state.checkLogin();
                                  if (!isLogin) {
                                    return;
                                  }
                                  final result = await showBottomInputDialog(
                                    context,
                                    title: l10n.ucenterRedeemCodeTitle,
                                    // 可选
                                    hintText: l10n.ucenterRedeemCodeHint,
                                  );
                                  if (result.isFailure) return;
                                  state.exchangeVip(result.data ?? "");
                                },
                              ),
                            );
                          })
                        ]),
                  );
                }),

                // 分类设置部分
                SectionWarp(title: l10n.ucenterAsset, children: [
                  ListTile(
                    leading: Icon(Icons.category_outlined),
                    title: Text(
                      l10n.ucenterManageCategory,
                      style: textTheme.bodyMedium,
                    ),
                    trailing: Icon(Icons.chevron_right),
                    onTap: () {
                      context.push(Routers.categoryManage);
                    },
                  ),
                  ListTile(
                    leading: Icon(Icons.shopping_bag_outlined),
                    title: Text(
                      l10n.ucenterManageBuyChannel,
                      style: textTheme.bodyMedium,
                    ),
                    trailing: Icon(Icons.chevron_right),
                    onTap: () {
                      context.push(Routers.buyChannelManage);
                    },
                  ),
                  ListTile(
                    leading: Icon(Icons.location_on_outlined),
                    title: Text(
                      l10n.ucenterManageStorageLocation,
                      style: textTheme.bodyMedium,
                    ),
                    trailing: Icon(Icons.chevron_right),
                    onTap: () {
                      context.push(Routers.storageLocationManage);
                    },
                  ),
                  ListTile(
                    leading: Icon(Icons.video_label),
                    title: Text(
                      l10n.homeAssetSettings,
                      style: textTheme.bodyMedium,
                    ),
                    trailing: Icon(Icons.chevron_right),
                    onTap: () {
                      context.push(Routers.assetSettings);
                    },
                  ),
                ]),
                // 数据管理 部分
                SectionWarp(title: l10n.ucenterDataManagement, children: [
                  ListTile(
                    leading: Icon(Icons.data_saver_on),
                    title: Text(
                      l10n.ucenterCacheCenter,
                      style: textTheme.bodyMedium,
                    ),
                    trailing: Icon(Icons.chevron_right),
                    onTap: () async {
                      // final isLogin = await state.checkLogin();
                      // if (!isLogin) {
                      //   return;
                      // }
                      if (!context.mounted) return;
                      context.push(Routers.cacheCenter);
                    },
                  ),
                ]),
                SizedBox(
                  height: 100,
                ),
                // 版权信息
                Column(
                  children: [
                    InkWell(
                      onTap: () {
                        UrlUtils.openUrlOut("https://beian.miit.gov.cn/");
                      },
                      child: Text(
                        "渝ICP备20001082号-6A",
                        style: textTheme.labelMedium,
                      ),
                    ),
                    SizedBox(
                      height: 12,
                    ),
                    InkWell(
                      onTap: () {
                        final email = ConfigRepo.getAllConfig().feedbackEmail;
                        ClipboardUtils.copyText(context, email);
                        UrlUtils.openUrlOut("mailto:$email");
                      },
                      child: Text(
                        "客服：周一至周五 9:00-18:00",
                        style: textTheme.labelMedium,
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ));
  }
}
