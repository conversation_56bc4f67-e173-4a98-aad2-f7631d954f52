import 'package:mobx/mobx.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/config/config_repo.dart';
import 'package:tasks/data/user/models/user_model.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/config/config_entity.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/user_store.dart';

part 'user_center_state.g.dart';

enum UCenterEvent { goLogin }

class UserCenterState = _UserCenterState with _$UserCenterState;

abstract class _UserCenterState extends BaseStore with Store {
  final repo = getIt.get<UserRepo>();
  final configRepo = getIt.get<ConfigRepo>();

  final refreshController = RefreshController(initialRefresh: false);

  UserStore userStore = getIt.get();

  @observable
  ConfigEntity? config;

  @computed
  UserModel? get userInfo => userStore.userModel;

  @computed
  String? get avatarUrl => userStore.userModel?.avatarUrl;

  @observable
  bool excludeRetired = false;

  @observable
  UCenterEvent? event;

  @observable
  Result<void>? cacheResult;

  @computed
  bool get isLogin {
    return userInfo != null;
  }

  @computed
  bool get isVip {
    return userInfo?.paidVip == true;
  }

  @computed
  bool get showMembershipActivity {
    return !isVip && config?.showMembershipActivity == true;
  }

  _UserCenterState() {
    getUserInfo();
    getConfig();
  }

  @action
  Future<bool> checkLogin() async {
    await getUserInfo();
    if (userInfo != null) {
      return true;
    }
    event = UCenterEvent.goLogin;
    return false;
  }

  @action
  Future<void> getUserInfo() async {
    runWithLoading(() async {
      await userStore.syncFetchUserInfo();
    });
  }

  /// 获取应用配置信息
  @action
  Future<void> getConfig() async {
    runWithLoading(() async {
      config = await configRepo.syncConfig();
    });
  }

  @action
  Future<void> exchangeVip(String cdkey) async {
    runWithLoading(() async {
      final result = await repo.exchangeVip(cdkey);
      if (result.isSuccess) {
        await userStore.syncFetchUserInfo();
        return;
      }
    });
  }

  @action
  Future<void> onRefresh() async {
    await userStore.syncFetchUserInfo();
    refreshController.refreshCompleted();
  }

  @action
  Future<void> logout() async {
    userStore.logout();
  }

  @action
  void clearEvent() {
    event = null;
  }

  void dispose() {
    refreshController.dispose();
  }
}
