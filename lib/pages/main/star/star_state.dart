import 'package:mobx/mobx.dart';
import 'package:tasks/data/config/app_config.dart';
import 'package:tasks/data/asset/asset_local_data_source.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/providers/asset_store.dart';
import 'package:tasks/providers/category_store.dart';

part 'star_state.g.dart';

class StarState = StarStateBase with _$StarState;

abstract class StarStateBase with Store {
  AppConfigDataSource appConfigDataSource = getIt.get();
  AssetStore assetStore = getIt.get();
  CategoryStore categoryStore = getIt.get();
  final _assetRepo = getIt.get<AssetRepo>();
  final _datasource = getIt.get<AssetLocalDataSource>();
  final repo = getIt.get<AssetRepo>();
  final userRepo = getIt.get<UserRepo>();

  @computed
  List<CategoryEntity> get categories => categoryStore.categories;

  @computed
  List<AssetCardEntity> get assets {
    final assets =
        _assetRepo.getStaredList(assetStore.assets, categoryStore.categories);
    return assets;
  }

  StarStateBase() {}

  @action
  Future<void> changeAssetInService(String id) async {
    final asset = await _datasource.getAssetById(id);
    if (asset != null) {
      final updated = asset.copyWith(
          isInService: !asset.isInService, retireDate: DateTime.now());
      assetStore.updateAsset(updated);
    }
  }

  @action
  Future<void> toggleFavorite(String id) async {
    final asset = await _datasource.getAssetById(id);
    if (asset != null) {
      final updated = asset.copyWith(isFavorite: !asset.isFavorite);
      assetStore.updateAsset(updated);
    }
  }
}
