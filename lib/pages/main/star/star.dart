import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';

import 'package:tasks/generated/l10n.dart';
import 'package:tasks/pages/main/home/<USER>';
import 'package:tasks/pages/main/star/star_state.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/empty_state_widget.dart';

class StarPage extends StatefulWidget {
  const StarPage({super.key});

  @override
  State<StarPage> createState() => _StarPageState();
}

class _StarPageState extends State<StarPage> {
  late StarState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = StarState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: colorScheme.surface,
      child: SafeArea(
        child: Column(
          children: [
            CommonAppBar(
              backgroundColor: colorScheme.surface,
              title: l10n.commonStar,
            ),
            // 数据为空时展示
            Observer(builder: (context) {
              return Visibility(
                  visible: state.assets.isEmpty,
                  child: Expanded(
                    child: EmptyStateWidget(
                      title: l10n.homeEmptyStarListTitle,
                      subtitle: l10n.homeEmptyStarListDesc,
                      backgroundColor: colorScheme.surface,
                      icon: Icons.account_balance_wallet_outlined,
                    ),
                  ));
            }),
            // 资产列表
            Observer(builder: (context) {
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 6.0, right: 6),
                  child: RecordAssetList(
                    assets: state.assets,
                    onFavorite: (a) => state.toggleFavorite(a.id),
                    switchInService: (a) => state.changeAssetInService(a.id),
                    enableSwap: false,
                  ),
                ),
              );
            })
          ],
        ),
      ),
    );
  }
}
