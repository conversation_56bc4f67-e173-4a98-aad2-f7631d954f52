import 'package:mobx/mobx.dart';
import 'package:tasks/data/storage_location/storage_location_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/storage_location_store.dart';
import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';

part 'edit_storage_location_state.g.dart';

enum EditStorageLocationEvent {
  addSuccess,
  editSuccess,
}

class EditStorageLocationState = _EditStorageLocationState with _$EditStorageLocationState;

abstract class _EditStorageLocationState extends BaseStore with Store {
  StorageLocationLocalDataSource _dataSource = getIt.get();

  StorageLocationStore storageLocationStore = getIt.get();

  BuildContext? _context;

  @observable
  String? id;

  @observable
  String name = "";

  @computed
  bool get isEditMode => id != null;

  @computed
  String get title {
    if (_context == null) return isEditMode ? "编辑存放位置" : "添加存放位置";
    final l10n = S.of(_context!);
    return isEditMode ? l10n.assetStorageLocationEdit : l10n.assetStorageLocationAdd;
  }

  @computed
  String get actionText {
    if (_context == null) return isEditMode ? "保存" : "添加";
    final l10n = S.of(_context!);
    return isEditMode ? l10n.commonSave : l10n.commonAdd;
  }

  @observable
  EditStorageLocationEvent? event;

  _EditStorageLocationState(String? locationId) {
    init(locationId);
  }

  void setContext(BuildContext context) {
    _context = context;
  }

  @action
  void init(String? locationId) {
    if (locationId != null) {
      id = locationId;
      final location = _dataSource.getStorageLocation(locationId);
      if (location != null) {
        name = location.name;
      }
    }
  }

  @action
  void onNameChanged(String value) {
    name = value;
  }

  @action
  @override
  void clearError() {
    super.clearError();
  }

  @action
  Future<void> confirm() async {
    if (name.isEmpty) {
      final errorMsg = _context != null 
          ? S.of(_context!).assetStorageLocationNameRequired 
          : "存放位置名称不能为空";
      setError(errorMsg);
      return;
    }
    // 添加
    if (!isEditMode) {
      await _dataSource.addStorageLocation(name);
      storageLocationStore.fetchStorageLocations();
      event = EditStorageLocationEvent.addSuccess;
      return;
    }
    // 编辑
    await _dataSource.updateStorageLocation(id!, name);
    storageLocationStore.fetchStorageLocations();
    event = EditStorageLocationEvent.editSuccess;
  }
}
