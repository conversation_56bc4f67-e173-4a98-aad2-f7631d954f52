import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/models/storage_location.dart';
import 'package:tasks/pages/storage_location/manage/storage_location_manage_state.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/widgets/common_app_bar.dart';

class StorageLocationManagePage extends StatefulWidget {
  @override
  _StorageLocationManagePageState createState() => _StorageLocationManagePageState();
}

class _StorageLocationManagePageState extends State<StorageLocationManagePage> {
  late StorageLocationManageState state;

  S get l10n => S.of(context);

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    super.initState();
    state = StorageLocationManageState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surfaceContainer,
        title: l10n.assetStorageLocationManage,
        actions: [
          TextButton(
            child: Text(
              l10n.commonAdd,
              style:
                  textTheme.titleMedium!.copyWith(color: colorScheme.primary),
            ),
            onPressed: () {
              context.push(Routers.storageLocationEdit);
            },
          ),
        ],
      ),
      body: Observer(builder: (context) {
        return ReorderableListView.builder(
          itemCount: state.storageLocations.length,
          onReorder: (oldIndex, newIndex) {
            state.reorderStorageLocations(oldIndex, newIndex);
          },
          itemBuilder: (context, index) {
            final item = state.storageLocations[index];
            return _buildLocationItem(item, index);
          },
        );
      }),
    );
  }

  Widget _buildLocationItem(StorageLocationEntity item, int index) {
    return Column(
      key: Key(item.id.toString()),
      children: [
        Container(
          color: colorScheme.surface,
          child: ListTile(
            leading: InkWell(
                onTap: () {
                  showConfirm(
                    context,
                    title: l10n.delete,
                    content: l10n.assetStorageLocationDeleteConfirm,
                    onConfirm: () {
                      state.deleteStorageLocation(item.id);
                    },
                  );
                },
                child: Icon(Icons.remove_circle, color: colorScheme.error)),
            title: Padding(
              padding: EdgeInsets.only(left: 8),
              child: Text(item.name),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [Icon(Icons.drag_indicator)],
            ),
            onTap: () {
              context.push("${Routers.storageLocationEdit}?locationId=${item.id}");
            },
          ),
        ),
        if (index < state.storageLocations.length - 1)
          Divider(
            height: 1,
            thickness: 1,
            color: colorScheme.outlineVariant,
            indent: 16,
            endIndent: 16,
          ),
      ],
    );
  }
}
