import 'package:mobx/mobx.dart';
import 'package:tasks/data/storage_location/storage_location_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/storage_location.dart';
import 'package:tasks/providers/storage_location_store.dart';

part 'storage_location_manage_state.g.dart';

class StorageLocationManageState = _StorageLocationManageState with _$StorageLocationManageState;

abstract class _StorageLocationManageState with Store {
  final StorageLocationLocalDataSource _dataSource = getIt.get();

  StorageLocationStore storageLocationStore = getIt.get();

  @computed
  ObservableList<StorageLocationEntity> get storageLocations => storageLocationStore.storageLocations;

  @action
  Future<void> reorderStorageLocations(int oldIndex, int newIndex) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    // 在内存中直接更新顺序
    final StorageLocationEntity item = storageLocations.removeAt(oldIndex);
    storageLocations.insert(newIndex, item);

    // 更新所有项的order
    for (int i = 0; i < storageLocations.length; i++) {
      storageLocations[i].order = i;
    }

    // 异步保存到本地
    await _dataSource.saveStorageLocations(storageLocations);
    storageLocationStore.fetchStorageLocations();
  }

  @action
  Future<void> deleteStorageLocation(String id) async {
    await _dataSource.deleteStorageLocation(id);
    storageLocationStore.fetchStorageLocations();
  }
}
