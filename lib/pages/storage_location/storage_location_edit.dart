import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/pages/storage_location/edit_storage_location_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';

class StorageLocationEditPage extends StatefulWidget {
  final String? locationId;

  const StorageLocationEditPage({Key? key, this.locationId}) : super(key: key);

  @override
  _StorageLocationEditPageState createState() => _StorageLocationEditPageState();
}

class _StorageLocationEditPageState extends State<StorageLocationEditPage> {
  late EditStorageLocationState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  final nameController = TextEditingController();

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = EditStorageLocationState(
      widget.locationId,
    );
    
    // 在下一帧设置context
    WidgetsBinding.instance.addPostFrameCallback((_) {
      state.setContext(context);
    });
    
    // 事件监听
    reaction((_) => state.event, (r) async {
      state.clearError();
      // 编辑成功
      if (r == EditStorageLocationEvent.editSuccess) {
        ToastUtils.success(context, l10n.assetStorageLocationEditSuccess);
        context.pop(true);
        return;
      }
      // 添加成功
      if (r == EditStorageLocationEvent.addSuccess) {
        ToastUtils.success(context, l10n.assetStorageLocationAddSuccess);
        context.pop(true);
      }
    });
  }

  @override
  void dispose() {
    nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: CommonAppBar(
          backgroundColor: colorScheme.surface,
          title: state.title,
          actions: [
            TextButton(
              onPressed: state.confirm,
              child: Text(
                state.actionText,
                style: TextStyle(color: colorScheme.primary),
              ),
            ),
          ]),
      body: StateWarp(
        store: state,
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            _buildTitle(l10n.assetStorageLocationName),
            SizedBox(height: 12),
            ClearInputTextField(
              value: state.name,
              hintText: l10n.assetStorageLocationNamePlaceholder,
              onChange: (v) {
                state.onNameChanged(v);
              },
              fillColor: colorScheme.surfaceContainer,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium,
    );
  }
}
