import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tasks/common/global/GlobalInfo.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/flavor.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/utils/clipboard_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

class DebugPage extends StatefulWidget {
  const DebugPage({super.key});

  @override
  State<DebugPage> createState() => _DebugPageState();
}

class _DebugPageState extends State<DebugPage> {
  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: "调试信息",
      ),
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          // 应用信息
          SectionWarp(
            title: "应用信息",
            children: [
              _buildInfoTile(
                "应用名称",
                GlobalInfo.appName.isNotEmpty ? GlobalInfo.appName : "极简记物",
                Icons.apps,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "应用版本",
                "v${GlobalInfo.appVersion}",
                Icons.info_outline,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "应用包名",
                GlobalInfo.appId,
                Icons.inventory_2_outlined,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "系统架构",
                GlobalInfo.appArch,
                Icons.memory,
              ),
            ],
          ),

          SizedBox(height: 16),

          // 渠道信息
          SectionWarp(
            title: "渠道信息",
            children: [
              _buildInfoTile(
                "当前渠道",
                getChannel(),
                Icons.store,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "是否Google渠道",
                isGoogle() ? "是" : "否",
                Icons.android,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "原价",
                getFlavorValue(FlavorConstants.originPrice) ?? "未设置",
                Icons.attach_money,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "折扣价",
                getFlavorValue(FlavorConstants.discountPrice) ?? "未设置",
                Icons.local_offer,
              ),
            ],
          ),

          SizedBox(height: 16),

          // 接口信息
          SectionWarp(
            title: "接口信息",
            children: [
              _buildInfoTile(
                "主接口地址",
                getFlavorValue(FlavorConstants.baseUrl) ?? "未设置",
                Icons.cloud,
                copyable: true,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "备用接口地址",
                getFlavorValue(FlavorConstants.backupUrl) ?? "未设置",
                Icons.cloud_queue,
                copyable: true,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "连接超时",
                "${ApiConstants.connectTimeout}ms",
                Icons.timer,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "接收超时",
                "${ApiConstants.receiveTimeout}ms",
                Icons.timer_off,
              ),
            ],
          ),

          SizedBox(height: 16),

          // 构建信息
          SectionWarp(
            title: "构建信息",
            children: [
              _buildInfoTile(
                "Flutter版本",
                "3.29.2",
                Icons.flutter_dash,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "构建时间",
                DateTime.now().toString().split('.')[0],
                Icons.access_time,
              ),
              Divider(indent: 16, endIndent: 16),
              _buildInfoTile(
                "调试模式",
                kDebugMode ? "开发版本" : "正式版本",
                Icons.bug_report,
              ),
            ],
          ),

          SizedBox(height: 32),

          // 底部提示
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.info_outline,
                  color: colorScheme.primary,
                  size: 32,
                ),
                SizedBox(height: 8),
                Text(
                  "调试信息页面",
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  "此页面仅用于开发调试，包含应用的详细配置信息",
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoTile(
    String title,
    String value,
    IconData icon, {
    bool copyable = false,
  }) {
    return ListTile(
      leading: Icon(icon, color: colorScheme.primary),
      title: Text(
        title,
        style: textTheme.bodyMedium,
      ),
      subtitle: Text(
        value,
        style: textTheme.bodySmall?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      trailing: copyable
          ? InkWell(
              onTap: () {
                ClipboardUtils.copyText(context, value);
              },
              child: Icon(
                Icons.copy,
                size: 16,
                color: colorScheme.primary,
              ),
            )
          : null,
      onTap: copyable
          ? () {
              ClipboardUtils.copyText(context, value);
            }
          : null,
    );
  }
}
