import 'package:mobx/mobx.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/user/models/register.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/pages/base_store.dart';

part 'register_state.g.dart';

class RegisterState = _RegisterState with _$RegisterState;

enum RegisterEvent { registerSuccess, notAgree }

abstract class _RegisterState extends BaseStore with Store {
  UserRepo _repo = getIt.get();

  @observable
  String username = "";

  @observable
  String password = "";

  @observable
  String _rePassword = "";

  @observable
  bool agree = false;

  @observable
  RegisterEvent? event;

  @action
  void setUsername(String value) {
    username = value;
  }

  @action
  void setPassword(String value) {
    password = value;
  }

  @action
  void setRePassword(String value) {
    _rePassword = value;
  }

  @action
  void updateAgree(bool? v) {
    agree = v ?? false;
  }

  @action
  Future<void> register() async {
    // 数据校验
    if (username.isEmpty) {
      setError(S.current.registerUserNameTips);
      return;
    }
    if (password.isEmpty) {
      setError(S.current.registerPasswordTips);
      return;
    }
    if (_rePassword.isEmpty) {
      setError(S.current.registerRePasswordTips);
      return;
    }
    if (password != _rePassword) {
      setError(S.current.registerRepeatPasswordError);
      return;
    }
    if (!agree) {
      event = RegisterEvent.notAgree;
      return;
    }
    // 请求数据
    runWithLoading(() async {
      final result = await _repo.register(RegisterParams(
          password: password, rePassword: _rePassword, username: username));
      // 处理登录结果
      result.onSuccess((v) {
        runInAction(() {
          event = RegisterEvent.registerSuccess;
        });
      });
    });
  }

  @action
  void clearEvent() {
    event = null;
  }
}
