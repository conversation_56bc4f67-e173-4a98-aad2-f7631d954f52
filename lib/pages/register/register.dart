import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/pages/register/register_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/reg_constans.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/agree_privacy.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';
import 'package:tasks/widgets/textfield/password_textfield.dart';

class RegisterPage extends StatefulWidget {
  final String? wechatId;

  RegisterPage({super.key, this.wechatId});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  late RegisterState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = RegisterState();

    // 监听事件
    reaction((_) => state.event, (r) {
      state.clearEvent();
      // 需要同意服务协议
      if (r == RegisterEvent.notAgree) {
        showConfirm(context, content: l10n.registerConfirm, onConfirm: () {
          state.updateAgree(true);
          // 重新执行登录
          state.register();
        });
        return;
      }
      // 注册成功
      if (r == RegisterEvent.registerSuccess) {
        ToastUtils.success(context, l10n.registerSuccessTips);
        context.pop(true);
        return;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    ColorScheme colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surface,
        title: "",
      ),
      backgroundColor: colorScheme.surface,
      body: SafeArea(
        child: StateWarp(
          store: state,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.registerTips,
                    style: textTheme.bodyLarge,
                  ),
                  SizedBox(height: 32),
                  // 用户名输入框
                  ClearInputTextField(
                      hintText: l10n.registerUserNamePlaceHolder,
                      icon: Icons.person,
                      fillColor: colorScheme.surfaceContainer,
                      filterPattern: RegFilterConstant.regUsername,
                      onChange: state.setUsername),
                  SizedBox(height: 16),
                  // 密码输入框
                  PasswordTextField(
                      hintText: l10n.registerPasswordPlaceHolder,
                      fillColor: colorScheme.surfaceContainer,
                      onChange: state.setPassword),
                  SizedBox(height: 16),
                  // 确认密码输入框
                  PasswordTextField(
                      hintText: l10n.registerRePasswordTips,
                      fillColor: colorScheme.surfaceContainer,
                      onChange: state.setRePassword),
                  SizedBox(height: 16),
                  // 注册按钮
                  ElevatedButton(
                    onPressed: state.register,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      minimumSize: Size(double.infinity, 50),
                    ),
                    child: Text(
                      l10n.register,
                      style: textTheme.titleMedium!
                          .copyWith(color: colorScheme.onPrimary),
                    ),
                  ),
                  SizedBox(height: 16),
                  // 协议
                  Observer(builder: (context) {
                    return AgreePrivacy(
                      value: state.agree,
                      onChanged: state.updateAgree,
                    );
                  }),
                  // 返回登录链接
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        l10n.registerExistAccount,
                        style: textTheme.bodyMedium,
                      ),
                      TextButton(
                        onPressed: () {
                          // 返回登录逻辑
                          Navigator.pop(context);
                        },
                        child: Text(
                          l10n.registerGoLogin,
                          style: textTheme.bodyMedium!
                              .copyWith(color: colorScheme.primary),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
