import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/pages/userinfo/bind_email/bind_email_state.dart';
import 'package:tasks/providers/user_store.dart';
import 'package:tasks/utils/reg_constans.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';
import 'package:tasks/widgets/textfield/verify_code_textfield.dart';

class BindEmailPage extends StatefulWidget {
  final String? type;

  BindEmailPage({super.key, this.type});

  @override
  State<BindEmailPage> createState() => _BindEmailPageState();
}

class _BindEmailPageState extends State<BindEmailPage> {
  late BindEmailState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  UserStore userStore = getIt.get();

  @override
  void initState() {
    super.initState();
    state = BindEmailState(widget.type);

    // 监听事件
    reaction((_) => state.event, (r) {
      state.clearEvent();
      // 绑定邮箱成功
      if (r == BindEmailEvent.bindEmailSuccess) {
        userStore.syncFetchUserInfo();
        ToastUtils.success(context, "绑定邮箱成功");
        // 返回上一页
        context.pop();
        return;
      }
      // 绑定邮箱成功
      if (r == BindEmailEvent.unbindEmailSuccess) {
        userStore.syncFetchUserInfo();
        ToastUtils.success(context, "解绑邮箱成功");
        // 返回上一页
        context.pop();
        return;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;

    return StateWarp(
      store: state,
      child: Scaffold(
        appBar: CommonAppBar(),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Observer(builder: (context) {
                    return Visibility(
                      visible: !state.isUnBind,
                      child: Text(
                        "绑定邮箱用于登录、修改密码、重新密码",
                        style: textTheme.bodyMedium,
                      ),
                    );
                  }),
                  Observer(builder: (context) {
                    return Visibility(
                      visible: state.isUnBind,
                      child: Text(
                        "当前邮箱：${state.email}",
                        style: textTheme.labelMedium,
                      ),
                    );
                  }),
                  SizedBox(height: 16),
                  // 用户名输入框
                  Observer(builder: (context) {
                    return Visibility(
                      visible: !state.isUnBind,
                      child: Column(
                        children: [
                          ClearInputTextField(
                            hintText: "输入邮箱",
                            icon: Icons.email,
                            filterPattern: RegFilterConstant.regEmail,
                            textInputAction: TextInputAction.next,
                            onChange: state.onEmailChange,
                            fillColor: colorScheme.surfaceContainer,
                          ),
                          SizedBox(height: 16),
                        ],
                      ),
                    );
                  }),
                  // 密码输入框
                  Observer(builder: (context) {
                    return VerifyCodeTextField(
                      hintText: "输入验证码",
                      icon: Icons.verified,
                      onChange: state.onVerifyCodeChange,
                      onGetCode: state.getVerifyCode,
                      filterPattern: RegFilterConstant.inputVerifyCode,
                      fillColor: colorScheme.surfaceContainer,
                    );
                  }),
                  SizedBox(height: 32),
                  // 注册按钮
                  ElevatedButton(
                    onPressed: state.doConfirm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      minimumSize: Size(double.infinity, 50),
                    ),
                    child: Text(
                      l10n.confirm,
                      style: textTheme.titleMedium!
                          .copyWith(color: colorScheme.onPrimary),
                    ),
                  ),
                  SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
