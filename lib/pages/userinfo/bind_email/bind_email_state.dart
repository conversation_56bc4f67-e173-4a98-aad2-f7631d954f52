import 'package:mobx/mobx.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/user/email_verify_code_type.dart';
import 'package:tasks/pages/base_store.dart';

part 'bind_email_state.g.dart';

class BindEmailState = _BindEmailState with _$BindEmailState;

enum BindEmailEvent { bindEmailSuccess, unbindEmailSuccess }

abstract class _BindEmailState extends BaseStore with Store {
  UserRepo repo = getIt.get();

  @observable
  String? email;

  @observable
  String? verifyCode;

  @observable
  BindEmailEvent? event;

  @observable
  String? type;

  @computed
  bool get isUnBind => type == EmailVerifyCodeType.unbindEmail.name;

  _BindEmailState(String? type) {
    runInAction(() {
      this.type = type;
    });
    init();
  }

  @action
  void init() {
    if (isUnBind) {
      email = repo.getUserInfoFromCache()?.email ?? "";
    }
  }

  @action
  void onEmailChange(String value) {
    email = value;
  }

  @action
  void onVerifyCodeChange(String value) {
    verifyCode = value;
  }

  Future<bool> getVerifyCode() async {
    if (email == null || email!.isEmpty) {
      setError("请输入有效的邮箱");
      return Future.value(false);
    }
    final r = await runWithLoading(() async {
      final result = await repo.getEmailVerifyCode(
          email ?? "",
          isUnBind
              ? EmailVerifyCodeType.unbindEmail
              : EmailVerifyCodeType.bindEmail);
      return result.isSuccess == true;
    });
    return r == true;
  }

  @action
  void doConfirm() {
    if (email == null || email!.isEmpty) {
      setError("请输入有效的邮箱");
      return;
    }
    if (verifyCode == null) {
      setError("请输入有效的验证码");
      return;
    }
    if (isUnBind) {
      unBindEmail();
      return;
    }
    bindEmail();
  }

  @action
  void bindEmail() {
    runWithLoading(() async {
      final result = await repo.bindEmail(email ?? "", verifyCode ?? "");
      if (result.isSuccess == true) {
        runInAction(() {
          event = BindEmailEvent.bindEmailSuccess;
        });
      }
    });
  }

  @action
  void unBindEmail() {
    runWithLoading(() async {
      final result = await repo.unBindEmail(email ?? "", verifyCode ?? "");
      if (result.isSuccess == true) {
        runInAction(() {
          event = BindEmailEvent.unbindEmailSuccess;
        });
      }
    });
  }

  @action
  void clearEvent() {
    event = null;
  }
}
