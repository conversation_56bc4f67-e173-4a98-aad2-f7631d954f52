import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/modal/cdkey_input/cdkey_input_utils.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/models/user/email_verify_code_type.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/pages/userinfo/edit_user_info_state.dart';
import 'package:tasks/providers/wechat_state.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/utils/clipboard_utils.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

class EditUserInfo extends StatefulWidget {
  const EditUserInfo({super.key});

  @override
  State<EditUserInfo> createState() => _EditUserInfoState();
}

class _EditUserInfoState extends State<EditUserInfo> {
  late EditUserInfoState state;

  ColorScheme get cs => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  WechatState get wechatStore => getIt.get();

  @override
  void initState() {
    super.initState();
    state = EditUserInfoState();
    // 事件
    reaction((_) => wechatStore.event, (r) async {
      if (r == WechatEvent.bindWechatSuccess) {
        ToastUtils.success(context, "绑定成功");
        await state.sync();
        return;
      }
      if (r == WechatEvent.unbindWechatSuccess) {
        ToastUtils.success(context, "解绑成功");
        await state.sync();
        return;
      }
    });
    // 监听当前页面事件
    reaction((_) => state.event, (e) {
      if (e == EditUserInfoEvent.deleteAccountSuccess) {
        ToastUtils.success(context, "注销成功，15个工作日内完成账号删除！");
        context.pop();
        state.logout();
        return;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CommonAppBar(
          backgroundColor: cs.surface,
        ),
        body: StateWarp(
          store: state,
          child: StateWarp(
            store: wechatStore,
            child: ListView(
              children: [
                SectionWarp(title: "基本信息", children: [
                  // 用户名
                  Observer(builder: (context) {
                    final username = state.userInfo?.username ?? "";
                    return ListTile(
                      title: Text(l10n.ucenterUsername),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 120,
                            child: Text(
                              username,
                              style: textTheme.bodyMedium,
                              maxLines: 1,
                              textAlign: TextAlign.end,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          InkWell(
                            onTap: () =>
                                ClipboardUtils.copyText(context, username),
                            child: Icon(
                              size: 16,
                              Icons.copy,
                              color: cs.primary,
                            ),
                          )
                        ],
                      ),
                      onTap: () async {
                        final value = await showBottomInputDialog(
                          context,
                          title: l10n.ucenterUsername,
                          hintText: l10n.loginUserNameTips,
                          initialValue: state.userInfo?.username ?? "",
                          keyboardType: TextInputType.emailAddress,
                        );
                        if (value.isFailure) return;
                        state.editUserName(value.data ?? "");
                      },
                    );
                  }),
                  // 昵称
                  Observer(builder: (context) {
                    return ListTile(
                      title: const Text('昵称'),
                      trailing: Text(state.userInfo?.nickname ?? "未设定"),
                      onTap: () async {
                        final value = await showBottomInputDialog(
                          context,
                          title: "请输入昵称",
                          hintText: "请输入昵称",
                          initialValue: state.userInfo?.nickname ?? "",
                        );
                        if (value.isFailure) return;
                        state.editNickName(value.data ?? "");
                      },
                    );
                  }),
                  // 邀请码
                  Observer(builder: (context) {
                    final text = state.userInfo?.inviteCode ?? "";
                    return ListTile(
                      title: const Text('邀请码'),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            text,
                            style: textTheme.bodyMedium,
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          Icon(
                            size: 16,
                            Icons.copy,
                            color: cs.primary,
                          )
                        ],
                      ),
                      onTap: () {
                        ClipboardUtils.copyText(context, text);
                      },
                    );
                  })
                ]),
                SectionWarp(title: "其他登录", children: [
                  Observer(builder: (context) {
                    return ListTile(
                      title: const Text('微信'),
                      trailing: Text(
                        state.isWechatBind ? "已绑定" : "未绑定",
                        style: TextStyle(
                            color:
                                state.isWechatBind ? cs.primary : cs.onSurface),
                      ),
                      onTap: () {
                        if (state.isWechatBind) {
                          // 走解绑流程
                          showConfirm(context, content: "您已绑定微信，是否继续解绑流程？",
                              onConfirm: () {
                            wechatStore.unbindWechat();
                          });
                          return;
                        }
                        wechatStore.bindWechat();
                      },
                    );
                  }),
                  Observer(builder: (context) {
                    return ListTile(
                      title: const Text('邮箱'),
                      trailing: Text(
                        state.isEmailBind ? (state.email ?? "") : "未绑定",
                        style: TextStyle(
                            color:
                                state.isEmailBind ? cs.primary : cs.onSurface),
                      ),
                      onTap: () {
                        if (state.isEmailBind) {
                          // 走解绑流程
                          showConfirm(context, content: "您已绑定邮箱，是否继续解绑流程？",
                              onConfirm: () {
                            context.push(
                                "${Routers.bindEmail}?type=${EmailVerifyCodeType.unbindEmail.name}");
                          });
                          return;
                        }
                        context.push(Routers.bindEmail);
                      },
                    );
                  }),
                ]),
                SizedBox(
                  height: 24,
                ),
                SectionWarp(title: "", children: [
                  ListTile(
                    title: const Text('注销账号'),
                    trailing: Icon(Icons.chevron_right),
                    onTap: () {
                      showConfirm(context,
                          content: "注销账号后，您的账号数据将在15个工作日内被清空，且无法找回，确定注销？",
                          onConfirm: () {
                        state.deleteAccount();
                      });
                    },
                  )
                ])
              ],
            ),
          ),
        ));
  }
}
