import 'package:mobx/mobx.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/user/models/resp/account_provider.dart';
import 'package:tasks/data/user/models/user_model.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/user_store.dart';
import 'package:tasks/utils/string_ext.dart';
import 'package:tasks/utils/toast_utils.dart';

part 'edit_user_info_state.g.dart';

class EditUserInfoState = _EditUserInfoState with _$EditUserInfoState;

enum EditUserInfoEvent { deleteAccountSuccess }

abstract class _EditUserInfoState extends BaseStore with Store {
  UserStore userStore = getIt.get();

  UserRepo repo = getIt.get();

  @observable
  EditUserInfoEvent? event;

  @computed
  UserModel? get userInfo => userStore.userModel;

  @observable
  ObservableList<OAuthProviderEntity> thirdList = ObservableList();

  @computed
  bool get isWechatBind =>
      userInfo?.wechatBind == true ||
      thirdList.where((i) => i.provider == OAuthProvider.Wechat).firstOrNull !=
          null;

  @computed
  bool get isEmailBind => (userInfo?.email.isNullOrEmpty ?? false) != true;

  @computed
  String? get email => userInfo?.email;

  @action
  void logout() {
    userStore.logout();
  }

  _EditUserInfoState() {
    sync();
  }

  @action
  Future<void> sync() async {
    // 获取用户信息
    await userStore.syncFetchUserInfo();
    // 获取三方绑定信息
    final result = await repo.getThirdList();
    if (result.isSuccess) {
      runInAction(() {
        thirdList.clear();
        thirdList.addAll(result.data ?? []);
      });
    }
  }

  @action
  Future<void> editNickName(String name) async {
    runWithLoading(() async {
      final result = await repo.editNickName(name);
      result.onSuccess((r) {
        ToastUtils.success(null, "修改成功");
        userStore.syncFetchUserInfo();
      });
    });
  }

  @action
  Future<void> editUserName(String name) async {
    if (name.isEmpty) {
      return;
    }
    runWithLoading(() async {
      final result = await repo.editUsername(name);
      if (result.isSuccess) {
        ToastUtils.success(null, "修改成功");
        userStore.syncFetchUserInfo();
        return;
      }
    });
  }

  @action
  Future<void> deleteAccount() async {
    runWithLoading(() async {
      final result = await repo.logoutAccount();
      result.onSuccess((r) {
        setEvent(EditUserInfoEvent.deleteAccountSuccess);
      });
    });
  }

  @action
  void setEvent(EditUserInfoEvent event) {
    this.event = event;
  }

  @action
  void clearEvent() {
    event = null;
  }
}
