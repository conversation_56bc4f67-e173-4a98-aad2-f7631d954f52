import 'package:mobx/mobx.dart';
import 'package:tasks/data/message/message_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/message/message_info.dart';
import 'package:tasks/pages/base_store.dart';

part 'message_state.g.dart';

class MessageState = _MessageState with _$MessageState;

abstract class _MessageState extends BaseStore with Store {
  MessageRepo repo = getIt.get();

  @observable
  List<MessageInfo> messages = [];

  _MessageState() {
    getMessageList();
  }

  @action
  Future<void> getMessageList() async {
    final result = await repo.getMessageList();
    messages = result.data ?? List.empty();
  }
}
