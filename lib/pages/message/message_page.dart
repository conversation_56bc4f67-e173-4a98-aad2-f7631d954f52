import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/pages/message/message_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/string_ext.dart';
import 'package:tasks/utils/url_utils.dart';
import 'package:tasks/widgets/app_logo.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/empty_state_widget.dart';

class MessagePage extends StatefulWidget {
  const MessagePage({super.key});

  @override
  State<MessagePage> createState() => _MessagePageState();
}

class _MessagePageState extends State<MessagePage> {
  late MessageState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = MessageState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surface,
      ),
      body: StateWarp(
          store: state,
          child: Stack(
            children: [
              // 数据为空时展示
              Observer(builder: (context) {
                return Visibility(
                    visible: state.messages.isEmpty,
                    child: Center(
                      child: EmptyStateWidget(
                        title: l10n.commonEmptyContent,
                        subtitle: null,
                        backgroundColor: colorScheme.surface,
                        icon: Icons.hourglass_empty,
                      ),
                    ));
              }),
              Observer(builder: (context) {
                return ListView.separated(
                  itemBuilder: (context, index) {
                    final item = state.messages[index];
                    return ListTile(
                      leading: AppLogo(
                        avatarUrl: item.imageUrl,
                      ),
                      title: Text(
                        item.title ?? "",
                        style: textTheme.titleMedium,
                      ),
                      subtitle: Visibility(
                          visible: !item.content.isNullOrEmpty,
                          child: Text(
                            "${item.createTime}\n${item.content ?? ""}",
                            style: textTheme.bodySmall,
                          )),
                      onTap: () {
                        if (item.actionType == "INNER") {
                          // 打开应用
                          if (!item.action.isNullOrEmpty) {
                            context.push(item.action!);
                          }
                        } else if (item.actionType == "OUT_URL") {
                          // 打开网址
                          UrlUtils.openUrlOut(item.action);
                        }
                      },
                      trailing: Visibility(
                          visible: !item.actionType.isNullOrEmpty,
                          child: Icon(Icons.chevron_right)),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return Divider();
                  },
                  itemCount: state.messages.length,
                );
              })
            ],
          )),
    );
  }
}
