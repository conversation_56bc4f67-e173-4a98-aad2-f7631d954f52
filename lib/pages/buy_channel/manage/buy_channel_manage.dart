import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/models/buy_channel.dart';
import 'package:tasks/pages/buy_channel/manage/buy_channel_manage_state.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/widgets/common_app_bar.dart';

class BuyChannelManagePage extends StatefulWidget {
  @override
  _BuyChannelManagePageState createState() => _BuyChannelManagePageState();
}

class _BuyChannelManagePageState extends State<BuyChannelManagePage> {
  late BuyChannelManageState state;

  S get l10n => S.of(context);

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    super.initState();
    state = BuyChannelManageState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surfaceContainer,
        title: l10n.assetBuyChannelManage,
        actions: [
          TextButton(
            child: Text(
              l10n.commonAdd,
              style:
                  textTheme.titleMedium!.copyWith(color: colorScheme.primary),
            ),
            onPressed: () {
              context.push(Routers.buyChannelEdit);
            },
          ),
        ],
      ),
      body: Observer(builder: (context) {
        return ReorderableListView.builder(
          itemCount: state.buyChannels.length,
          onReorder: (oldIndex, newIndex) {
            state.reorderBuyChannels(oldIndex, newIndex);
          },
          itemBuilder: (context, index) {
            final item = state.buyChannels[index];
            return _buildChannelItem(item, index);
          },
        );
      }),
    );
  }

  Widget _buildChannelItem(BuyChannelEntity item, int index) {
    return Column(
      key: Key(item.id.toString()),
      children: [
        Container(
          color: colorScheme.surface,
          child: ListTile(
            leading: InkWell(
                onTap: () {
                  showConfirm(
                    context,
                    title: l10n.delete,
                    content: l10n.assetBuyChannelDeleteConfirm,
                    onConfirm: () {
                      state.deleteBuyChannel(item.id);
                    },
                  );
                },
                child: Icon(Icons.remove_circle, color: colorScheme.error)),
            title: Padding(
              padding: EdgeInsets.only(left: 8),
              child: Text(item.name),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [Icon(Icons.drag_indicator)],
            ),
            onTap: () {
              context.push("${Routers.buyChannelEdit}?channelId=${item.id}");
            },
          ),
        ),
        if (index < state.buyChannels.length - 1)
          Divider(
            height: 1,
            thickness: 1,
            color: colorScheme.outlineVariant,
            indent: 16,
            endIndent: 16,
          ),
      ],
    );
  }
}
