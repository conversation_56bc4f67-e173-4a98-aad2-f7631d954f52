import 'package:mobx/mobx.dart';
import 'package:tasks/data/buy_channel/buy_channel_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/buy_channel.dart';
import 'package:tasks/providers/buy_channel_store.dart';

part 'buy_channel_manage_state.g.dart';

class BuyChannelManageState = _BuyChannelManageState with _$BuyChannelManageState;

abstract class _BuyChannelManageState with Store {
  final BuyChannelLocalDataSource _dataSource = getIt.get();

  BuyChannelStore buyChannelStore = getIt.get();

  @computed
  ObservableList<BuyChannelEntity> get buyChannels => buyChannelStore.buyChannels;

  @action
  Future<void> reorderBuyChannels(int oldIndex, int newIndex) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    // 在内存中直接更新顺序
    final BuyChannelEntity item = buyChannels.removeAt(oldIndex);
    buyChannels.insert(newIndex, item);

    // 更新所有项的order
    for (int i = 0; i < buyChannels.length; i++) {
      buyChannels[i].order = i;
    }

    // 异步保存到本地
    await _dataSource.saveBuyChannels(buyChannels);
    buyChannelStore.fetchBuyChannels();
  }

  @action
  Future<void> deleteBuyChannel(String id) async {
    await _dataSource.deleteBuyChannel(id);
    buyChannelStore.fetchBuyChannels();
  }
}
