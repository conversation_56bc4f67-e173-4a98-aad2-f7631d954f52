import 'package:mobx/mobx.dart';
import 'package:tasks/data/buy_channel/buy_channel_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/buy_channel_store.dart';
import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';

part 'edit_buy_channel_state.g.dart';

enum EditBuyChannelEvent {
  addSuccess,
  editSuccess,
}

class EditBuyChannelState = _EditBuyChannelState with _$EditBuyChannelState;

abstract class _EditBuyChannelState extends BaseStore with Store {
  BuyChannelLocalDataSource _dataSource = getIt.get();

  BuyChannelStore buyChannelStore = getIt.get();

  BuildContext? _context;

  @observable
  String? id;

  @observable
  String name = "";

  @computed
  bool get isEditMode => id != null;

  @computed
  String get title {
    if (_context == null) return isEditMode ? "编辑渠道" : "添加渠道";
    final l10n = S.of(_context!);
    return isEditMode ? l10n.assetBuyChannelEdit : l10n.assetBuyChannelAdd;
  }

  @computed
  String get actionText {
    if (_context == null) return isEditMode ? "保存" : "添加";
    final l10n = S.of(_context!);
    return isEditMode ? l10n.commonSave : l10n.commonAdd;
  }

  @observable
  EditBuyChannelEvent? event;

  _EditBuyChannelState(String? channelId) {
    init(channelId);
  }

  void setContext(BuildContext context) {
    _context = context;
  }

  @action
  void init(String? channelId) {
    if (channelId != null) {
      id = channelId;
      final channel = _dataSource.getBuyChannel(channelId);
      if (channel != null) {
        name = channel.name;
      }
    }
  }

  @action
  void onNameChanged(String value) {
    name = value;
  }

  @action
  @override
  void clearError() {
    super.clearError();
  }

  @action
  Future<void> confirm() async {
    if (name.isEmpty) {
      final errorMsg = _context != null
          ? S.of(_context!).assetBuyChannelNameRequired
          : "渠道名称不能为空";
      setError(errorMsg);
      return;
    }
    // 添加
    if (!isEditMode) {
      await _dataSource.addBuyChannel(name);
      buyChannelStore.fetchBuyChannels();
      event = EditBuyChannelEvent.addSuccess;
      return;
    }
    // 编辑
    await _dataSource.updateBuyChannel(id!, name);
    buyChannelStore.fetchBuyChannels();
    event = EditBuyChannelEvent.editSuccess;
  }
}
