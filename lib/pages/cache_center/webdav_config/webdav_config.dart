import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/pages/cache_center/webdav_config/webdav_config_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';
import 'package:tasks/widgets/textfield/password_textfield.dart';

class WebdavConfigPage extends StatefulWidget {
  const WebdavConfigPage({Key? key}) : super(key: key);

  @override
  State<WebdavConfigPage> createState() => _WebdavConfigPageState();
}

class _WebdavConfigPageState extends State<WebdavConfigPage> {
  final state = WebdavConfigState();

  S get l10n => S.of(context);

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  @override
  void initState() {
    super.initState();
    // 监听事件
    reaction((_) => state.event, (event) {
      if (event == null) return;

      switch (event) {
        case WebdavConfigEvent.saveSuccess:
          ToastUtils.success(context, l10n.commonSave);
          break;
        case WebdavConfigEvent.testSuccess:
          ToastUtils.success(context, l10n.settingWebdavTestSuccess);
          break;
        case WebdavConfigEvent.testFailed:
          // 错误信息已在state中设置，会通过errorBanner显示
          break;
      }
      state.clearEvent();
    });

    // 监听错误消息并进行本地化
    reaction((_) => state.errorMessage, (errorMessage) {
      if (errorMessage == null) return;

      String localizedMessage = _getLocalizedErrorMessage(errorMessage);
      if (localizedMessage != errorMessage) {
        // 如果消息被本地化了，更新state中的错误消息
        state.setError(localizedMessage);
      }
    });
  }

  String _getLocalizedErrorMessage(String errorCode) {
    switch (errorCode) {
      case "WEBDAV_SERVER_URL_EMPTY":
        return l10n.settingWebdavServerUrl;
      case "WEBDAV_USERNAME_EMPTY":
        return l10n.settingWebdavUsername;
      case "WEBDAV_PASSWORD_EMPTY":
        return l10n.settingWebdavPassword;
      case "WEBDAV_SAVE_FAILED":
        return l10n.settingWebdavSaveFailed;
      case "WEBDAV_CONFIG_INCOMPLETE":
        return l10n.settingWebdavConfigIncomplete;
      case "WEBDAV_TEST_FAILED":
        return l10n.settingWebdavTestFailed;
      default:
        return errorCode; // 返回原始错误消息
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: l10n.settingWebdavConfig,
      ),
      body: StateWarp(
        store: state,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoCard(),
                SizedBox(height: 24),
                _buildConfigForm(),
                SizedBox(height: 24),
                _buildButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: colorScheme.surfaceContainer,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "WebDAV 同步",
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              "配置 WebDAV 服务可以将您的数据备份到自己的云存储中，支持 NextCloud、Seafile 等服务。",
              style: textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigForm() {
    return Observer(
      builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.settingWebdavServerConfig,
              style: textTheme.titleMedium,
            ),
            SizedBox(height: 16),
            ClearInputTextField(
              hintText: l10n.settingWebdavServerUrlHint,
              icon: Icons.link,
              value: state.serverUrl,
              onChange: state.onServerUrlChanged,
              fillColor: colorScheme.surfaceContainer,
            ),
            SizedBox(height: 16),
            ClearInputTextField(
              hintText: l10n.settingWebdavUsernameHint,
              icon: Icons.person,
              value: state.username,
              onChange: state.onUsernameChanged,
              fillColor: colorScheme.surfaceContainer,
            ),
            SizedBox(height: 16),
            PasswordTextField(
              hintText: l10n.settingWebdavPasswordHint,
              value: state.password,
              onChange: state.onPasswordChanged,
              fillColor: colorScheme.surfaceContainer,
            ),
            SizedBox(height: 16),
            ClearInputTextField(
              hintText: l10n.settingWebdavDirectoryHint,
              icon: Icons.folder,
              value: state.directory,
              onChange: state.onDirectoryChanged,
              fillColor: colorScheme.surfaceContainer,
            ),
          ],
        );
      },
    );
  }

  Widget _buildButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: state.testConnection,
            icon: Icon(Icons.check_circle),
            label: Text(l10n.settingWebdavTestConnection),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.primaryContainer,
              foregroundColor: colorScheme.onPrimaryContainer,
            ),
          ),
        ),
        SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: state.saveConfig,
            icon: Icon(Icons.save),
            label: Text(l10n.settingWebdavSaveConfig),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.primary,
              foregroundColor: colorScheme.onPrimary,
            ),
          ),
        ),
      ],
    );
  }
}
