import 'package:mobx/mobx.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/pages/base_store.dart';

part 'webdav_config_state.g.dart';

class WebdavConfigState = _WebdavConfigState with _$WebdavConfigState;

enum WebdavConfigEvent { saveSuccess, testSuccess, testFailed }

abstract class _WebdavConfigState extends BaseStore with Store {
  final UserRepo repo = getIt.get();

  @observable
  String serverUrl = "";

  @observable
  String username = "";

  @observable
  String password = "";

  @observable
  String directory = "recollect";

  @observable
  WebdavConfigEvent? event;

  _WebdavConfigState() {
    loadConfig();
  }

  @action
  Future<void> loadConfig() async {
    runWithLoading(() async {
      final config = await repo.getWebdavConfig();
      if (config != null) {
        serverUrl = config.serverUrl ?? "";
        username = config.username ?? "";
        password = config.password ?? "";
        directory = config.directory ?? "recollect";
      }
    });
  }

  @action
  void onServerUrlChanged(String value) {
    serverUrl = value;
  }

  @action
  void onUsernameChanged(String value) {
    username = value;
  }

  @action
  void onPasswordChanged(String value) {
    password = value;
  }

  @action
  void onDirectoryChanged(String value) {
    directory = value;
  }

  @action
  void clearEvent() {
    event = null;
  }

  @action
  Future<void> saveConfig() async {
    if (serverUrl.isEmpty) {
      setError("WEBDAV_SERVER_URL_EMPTY");
      return;
    }

    if (username.isEmpty) {
      setError("WEBDAV_USERNAME_EMPTY");
      return;
    }

    if (password.isEmpty) {
      setError("WEBDAV_PASSWORD_EMPTY");
      return;
    }

    runWithLoading(() async {
      // 保存WebDAV配置
      final result = await repo.saveWebdavConfig(
        serverUrl: serverUrl,
        username: username,
        password: password,
        directory: directory,
      );

      if (result.isSuccess) {
        event = WebdavConfigEvent.saveSuccess;
      } else {
        setError(result.msg ?? "WEBDAV_SAVE_FAILED");
      }
    });
  }

  @action
  Future<void> testConnection() async {
    if (serverUrl.isEmpty || username.isEmpty || password.isEmpty) {
      setError("WEBDAV_CONFIG_INCOMPLETE");
      return;
    }

    runWithLoading(() async {
      // 测试WebDAV连接
      final result = await repo.testWebdavConnection(
        serverUrl: serverUrl,
        username: username,
        password: password,
      );

      if (result.isSuccess) {
        event = WebdavConfigEvent.testSuccess;
      } else {
        event = WebdavConfigEvent.testFailed;
        setError(result.msg ?? "WEBDAV_TEST_FAILED");
      }
    });
  }
}
