import 'package:mobx/mobx.dart';
import 'package:tasks/data/asset/asset_local_data_source.dart';
import 'package:tasks/data/cache/cache_repo.dart';
import 'package:tasks/data/data_sync_repo.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/cache/cache_history_resp.dart';
import 'package:tasks/pages/base_store.dart';

part 'cache_center_state.g.dart';

enum CacheCenterEvent {
  cacheSuccess,
  revertSuccess,
  webdavSuccess,
  webdavFailed
}

class CacheCenterState = _CacheCenterState with _$CacheCenterState;

abstract class _CacheCenterState extends BaseStore with Store {
  final repo = getIt.get<DataSyncRepo>();
  final userRepo = getIt.get<UserRepo>();
  final CacheRepo cacheRepo = getIt.get();
  final AssetLocalDataSource _datasource = getIt.get();

  _CacheCenterState() {
    getCacheList();
  }

  @observable
  List<CacheHistoryEntity> cacheHistory = List.empty();

  @observable
  CacheCenterEvent? event;

  @action
  Future<void> getCacheList() async {
    runWithLoading(() async {
      cacheHistory = (await repo.getCacheList()).data ?? List.empty();
    });
  }

  @action
  Future<void> saveCache() async {
    runWithLoading(() async {
      final result = await repo.saveCache();
      if (result.isSuccess) {
        _datasource.saveCacheAssetsTips();
        runInAction(() {
          event = CacheCenterEvent.cacheSuccess;
        });
        getCacheList();
        return;
      }
    });
  }

  @action
  Future<void> recoverCache(String id) async {
    runWithLoading(() async {
      final result = await repo.fetchCache(id);
      if (result.isSuccess) {
        event = CacheCenterEvent.revertSuccess;
        return;
      }
      setError("恢复数据失败:${result.msg ?? ""}");
    });
  }

  @action
  Future<void> deleteCache(String id) async {
    runWithLoading(() async {
      final result = await repo.deleteCache(id);
      if (result.isSuccess) {
        getCacheList();
        return;
      }
      setError("删除失败");
    });
  }

  @action
  void clearEvent() {
    event = null;
  }

  @action
  Future<void> backupToWebdav() async {
    runWithLoading(() async {
      try {
        // 获取WebDAV配置
        final webdavConfig = await userRepo.getWebdavConfig();

        if (webdavConfig == null ||
            webdavConfig.serverUrl == null ||
            webdavConfig.serverUrl!.isEmpty) {
          setError("请先配置WebDAV服务");
          return;
        }

        // 执行备份到WebDAV的逻辑
        final result = await cacheRepo.backupToWebdav(webdavConfig);

        if (result.isSuccess) {
          saveWebdavLog(result?.data);
          event = CacheCenterEvent.webdavSuccess;
        } else {
          event = CacheCenterEvent.webdavFailed;
          print('备份到WebDAV失败: $result');
          setError(result.msg ?? "备份到WebDAV失败");
        }
      } catch (e) {
        event = CacheCenterEvent.webdavFailed;
        print('备份到WebDAV失败: $e');
        setError("备份到WebDAV失败: $e");
      }
    });
  }

  // 记录WebDAV备份
  @action
  Future<void> saveWebdavLog(String? filename) async {
    if (filename == null) return;
    await repo.saveWebdavCache(filename);
    // 刷新缓存列表
    getCacheList();
  }

  // webdav备份回滚
  @action
  Future<void> recoverWebdav(String fileId) async {
    runWithLoading(() async {
      final result = await cacheRepo.recoverWebdav(fileId);
      if (result.isSuccess) {
        event = CacheCenterEvent.revertSuccess;
        return;
      }
      setError("恢复数据失败:${result.msg ?? ""}");
    });
  }
}
