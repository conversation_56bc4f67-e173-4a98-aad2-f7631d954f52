import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/models/cache/cache_history_resp.dart';
import 'package:tasks/pages/cache_center/cache_center_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/providers/user_store.dart';
import 'package:tasks/providers/asset_store.dart';
import 'package:tasks/providers/category_store.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';

class CacheCenter extends StatefulWidget {
  const CacheCenter({super.key});

  @override
  State<CacheCenter> createState() => _CacheCenterState();
}

class _CacheCenterState extends State<CacheCenter> {
  late CacheCenterState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  UserStore userStore = getIt.get();

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = CacheCenterState();

    reaction((_) => state.event, (result) {
      state.clearEvent();
      switch (result) {
        case CacheCenterEvent.cacheSuccess:
          ToastUtils.success(context, "缓存成功");
          break;
        case CacheCenterEvent.revertSuccess:
          getIt.get<AssetStore>().fetchAssets();
          getIt.get<CategoryStore>().fetchCategories();
          ToastUtils.success(context, "覆盖数据成功");
          break;
        case CacheCenterEvent.webdavSuccess:
          ToastUtils.success(context, "WebDAV备份成功");
          break;
        case CacheCenterEvent.webdavFailed:
          // 错误信息已在state中设置，会通过errorBanner显示
          break;
        default:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        actions: [
          InkWell(
              onTap: () {
                context.push(Routers.webdavConfig);
              },
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                child: Text("webdav配置"),
              ))
        ],
      ),
      body: StateWarp(
        store: state,
        child: Observer(builder: (context) {
          return ListView(
            children: [
              SizedBox(height: 8),
              ...state.cacheHistory.map(_buildItem).toList(),
              SizedBox(height: 16),
              _buildBackupButtons(),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildItem(CacheHistoryEntity entity) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: colorScheme.surfaceContainer),
        child: ListTile(
          title: Text(
            entity.type == "Webdav"
                ? l10n.settingCacheWebdavBackup
                : l10n.settingCacheCloudBackup,
            style: textTheme.titleMedium,
          ),
          subtitle: Text(
            entity.createdAt ?? "",
            style: textTheme.bodySmall,
          ),
          onTap: () {
            showConfirm(context, content: l10n.settingCacheRestoreConfirm,
                onConfirm: () {
              // 云端备份
              if (entity.id != null && entity.type != "Webdav") {
                state.recoverCache(entity.id!);
                return;
              }
              // webdav备份
              if (entity.fileId == null) return;
              state.recoverWebdav(entity.fileId!);
            });
          },
          trailing: IconButton(
              onPressed: () {
                showConfirm(context, content: l10n.settingCacheDeleteConfirm,
                    onConfirm: () {
                  if (entity.id != null) {
                    state.deleteCache(entity.id!);
                  }
                });
              },
              icon: Icon(
                Icons.delete,
                color: colorScheme.error,
              )),
        ));
  }

  Widget _buildBackupButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ElevatedButton.icon(
            onPressed: () {
              showConfirm(context,
                  content: l10n.settingCacheBackupToServerConfirm,
                  onConfirm: () {
                state.saveCache();
              });
            },
            icon: Icon(Icons.cloud_upload),
            label: Text(l10n.settingCacheBackupToServer),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.primaryContainer,
              foregroundColor: colorScheme.onPrimaryContainer,
              padding: EdgeInsets.symmetric(vertical: 12),
            ),
          ),
          SizedBox(height: 12),
          ElevatedButton.icon(
            onPressed: () {
              showConfirm(context,
                  content: l10n.settingCacheBackupToWebdavConfirm,
                  onConfirm: () {
                state.backupToWebdav();
              });
            },
            icon: Icon(Icons.folder_shared),
            label: Text(l10n.settingCacheBackupToWebdav),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.secondaryContainer,
              foregroundColor: colorScheme.onSecondaryContainer,
              padding: EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ],
      ),
    );
  }
}
