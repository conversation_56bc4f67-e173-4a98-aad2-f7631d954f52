import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/pages/main/home/<USER>';
import 'package:tasks/pages/search/search_state.dart';
import 'package:tasks/widgets/appbar/search_appbar.dart';
import 'package:tasks/widgets/empty_state_widget.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  SearchState state = SearchState();
  final FocusNode _searchFocusNode = FocusNode();

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state.loadSearchHistory();
  }

  @override
  void dispose() {
    state.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(kToolbarHeight),
          child: Observer(builder: (context) {
            return SearchAppbar(
              hintText: state.hintText,
              searchText: state.searchText,
              onChange: state.updateSearchText,
              onSubmitted: (v) {
                _performSearch(v);
              },
              focusNode: _searchFocusNode,
            );
          })),
      body: Container(
        color: colorScheme.surfaceContainer,
        child: Observer(builder: (context) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          // 显示搜索历史
          if (state.showSearchHistory) {
            return _buildSearchHistoryView();
          }

          // 显示搜索结果
          return _buildSearchResultView();
        }),
      ),
    );
  }

  void _performSearch(String searchText) {
    if (searchText.trim().isEmpty) return;

    // 执行搜索并显示结果
    state.performSearch(searchText.trim());

    // 取消输入框焦点
    _searchFocusNode.unfocus();
  }

  Widget _buildSearchHistoryView() {
    if (state.searchHistory.isEmpty) {
      return EmptyStateWidget(
        title: l10n.searchHistoryEmpty,
        subtitle: l10n.searchHistoryEmptyDesc,
        backgroundColor: colorScheme.surfaceContainer,
        icon: Icons.history,
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            l10n.searchHistoryTitle,
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: state.searchHistory.length,
            itemBuilder: (context, index) {
              final historyItem = state.searchHistory[index];
              return ListTile(
                leading: Icon(
                  Icons.history,
                  color: colorScheme.onSurfaceVariant,
                ),
                title: Text(
                  historyItem,
                  style: textTheme.bodyLarge,
                ),
                trailing: IconButton(
                  icon: Icon(
                    Icons.close,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  onPressed: () {
                    state.removeSearchHistory(historyItem);
                  },
                ),
                onTap: () {
                  _performSearch(historyItem);
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResultView() {
    if (state.searchResults.isEmpty && state.searchText.isNotEmpty) {
      return EmptyStateWidget(
        title: l10n.homeEmptyListTitle,
        subtitle: "",
        backgroundColor: colorScheme.surfaceContainer,
        icon: Icons.search_off,
      );
    }

    return Padding(
      padding: const EdgeInsets.only(left: 6.0, right: 6),
      child: RecordAssetList(
        assets: state.searchResults,
        onFavorite: (v) {},
        switchInService: (v) {},
        enableSwap: false,
      ),
    );
  }
}
