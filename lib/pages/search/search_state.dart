import 'package:mobx/mobx.dart';
import 'package:tasks/data/asset/search_assets_usecase.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/providers/user_preferences_store.dart';
import 'package:tasks/utils/keyboard_utils.dart';

part 'search_state.g.dart';

class SearchState = _SearchState with _$SearchState;

abstract class _SearchState with Store {
  final _userPreferencesStore = getIt.get<UserPreferencesStore>();
  final _searchUseCase = getIt.get<SearchAssetsUseCase>();

  @observable
  String searchText = '';

  @observable
  List<String> searchHistory = [];

  @observable
  ObservableList<AssetCardEntity> searchResults =
      ObservableList<AssetCardEntity>();

  @observable
  bool isLoading = false;

  @observable
  bool showSearchHistory = true;

  @computed
  String get latestSearchText {
    return searchHistory.isNotEmpty ? searchHistory.first : '';
  }

  @computed
  String get hintText => S.current.homeSearchAssetHint;

  @action
  void updateSearchText(String text) {
    searchText = text;
    showSearchHistory = true;
  }

  @action
  Future<void> performSearch(String text) async {
    this.searchText = text;
    if (text.trim().isEmpty) {
      searchResults.clear();
      showSearchHistory = true;
      return;
    }

    showSearchHistory = false;
    isLoading = true;

    try {
      // 添加到搜索历史
      await _userPreferencesStore.addSearchHistory(text.trim());
      await loadSearchHistory();

      // 执行搜索
      final results = await _searchUseCase.search(text.trim());
      searchResults.clear();
      searchResults.addAll(results);
    } catch (e) {
      print('搜索失败: $e');
      searchResults.clear();
    } finally {
      isLoading = false;
      systemHideKeyboard();
    }
  }

  @action
  Future<void> loadSearchHistory() async {
    isLoading = true;
    try {
      searchHistory = await _userPreferencesStore.getSearchHistory();
    } catch (e) {
      print('加载搜索历史失败: $e');
    } finally {
      isLoading = false;
    }
  }

  @action
  Future<void> addSearchHistory(String text) async {
    if (text.trim().isEmpty) return;

    try {
      await _userPreferencesStore.addSearchHistory(text.trim());
      await loadSearchHistory(); // 重新加载历史记录
    } catch (e) {
      print('添加搜索历史失败: $e');
    }
  }

  @action
  Future<void> removeSearchHistory(String text) async {
    try {
      await _userPreferencesStore.removeSearchHistory(text);
      await loadSearchHistory(); // 重新加载历史记录
    } catch (e) {
      print('删除搜索历史失败: $e');
    }
  }

  @action
  Future<void> clearSearchHistory() async {
    try {
      await _userPreferencesStore.clearSearchHistory();
      searchHistory = [];
    } catch (e) {
      print('清空搜索历史失败: $e');
    }
  }

  void dispose() {
    _searchUseCase.dispose();
  }
}
