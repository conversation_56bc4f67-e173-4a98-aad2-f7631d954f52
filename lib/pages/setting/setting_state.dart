import 'package:mobx/mobx.dart';
import 'package:tasks/common/global/GlobalInfo.dart';
import 'package:tasks/data/user_preferences/user_preferences_lds.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/data/user/models/user_model.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/app_update_info.dart';
import 'package:tasks/models/user_preferences/user_preferences_entity.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/user_store.dart';
import 'package:tasks/providers/user_preferences_store.dart';

part 'setting_state.g.dart';

class SettingState = _SettingState with _$SettingState;

enum SettingEvent { noUpdate, hasUpdate }

abstract class _SettingState extends BaseStore with Store {
  UserStore userStore = getIt.get();
  UserRepo repo = getIt.get();
  UserPreferencesStore userPreferencesStore = getIt.get();

  @observable
  String appName = GlobalInfo.appName;

  @observable
  String version = GlobalInfo.appVersion;

  @observable
  AppUpdateInfo? info;

  @observable
  SettingEvent? event;

  @computed
  UserPreferencesEntity get entity => userPreferencesStore.preferencesEntity;

  @computed
  String get displayMoneySymbol {
    return '${entity.currencyCode} (${entity.currencySymbol})';
  }

  @computed
  bool get hasUpdate {
    return info != null;
  }

  @computed
  bool get isLogin => userStore.isLogin;

  @computed
  UserModel? get userModel => userStore.userModel;

  _SettingState() {
    init();
  }

  @action
  Future<void> init() async {
    // 用户偏好设置现在通过computed属性自动获取
  }

  @action
  Future<void> sync() async {
    runInAction(() async {
      final result = await repo.checkUpdate(null);
      if (result.data == null) {
        event = SettingEvent.noUpdate;
        return;
      }
      event = SettingEvent.hasUpdate;
      info = result.data;
    });
  }

  @action
  void clearEvent() {
    event = null;
  }

  @action
  void logout() {
    userStore.logout();
  }
}
