import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:provider/provider.dart';
import 'package:tasks/common/global/GlobalInfo.dart';
import 'package:tasks/data/config/config_repo.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/modal/color_picker/color_picker.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/modal/version_update/UpdateDialog.dart';
import 'package:tasks/pages/setting/setting_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/providers/theme_provider.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/utils/clipboard_utils.dart';
import 'package:tasks/utils/platform.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/utils/url_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  late SettingState state;

  @override
  void initState() {
    super.initState();
    state = SettingState();

    // 事件
    reaction((_) => state.event, (e) {
      if (e == null) {
        return;
      }
      state.clearEvent();
      switch (e) {
        case SettingEvent.noUpdate:
          ToastUtils.success(context, l10n.settingNoUpdate);
          break;
        case SettingEvent.hasUpdate:
          showUpdateDialog(context, state.info!);
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = context.watch<ThemeProvider>();
    final TextTheme textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surface,
      ),
      backgroundColor: colorScheme.surface,
      body: StateWarp(
        store: state,
        child: ListView(
          children: [
            SectionWarp(title: '', children: [
              ListTile(
                leading: Icon(Icons.settings),
                title: Text(
                  l10n.settingGenerateSetting,
                  style: textTheme.bodyMedium,
                ),
                trailing: Icon(Icons.chevron_right),
                onTap: () => context.push(Routers.generalSetting),
              ),
              ListTile(
                leading: Icon(Icons.security),
                title: Text(
                  '隐私保护',
                  style: textTheme.bodyMedium,
                ),
                subtitle: Text(
                  '任务栏切换时模糊应用内容',
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                trailing: Icon(Icons.chevron_right),
                onTap: () => context.push(Routers.securityBlurSetting),
              ),
              ListTile(
                leading: Icon(Icons.brightness_6),
                title: Text(
                  l10n.settingCheckVersion,
                  style: textTheme.bodyMedium,
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text("v${GlobalInfo.appVersion}"),
                    Icon(Icons.chevron_right),
                  ],
                ),
                onTap: () => state.sync(),
              ),
            ]),

            // 外观部分
            SectionWarp(title: l10n.settingAppearance, children: [
              ListTile(
                leading: Icon(Icons.brightness_6),
                title: Text(
                  l10n.settingAppearanceMode,
                  style: textTheme.bodyMedium,
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      themeProvider.themeModeText,
                      style: textTheme.bodySmall,
                    ),
                    Icon(Icons.chevron_right),
                  ],
                ),
                onTap: () => _showThemeModeDialog(context),
              ),
              ListTile(
                leading: Icon(Icons.style),
                title: Text(
                  l10n.settingCustomTheme,
                  style: textTheme.bodyMedium,
                ),
                trailing: Icon(Icons.chevron_right),
                onTap: () {
                  context.push(Routers.customTheme);
                },
              ),
              ListTile(
                leading: Icon(Icons.color_lens),
                title: Text(
                  l10n.settingDynamicColor,
                  style: textTheme.bodyMedium,
                ),
                trailing: Switch(
                  value: themeProvider.customTheme.enableDynamicColor,
                  onChanged: (value) {
                    themeProvider.setEnableColor(value);
                  },
                ),
              ),
              Visibility(
                visible: themeProvider.customTheme.enableDynamicColor,
                child: ColorPickerItem(
                    title: l10n.settingSeedColor,
                    color: themeProvider.customTheme.seedColor,
                    onChanged: (c) {
                      themeProvider.setSeedColor(c);
                    }),
              )
            ]),
            // 支持与反馈部分
            SectionWarp(title: l10n.settingSupport, children: [
              ListTile(
                leading: Icon(Icons.feedback),
                title: Text(
                  l10n.settingFeedback,
                  style: textTheme.bodyMedium,
                ),
                trailing: Icon(Icons.chevron_right),
                onTap: () async {
                  final email = ConfigRepo.getAllConfig().feedbackEmail;
                  ClipboardUtils.copyText(context, email);
                  UrlUtils.openUrlOut(
                      "mailto:$email?subject=意见反馈&body=用户名:${state.userModel?.username ?? ""},应用版本:${GlobalInfo.appVersion}");
                },
              ),
              // ListTile(
              //   leading: Icon(Icons.link),
              //   title: Text(
              //     l10n.settingOfficialWebsite,
              //     style: textTheme.bodyMedium,
              //   ),
              //   trailing: Icon(Icons.chevron_right),
              //   onTap: () async {
              //     UrlUtils.openUrlOut(ConfigRepo.getAllConfig().shareUrl);
              //   },
              // ),
              ListTile(
                leading: Icon(Icons.share),
                title: Text(
                  l10n.settingShare,
                  style: textTheme.bodyMedium,
                ),
                trailing: Icon(Icons.chevron_right),
                onTap: () async {
                  ClipboardUtils.copyText(
                      context,
                      l10n.settingShare2FriendTips(
                          ConfigRepo.getAllConfig().shareUrl));
                },
              ),
              Visibility(
                visible: !isIos(),
                child: ListTile(
                  leading: Icon(Icons.payment),
                  title: Text(
                    l10n.settingDonate,
                    style: textTheme.bodyMedium,
                  ),
                  trailing: Icon(Icons.chevron_right),
                  onTap: () async {
                    ConfigRepo repo = getIt.get();
                    final config = repo.getConfig();
                    final url = config.donateUrl;
                    UrlUtils.openUrlOut(url);
                  },
                ),
              ),
              ListTile(
                leading: Icon(Icons.contact_mail),
                title: Text(
                  l10n.settingAboutMe,
                  style: textTheme.bodyMedium,
                ),
                trailing: Icon(Icons.chevron_right),
                onTap: () async {
                  context.push(Routers.aboutUs);
                },
              ),
            ]),
            // 账号
            Observer(builder: (context) {
              return Visibility(
                visible: state.isLogin,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 12.0, vertical: 32),
                  child: TextButton(
                    onPressed: () {
                      showConfirm(context, confirmText: l10n.settingLogoutTips,
                          onConfirm: () {
                        state.logout();
                        context.pop();
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(l10n.settingLogout,
                            style: textTheme.bodyLarge!
                                .copyWith(color: colorScheme.primary)),
                      ],
                    ),
                  ),
                ),
              );
            }),
            // 间隔
            SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  void _showThemeModeDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: Text(l10n.settingAppearance,
                    style: TextStyle(fontWeight: FontWeight.bold)),
                trailing: IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => context.pop(),
                ),
              ),
              _buildThemeModeOption(
                  context, ThemeMode.system, l10n.settingSystemAppearance),
              _buildThemeModeOption(
                  context, ThemeMode.light, l10n.settingAppearanceLight),
              _buildThemeModeOption(
                  context, ThemeMode.dark, l10n.settingAppearanceDark),
            ],
          ),
        );
      },
    );
  }

  Widget _buildThemeModeOption(
      BuildContext context, ThemeMode mode, String text) {
    final themeProvider = context.watch<ThemeProvider>();
    return ListTile(
      title: Text(text),
      trailing: themeProvider.customTheme.themeMode == mode
          ? Icon(Icons.check)
          : null,
      onTap: () {
        context.read<ThemeProvider>().setThemeMode(context, mode);
        context.pop();
      },
    );
  }
}
