import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/common/global/GlobalInfo.dart';
import 'package:tasks/data/config/config_repo.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/utils/privacy_utils.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/utils/url_utils.dart';
import 'package:tasks/widgets/app_logo.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

class AboutUs extends StatefulWidget {
  const AboutUs({super.key});

  @override
  State<AboutUs> createState() => _AboutUsState();
}

class _AboutUsState extends State<AboutUs> {
  TextTheme get textTheme => Theme.of(context).textTheme;
  S get l10n => S.of(context);

  int _tapCount = 0;
  DateTime? _lastTapTime;

  void _handleLogoTap() {
    final now = DateTime.now();

    // 如果距离上次点击超过2秒，重置计数
    if (_lastTapTime == null || now.difference(_lastTapTime!).inSeconds > 2) {
      _tapCount = 1;
    } else {
      _tapCount++;
    }

    _lastTapTime = now;

    // 连点20次进入调试页面
    if (_tapCount >= 20) {
      _tapCount = 0;
      _lastTapTime = null;
      ToastUtils.success(context, "已进入调试模式");
      context.push(Routers.debug);
    } else if (_tapCount >= 15) {
      // 提示还需要几次
      ToastUtils.show("再点击${20 - _tapCount}次进入调试模式");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: l10n.settingAboutMe,
      ),
      body: Column(
        children: [
          SizedBox(
            height: 24,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppLogo(
                onTap: _handleLogoTap,
              ),
              SizedBox(width: 20), // 头像和作者名字之间的间距
              Text(
                GlobalInfo.appName,
                style: textTheme.titleLarge,
              ),
              SizedBox(width: 4),
              Text(
                "v${GlobalInfo.appVersion}",
                style: textTheme.bodySmall,
              ),
            ],
          ),
          SizedBox(
            height: 24,
          ),
          SectionWarp(title: '', children: [
            ListTile(
              leading: Icon(Icons.favorite),
              title: Text(
                "鼓励一下",
                style: textTheme.bodyMedium,
              ),
              trailing: Icon(Icons.chevron_right),
            ),
            Divider(
              indent: 16,
              endIndent: 16,
            ),
            ListTile(
              leading: Icon(Icons.privacy_tip_outlined),
              title: Text(
                "隐私政策",
                style: textTheme.bodyMedium,
              ),
              trailing: Icon(Icons.chevron_right),
              onTap: nav2Privacy,
            ),
            Divider(
              indent: 16,
              endIndent: 16,
            ),
            ListTile(
              leading: Icon(Icons.privacy_tip),
              title: Text(
                "服务协议",
                style: textTheme.bodyMedium,
              ),
              trailing: Icon(Icons.chevron_right),
              onTap: nav2Service,
            ),
            Divider(
              indent: 16,
              endIndent: 16,
            ),
            ListTile(
              leading: Icon(Icons.privacy_tip),
              title: Text(
                "关注小红书",
                style: textTheme.bodyMedium,
              ),
              trailing: Icon(Icons.chevron_right),
              onTap: () =>
                  UrlUtils.openUrlOut(ConfigRepo.getAllConfig().redBookLink),
            ),
          ]),
        ],
      ),
    );
  }
}
