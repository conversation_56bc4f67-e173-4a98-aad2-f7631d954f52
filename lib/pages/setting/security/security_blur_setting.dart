import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/services/app_lifecycle_service.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

/// 安全模糊设置页面
class SecurityBlurSettingPage extends StatefulWidget {
  @override
  State<SecurityBlurSettingPage> createState() =>
      _SecurityBlurSettingPageState();
}

class _SecurityBlurSettingPageState extends State<SecurityBlurSettingPage> {
  late final AppLifecycleService _lifecycleService;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    _lifecycleService = AppLifecycleManager.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: '隐私保护',
      ),
      body: Observer(
        builder: (context) {
          return SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSecuritySection(),
                SizedBox(height: 16),
                _buildBlurSettingsSection(),
                SizedBox(height: 16),
                _buildTestSection(),
                SizedBox(height: 16),
                _buildInfoSection(),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建安全设置区域
  Widget _buildSecuritySection() {
    return SectionWarp(
      title: '安全设置',
      children: [
        SwitchListTile(
          title: Text(
            '启用隐私保护',
            style: textTheme.titleMedium,
          ),
          subtitle: Text(
            '切换到任务栏时自动模糊应用内容',
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          value: _lifecycleService.enableSecurityBlur,
          onChanged: (value) async {
            await _lifecycleService.toggleSecurityBlur(value);
          },
          activeColor: colorScheme.primary,
        ),
      ],
    );
  }

  /// 构建模糊设置区域
  Widget _buildBlurSettingsSection() {
    return SectionWarp(
      title: '模糊效果',
      children: [
        ListTile(
          title: Text(
            '模糊强度',
            style: textTheme.titleMedium,
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '调整模糊效果的强度 (${_lifecycleService.blurSigma.toStringAsFixed(1)})',
                style: textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              SizedBox(height: 8),
              Slider(
                value: _lifecycleService.blurSigma,
                min: 0.0,
                max: 20.0,
                divisions: 20,
                label: _lifecycleService.blurSigma.toStringAsFixed(1),
                onChanged: _lifecycleService.enableSecurityBlur
                    ? (value) async {
                        await _lifecycleService.setBlurSigma(value);
                      }
                    : null,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建测试区域
  Widget _buildTestSection() {
    return SectionWarp(
      title: '测试功能',
      children: [
        ListTile(
          title: Text(
            '测试模糊效果',
            style: textTheme.titleMedium,
          ),
          subtitle: Text(
            '点击预览模糊效果（持续2秒）',
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          trailing: ElevatedButton(
            onPressed: _lifecycleService.enableSecurityBlur
                ? () {
                    _lifecycleService.triggerBlur();
                  }
                : null,
            child: Text('测试'),
          ),
        ),
      ],
    );
  }

  /// 构建信息说明区域
  Widget _buildInfoSection() {
    return SectionWarp(
      title: '功能说明',
      children: [
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: colorScheme.primaryContainer.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: colorScheme.primary.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 20,
                    color: colorScheme.primary,
                  ),
                  SizedBox(width: 8),
                  Text(
                    '隐私保护功能',
                    style: textTheme.titleSmall?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Text(
                '• 当应用切换到后台或失去焦点时，自动显示模糊遮罩\n'
                '• 防止他人在任务栏中看到您的隐私信息\n'
                '• 类似支付宝等金融应用的安全保护机制\n'
                '• 可自定义模糊强度以适应不同需求',
                style: textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
