import 'package:flutter/material.dart';

class UpdateIndicator extends StatelessWidget {
  final bool hasUpdate; // 是否有更新
  final String version;

  const UpdateIndicator(
      {Key? key, required this.hasUpdate, required this.version})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Stack(
        alignment: Alignment.center, // 子组件居中对齐
        clipBehavior: Clip.none,
        children: [
          // 文本
          Text(
            version,
            style: textTheme.bodyMedium,
          ),
          // 红点
          if (hasUpdate) // 如果有更新，显示红点
            Positioned(
              right: -40, // 调整红点的水平位置
              top: -8, // 调整红点的垂直位置
              child: Row(
                children: [
                  Container(
                    width: 6, // 红点宽度
                    height: 6, // 红点高度
                    decoration: BoxDecoration(
                      color: Colors.red, // 红点颜色
                      shape: BoxShape.circle, // 圆形
                    ),
                  ),
                  Text(
                    "点击更新",
                    style: textTheme.bodySmall!.copyWith(fontSize: 10),
                  )
                ],
              ),
            ),
        ],
      ),
    );
  }
}
