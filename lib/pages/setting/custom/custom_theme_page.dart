import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/custom_theme/custom_theme.dart';
import 'package:tasks/pages/setting/custom/custom_theme_item.dart';
import 'package:tasks/providers/theme_provider.dart';
import 'package:tasks/utils/clipboard_utils.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';

class CustomThemePage extends StatefulWidget {
  @override
  _CustomThemePageState createState() => _CustomThemePageState();
}

class _CustomThemePageState extends State<CustomThemePage> {
  late CustomTheme _customTheme;
  late ThemeProvider _themeProvider;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    _themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    final theme = _themeProvider.customTheme;
    setState(() {
      _customTheme = theme;
    });
  }

  Future<void> _saveTheme() async {
    await _themeProvider.writeTheme(_customTheme);
    if (!mounted) return;
    ToastUtils.success(context, l10n.settingCustomThemeSaved);
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        actions: [
          TextButton.icon(
            label: Text(l10n.settingThemeShare),
            // 新增重置按钮
            icon: Icon(Icons.share),
            onPressed: () {
              try {
                final value = jsonEncode(_customTheme.toJson());
                ClipboardUtils.copyText(context, value);
              } catch (e) {
                // doNothing
              }
            },
          ),
          TextButton.icon(
            label: Text(l10n.settingThemeImport),
            icon: Icon(Icons.import_export),
            onPressed: () async {
              try {
                final copyValue = await ClipboardUtils.pasteText(context);
                Map<String, dynamic> map = jsonDecode(copyValue ?? "{}");
                final value = CustomTheme.fromJson(map);
                ToastUtils.success(context, l10n.settingThemeImportSuccess);
                setState(() {
                  _customTheme = value;
                });
              } catch (e) {
                ToastUtils.error(context, l10n.settingThemeImportFailed);
              }
            },
          ),
          TextButton.icon(
            label: Text(l10n.commonSave),
            icon: Icon(Icons.save),
            onPressed: _saveTheme,
          ),
        ],
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.0),
        children: [
          Row(
            children: [
              TextButton(
                  onPressed: () {
                    setState(() {
                      _customTheme = CustomTheme.defaultCustomTheme();
                    });
                  },
                  child: Text(l10n.settingThemePureWhite)),
              TextButton(
                  onPressed: () {
                    setState(() {
                      _customTheme = CustomTheme.defaultBlackTheme();
                    });
                  },
                  child: Text(l10n.settingThemePureBlack)),
              TextButton(
                  onPressed: () {
                    setState(() {
                      _customTheme = CustomTheme.classicCustomTheme();
                    });
                  },
                  child: Text(l10n.settingThemeClassic)),
            ],
          ),
          SizedBox(
            height: 12,
          ),
          // 首页统计
          CustomThemeItem(
            value: _customTheme.topCard,
            title: l10n.settingStatisticalColorStart,
            onValueChanged: (v) {
              setState(() {
                _customTheme = _customTheme.copyWith(topCard: v);
              });
            },
          ),
          SizedBox(
            height: 12,
          ),
          // 普通卡片
          CustomThemeItem(
            value: _customTheme.normalCard,
            title: l10n.settingNormalColorStart,
            onValueChanged: (v) {
              setState(() {
                _customTheme = _customTheme.copyWith(normalCard: v);
              });
            },
          ),
          SizedBox(
            height: 12,
          ),
          // 收藏卡片
          CustomThemeItem(
            value: _customTheme.favoriteCard,
            title: l10n.settingCollectColorStart,
            onValueChanged: (v) {
              setState(() {
                _customTheme = _customTheme.copyWith(favoriteCard: v);
              });
            },
          ),
          SizedBox(
            height: 12,
          ),
          // 退役卡片
          CustomThemeItem(
            value: _customTheme.retiredCard,
            title: l10n.settingRetriedColorStart,
            onValueChanged: (v) {
              setState(() {
                _customTheme = _customTheme.copyWith(retiredCard: v);
              });
            },
          ),
          // 带点间隔
          SizedBox(
            height: 60,
          )
        ],
      ),
    );
  }
}
