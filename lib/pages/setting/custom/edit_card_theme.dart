import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/custom_theme/custom_card_theme.dart';
import 'package:tasks/pages/setting/custom/widget/color_select_item.dart';

import 'custom_theme_item.dart';

class EditCardThemeDialog extends StatefulWidget {
  final CustomCardTheme value;

  EditCardThemeDialog({super.key, required this.value});

  @override
  State<EditCardThemeDialog> createState() => _EditCardThemeDialogState();
}

class _EditCardThemeDialogState extends State<EditCardThemeDialog> {
  late CustomCardTheme value;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    setState(() {
      value = widget.value;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 56,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // 关闭弹窗
                },
                child: Text(
                  l10n.cancel,
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
              ),
              Text(
                "编辑卡片配色",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  context.pop(value); // 关闭弹窗
                },
                child: Text(
                  '确定',
                  style: TextStyle(
                    color: colorScheme.primary,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
        ColorSelectItem(
            title: "渐变开始",
            value: value.startColor,
            onValueChanged: (v) {
              setState(() {
                value = value.copyWith(startColor: v);
              });
            }),
        ColorSelectItem(
            title: "渐变结束",
            value: value.endColor,
            onValueChanged: (v) {
              setState(() {
                value = value.copyWith(endColor: v);
              });
            }),
        ColorSelectItem(
            title: "深色模式下文本颜色",
            value: value.onContainerDarkColor,
            onValueChanged: (v) {
              setState(() {
                value = value.copyWith(onContainerDarkColor: v);
              });
            }),
        ColorSelectItem(
            title: "浅色模式下文本颜色",
            value: value.onContainerLightColor,
            onValueChanged: (v) {
              setState(() {
                value = value.copyWith(onContainerLightColor: v);
              });
            }),
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: CustomThemeItem(
            value: value,
            title: "预览",
            onValueChanged: (v) {},
            enableEdit: false,
          ),
        ),
      ],
    );
  }
}

Future<CustomCardTheme?> showEditCardDialog(
    BuildContext context, CustomCardTheme theme) async {
  return await showModalBottomSheet<CustomCardTheme?>(
    context: context,
    builder: (BuildContext context) {
      return EditCardThemeDialog(
        value: theme,
      );
    },
  );
}
