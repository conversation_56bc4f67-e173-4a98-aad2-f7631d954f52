import 'package:flutter/material.dart';
import 'package:tasks/modal/color_picker/color_picker.dart';

class ColorSelectItem extends StatelessWidget {
  final String title;

  final Color value;

  final ValueChanged<Color> onValueChanged;

  const ColorSelectItem(
      {super.key,
      required this.title,
      required this.value,
      required this.onValueChanged});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        final color = await openColorPicker(context, value);
        if (color != null) {
          onValueChanged(color);
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(title),
            Container(
              width: 48,
              height: 48,
              color: value,
            )
          ],
        ),
      ),
    );
  }
}
