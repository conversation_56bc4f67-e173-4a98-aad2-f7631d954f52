import 'package:flutter/material.dart';
import 'package:tasks/models/custom_theme/custom_card_theme.dart';

import 'edit_card_theme.dart';

class CustomThemeItem extends StatelessWidget {
  final CustomCardTheme value;
  final ValueChanged<CustomCardTheme> onValueChanged;
  final String title;
  final bool enableEdit;

  CustomThemeItem(
      {super.key,
      required this.value,
      required this.onValueChanged,
      required this.title,
      this.enableEdit = true});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;

    return GestureDetector(
      onTap: () async {
        if (!enableEdit) {
          return;
        }
        final result = await showEditCardDialog(context, value);
        if (result != null) {
          onValueChanged(result);
        }
      },
      child: Container(
        height: 60,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [value.startColor, value.endColor],
          ),
        ),
        child: Center(
            child: Text(
          title,
          style: textTheme.titleMedium!
              .copyWith(color: value.onContainerColor(context)),
        )),
      ),
    );
  }
}
