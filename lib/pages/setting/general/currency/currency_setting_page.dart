import 'package:flutter/material.dart';
import 'package:tasks/providers/user_preferences_store.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/currency/currency_model.dart';
import 'package:tasks/models/user_preferences/user_preferences_entity.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';

class CurrencySettingPage extends StatefulWidget {
  const CurrencySettingPage({Key? key}) : super(key: key);

  @override
  _CurrencySettingPageState createState() => _CurrencySettingPageState();
}

class _CurrencySettingPageState extends State<CurrencySettingPage> {
  UserPreferencesStore _userPreferencesStore = getIt.get();
  late UserPreferencesEntity _preferences;
  bool _isLoading = true;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    try {
      final preferences = _userPreferencesStore.preferencesEntity;
      setState(() {
        _preferences = preferences;
        _isLoading = false;
      });
    } catch (e) {
      print("加载用户偏好设置出错：$e");
      setState(() {
        _preferences = UserPreferencesEntity.defaultPreferences();
        _isLoading = false;
      });
    }
  }

  Future<void> _updateCurrency(String code, String symbol) async {
    try {
      // 检查是否是自定义货币
      final isCustomCurrency = !CurrencyModel.availableCurrencies
          .any((currency) => currency.code == code);

      if (isCustomCurrency) {
        // 如果是自定义货币，先保存到自定义货币列表
        final customCurrency = CustomCurrency(
          code: code,
          symbol: symbol,
          createdAt: DateTime.now(),
        );
        await _userPreferencesStore.addCustomCurrency(customCurrency);
      }

      // 更新当前选中的货币
      await _userPreferencesStore.updatePreference(
        currencyCode: code,
        currencySymbol: symbol,
      );

      setState(() {
        _preferences = _preferences.copyWith(
          currencyCode: code,
          currencySymbol: symbol,
        );
      });
      ToastUtils.info(context, l10n.settingCurrencyUpdated);
    } catch (e) {
      print("更新货币设置出错：$e");
      ToastUtils.error(context, "更新失败，请重试");
    }
  }

  void _showCustomCurrencyDialog() {
    // 检查当前货币是否是自定义货币
    final selectedCustomCurrency = _preferences.customCurrencies
        .where((currency) => currency.code == _preferences.currencyCode)
        .firstOrNull;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _CustomCurrencyBottomSheet(
        initialCode: selectedCustomCurrency?.code ?? '',
        initialSymbol: selectedCustomCurrency?.symbol ?? '',
        onSave: (code, symbol) async {
          await _updateCurrency(code, symbol);
          // 重新加载偏好设置以更新UI
          final updatedPreferences = _userPreferencesStore.preferencesEntity;
          setState(() {
            _preferences = updatedPreferences;
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final colorScheme = Theme.of(context).colorScheme;

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: Text(l10n.settingCurrencySetting)),
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surface,
        title: l10n.settingCurrencySetting,
      ),
      body: _buildCurrencyList(),
    );
  }

  Widget _buildCurrencyList() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    // 检查当前选中的货币类型
    final isPresetSelected = CurrencyModel.availableCurrencies
        .any((currency) => currency.code == _preferences.currencyCode);
    final selectedCustomCurrency = _preferences.customCurrencies
        .where((currency) => currency.code == _preferences.currencyCode)
        .firstOrNull;

    return ListView(
      padding: EdgeInsets.symmetric(vertical: 8),
      children: [
        // 自定义货币选项 - 放在最前面
        Container(
          margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: !isPresetSelected && selectedCustomCurrency == null
                ? colorScheme.primaryContainer.withValues(alpha: 0.3)
                : colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: !isPresetSelected && selectedCustomCurrency == null
                  ? colorScheme.primary.withValues(alpha: 0.5)
                  : colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: ListTile(
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            leading: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.add_rounded,
                color: colorScheme.primary,
                size: 20,
              ),
            ),
            title: Text(
              l10n.settingCustomCurrency,
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: !isPresetSelected && selectedCustomCurrency == null
                    ? colorScheme.primary
                    : null,
              ),
            ),
            subtitle: Padding(
              padding: EdgeInsets.only(top: 4),
              child: Text(
                l10n.settingCustomCurrencyHint,
                style: textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ),
            trailing: !isPresetSelected && selectedCustomCurrency == null
                ? Icon(Icons.check_circle, color: colorScheme.primary, size: 24)
                : Icon(Icons.arrow_forward_ios,
                    size: 16,
                    color: colorScheme.onSurface.withValues(alpha: 0.5)),
            onTap: _showCustomCurrencyDialog,
          ),
        ),

        // 自定义货币列表
        if (_preferences.customCurrencies.isNotEmpty) ...[
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                Expanded(child: Divider()),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    '我的货币',
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Expanded(child: Divider()),
              ],
            ),
          ),
          ..._preferences.customCurrencies.map((currency) {
            final isSelected = currency.code == _preferences.currencyCode;
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 16, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected
                    ? colorScheme.primaryContainer.withValues(alpha: 0.2)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListTile(
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                leading: Container(
                  padding: EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: colorScheme.secondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.stars_rounded,
                    color: colorScheme.secondary,
                    size: 16,
                  ),
                ),
                title: Text(
                  '${currency.displayName} (${currency.code})',
                  style: textTheme.bodyLarge?.copyWith(
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                    color: isSelected ? colorScheme.primary : null,
                  ),
                ),
                subtitle: Text(
                  currency.formatPreview(1234.56),
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (isSelected)
                      Icon(Icons.check_circle,
                          color: colorScheme.primary, size: 24)
                    else
                      SizedBox(width: 24),
                    SizedBox(width: 8),
                    IconButton(
                      onPressed: () => _deleteCustomCurrency(currency.code),
                      icon: Icon(Icons.delete_outline, size: 20),
                      style: IconButton.styleFrom(
                        backgroundColor:
                            colorScheme.error.withValues(alpha: 0.1),
                        foregroundColor: colorScheme.error,
                      ),
                    ),
                  ],
                ),
                onTap: () {
                  if (!isSelected) {
                    _updateCurrency(currency.code, currency.symbol);
                  }
                },
              ),
            );
          }).toList(),
        ],

        // 分割线和标题
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            children: [
              Expanded(child: Divider()),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  '预设货币',
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Expanded(child: Divider()),
            ],
          ),
        ),

        // 预设货币列表
        ...CurrencyModel.availableCurrencies.map((currency) {
          final isSelected = currency.code == _preferences.currencyCode;
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16, vertical: 2),
            decoration: BoxDecoration(
              color: isSelected
                  ? colorScheme.primaryContainer.withValues(alpha: 0.2)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListTile(
              contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              title: Text(
                '${currency.name} (${currency.code})',
                style: textTheme.bodyLarge?.copyWith(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected ? colorScheme.primary : null,
                ),
              ),
              subtitle: Text(
                '${currency.symbol}1,234.56',
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              trailing: isSelected
                  ? Icon(Icons.check_circle,
                      color: colorScheme.primary, size: 24)
                  : null,
              onTap: () {
                if (!isSelected) {
                  _updateCurrency(currency.code, currency.symbol);
                }
              },
            ),
          );
        }).toList(),

        // 底部间距
        SizedBox(height: 16),
      ],
    );
  }

  Future<void> _deleteCustomCurrency(String currencyCode) async {
    try {
      await _userPreferencesStore.removeCustomCurrency(currencyCode);

      // 如果删除的是当前选中的货币，切换到默认货币
      if (_preferences.currencyCode == currencyCode) {
        await _updateCurrency('CNY', '¥');
      }

      // 重新加载偏好设置
      final updatedPreferences = _userPreferencesStore.preferencesEntity;
      setState(() {
        _preferences = updatedPreferences;
      });

      ToastUtils.info(context, '自定义货币已删除');
    } catch (e) {
      print("删除自定义货币出错：$e");
      ToastUtils.error(context, "删除失败，请重试");
    }
  }
}

// 自定义货币底部弹窗组件
class _CustomCurrencyBottomSheet extends StatefulWidget {
  final String initialCode;
  final String initialSymbol;
  final Future<void> Function(String code, String symbol) onSave;

  const _CustomCurrencyBottomSheet({
    required this.initialCode,
    required this.initialSymbol,
    required this.onSave,
  });

  @override
  State<_CustomCurrencyBottomSheet> createState() =>
      _CustomCurrencyBottomSheetState();
}

class _CustomCurrencyBottomSheetState
    extends State<_CustomCurrencyBottomSheet> {
  String _code = '';
  String _symbol = '';

  @override
  void initState() {
    super.initState();
    _code = widget.initialCode;
    _symbol = widget.initialSymbol;
  }

  Future<void> _save() async {
    final code = _code.trim().toUpperCase();
    final symbol = _symbol.trim();

    // 验证货币代码
    if (code.isEmpty || code.length > 10) {
      ToastUtils.error(context, S.of(context).settingCurrencyCodeError);
      return;
    }

    // 验证货币符号
    if (symbol.isEmpty || symbol.length > 5) {
      ToastUtils.error(context, S.of(context).settingCurrencySymbolError);
      return;
    }

    try {
      await widget.onSave(code, symbol);
      Navigator.of(context).pop();
    } catch (e) {
      ToastUtils.error(context, "保存失败，请重试");
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final mediaQuery = MediaQuery.of(context);

    return Container(
      margin: EdgeInsets.only(bottom: mediaQuery.viewInsets.bottom),
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              margin: EdgeInsets.only(top: 12, bottom: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: colorScheme.onSurface.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 标题栏
            Container(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.edit_rounded,
                      color: colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          l10n.settingCustomCurrency,
                          style: textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          '创建您专属的货币设置',
                          style: textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(Icons.close_rounded),
                    style: IconButton.styleFrom(
                      backgroundColor:
                          colorScheme.onSurface.withValues(alpha: 0.1),
                    ),
                  ),
                ],
              ),
            ),

            // 内容区域
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  // 信息提示卡片
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color:
                          colorScheme.primaryContainer.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: colorScheme.primary.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline_rounded,
                          color: colorScheme.primary,
                          size: 20,
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            '支持加密货币或其他自定义货币，代码最多10个字符，符号最多5个字符',
                            style: textTheme.bodySmall?.copyWith(
                              color:
                                  colorScheme.onSurface.withValues(alpha: 0.8),
                              height: 1.4,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 24),

                  // 输入字段标签
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      l10n.settingCurrencyCodeLabel,
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ),

                  SizedBox(height: 8),

                  // 货币代码输入
                  ClearInputTextField(
                    hintText: l10n.settingCurrencyCodeHint,
                    icon: Icons.code_rounded,
                    value: _code,
                    keyboardType: TextInputType.text,
                    filterPattern: RegExp(r'^.{0,10}$'),
                    onChange: (value) {
                      if (value.length <= 10) {
                        setState(() {
                          _code = value.toUpperCase();
                        });
                      }
                    },
                    fillColor: colorScheme.surfaceContainer,
                  ),

                  SizedBox(height: 20),

                  // 输入字段标签
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      l10n.settingCurrencySymbolLabel,
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ),

                  SizedBox(height: 8),

                  // 货币符号输入
                  ClearInputTextField(
                    hintText: l10n.settingCurrencySymbolHint,
                    icon: Icons.attach_money_rounded,
                    value: _symbol,
                    keyboardType: TextInputType.text,
                    filterPattern: RegExp(r'^.{0,5}$'),
                    onChange: (value) {
                      if (value.length <= 5) {
                        setState(() {
                          _symbol = value;
                        });
                      }
                    },
                    fillColor: colorScheme.surfaceContainer,
                  ),
                ],
              ),
            ),

            // 底部按钮区域
            Container(
              padding: EdgeInsets.all(24),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: colorScheme.outline.withValues(alpha: 0.3),
                          ),
                        ),
                      ),
                      child: Text(
                        l10n.settingCancel,
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.onSurface.withValues(alpha: 0.7),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _save,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colorScheme.primary,
                        foregroundColor: colorScheme.onPrimary,
                        padding: EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.save_rounded, size: 20),
                          SizedBox(width: 8),
                          Text(
                            l10n.settingSave,
                            style: textTheme.titleMedium?.copyWith(
                              color: colorScheme.onPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 底部安全区域
            SizedBox(height: mediaQuery.padding.bottom),
          ],
        ),
      ),
    );
  }
}
