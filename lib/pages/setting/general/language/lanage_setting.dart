import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/providers/theme_provider.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

class LanguageSettingPage extends StatefulWidget {
  const LanguageSettingPage({super.key});

  @override
  _LanguageSettingPageState createState() => _LanguageSettingPageState();
}

class _LanguageSettingPageState extends State<LanguageSettingPage> {
  late ThemeProvider _themeProvider;

  S get l10n => S.of(context);

  Locale? curLocale;

  Locale? _selectedLocale; // 默认语言

  bool get isSimpleChinese =>
      _selectedLocale?.languageCode == 'zh' &&
      _selectedLocale?.countryCode == 'CN';

  bool get isTraditionalChinese =>
      _selectedLocale?.languageCode == 'zh' &&
      _selectedLocale?.countryCode == 'TW';

  bool get isEnglish => _selectedLocale?.languageCode == 'en';

  bool get isSystem => _selectedLocale == null;

  bool get enableSave => curLocale != _selectedLocale;

  @override
  void initState() {
    super.initState();
    // 初始获取语言
    _themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    setState(() {
      curLocale = _themeProvider.locale;
      _selectedLocale = _themeProvider.locale;
    });
  }

  void _changeLanguage(Locale? locale) {
    setState(() {
      _selectedLocale = locale;
    });
  }

  Future<void> _saveLanguage() async {
    // 保存语言设置
    await _themeProvider.setLocale(_selectedLocale);
    context.go(Routers.main);
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surface,
        title: l10n.settingLanguageSetting,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: SizedBox(
                height: 32,
                child: FilledButton(
                    onPressed: enableSave ? _saveLanguage : null,
                    child:
                        Text(l10n.commonSave, style: TextStyle(fontSize: 14)))),
          )
        ],
      ),
      body: Column(
        children: [
          SectionWarp(title: l10n.settingSelectLanguage, children: [
            ListTile(
                title: Text(
                  l10n.settingSystemLanguage,
                  style: textTheme.bodyMedium,
                ),
                trailing: isSystem
                    ? Icon(Icons.check, color: colorScheme.primary)
                    : null,
                onTap: () => _changeLanguage(null)),
            const Divider(
              indent: 16,
              endIndent: 16,
            ),
            ListTile(
                title: Text(
                  '简体中文',
                  style: textTheme.bodyMedium,
                ),
                trailing: isSimpleChinese
                    ? Icon(Icons.check, color: colorScheme.primary)
                    : null,
                onTap: () => _changeLanguage(const Locale('zh', 'CN'))),
            const Divider(
              indent: 16,
              endIndent: 16,
            ),
            ListTile(
                title: Text(
                  '繁体中文',
                  style: textTheme.bodyMedium,
                ),
                trailing: isTraditionalChinese
                    ? Icon(Icons.check, color: colorScheme.primary)
                    : null,
                onTap: () => _changeLanguage(const Locale('zh', 'TW'))),
            const Divider(
              indent: 16,
              endIndent: 16,
            ),
            ListTile(
              title: Text(
                'English',
                style: textTheme.bodyMedium,
              ),
              trailing: isEnglish
                  ? Icon(Icons.check, color: colorScheme.primary)
                  : null,
              onTap: () => _changeLanguage(const Locale('en')),
            ),
          ])
        ],
      ),
    );
  }
}
