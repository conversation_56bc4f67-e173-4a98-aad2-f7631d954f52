import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

class GeneralSettingPage extends StatelessWidget {
  const GeneralSettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    final textTheme = Theme.of(context).textTheme;
    final l10n = S.of(context);
    return Scaffold(
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surface,
        title: l10n.settingGenerateSetting,
      ),
      body: Column(
        children: [
          SectionWarp(title: "", children: [
            ListTile(
              leading: Icon(Icons.settings),
              title: Text(
                l10n.settingLanguage,
                style: textTheme.bodyMedium,
              ),
              trailing: Icon(Icons.chevron_right),
              onTap: () => context.push(Routers.languageSetting),
            ),
            ListTile(
              leading: Icon(Icons.manage_accounts),
              title: Text(
                l10n.settingAccountManage,
                style: textTheme.bodyMedium,
              ),
              trailing: Icon(Icons.chevron_right),
              onTap: () => context.push(Routers.editUserInfo),
            ),
          ])
        ],
      ),
    );
  }
}
