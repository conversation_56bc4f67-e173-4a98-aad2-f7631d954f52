import 'package:mobx/mobx.dart';
import 'package:tasks/data/share/invited_record_resp.dart';
import 'package:tasks/data/share/share_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/user_store.dart';

part 'share_state.g.dart';

class ShareState = _ShareState with _$ShareState;

abstract class _ShareState extends BaseStore with Store {
  UserStore userStore = getIt.get();
  ShareRepo repo = getIt.get();

  @computed
  String get inviteCode => userStore.userModel?.inviteCode ?? '未登录？';

  @computed
  bool get isLogin => userStore.isLogin;

  @observable
  List<InvitedRecordItem> records = [];

  _ShareState() {
    syncPage();
  }

  @action
  Future<void> syncPage() async {
    runWithLoading(() async {
      if (!isLogin) {
        return;
      }
      final result = await repo.getInvitedRecord();
      runInAction(() {
        records = result.data ?? [];
      });
    });
  }
}
