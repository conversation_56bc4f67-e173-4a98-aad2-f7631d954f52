import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/data/config/config_repo.dart';
import 'package:tasks/data/share/invited_record_resp.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/pages/share/share_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/clipboard_utils.dart';
import 'package:tasks/utils/url_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

class SharePage extends StatefulWidget {
  const SharePage({super.key});

  @override
  State<SharePage> createState() => _SharePageState();
}

class _SharePageState extends State<SharePage> {
  ShareState state = ShareState();

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surfaceContainer,
        title: l10n.ucenterInviteFriends,
        actions: [
          InkWell(
            onTap: () {
              UrlUtils.openUrlOut(ConfigRepo.getAllConfig().shareUrl);
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(l10n.ucenterRulesDetail),
            ),
          )
        ],
      ),
      body: Container(
        color: colorScheme.surfaceContainer,
        child: StateWarp(
          store: state,
          child: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 20,
                        color: colorScheme.primary,
                      ),
                      SizedBox(
                        width: 8,
                      ),
                      Expanded(
                        child: Text(
                          "邀请好友，好友付费后，你获得现金奖励。邀请的越多，你获得现金奖励。具体规则，请查看详情。",
                          softWrap: true,
                          overflow: TextOverflow.visible,
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "你的邀请码：",
                      style: textTheme.titleLarge,
                    ),
                    SizedBox(
                      width: 12,
                    ),
                    Observer(builder: (context) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(state.inviteCode),
                          Visibility(
                            visible: state.isLogin,
                            child: IconButton(
                              onPressed: () {
                                ClipboardUtils.copyText(
                                    context, state.inviteCode);
                              },
                              icon: Icon(
                                Icons.copy,
                                size: 16,
                                color: colorScheme.primary,
                              ),
                            ),
                          )
                        ],
                      );
                    }),
                  ],
                ),
                Observer(builder: (context) {
                  return state.isLogin
                      ? SectionWarp(
                          title: "邀请列表",
                          children: List.generate(state.records.length, (i) {
                            return buildItem(state.records[i]);
                          }),
                        )
                      : SizedBox();
                }),
                SizedBox(
                  height: 24,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildItem(InvitedRecordItem item) {
    return ListTile(
      title: Text(item.maskUserName ?? ""),
      subtitle: Text(item.createTime ?? ""),
      trailing: item.isVip == true
          ? Text(
              "会员",
              style: textTheme.bodyMedium!.copyWith(color: colorScheme.primary),
            )
          : Text(
              "普通用户",
              style: textTheme.bodyMedium,
            ),
    );
  }
}
