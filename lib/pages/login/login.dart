import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/models/user/login_type.dart';
import 'package:tasks/pages/login/action/wechat_login.dart';
import 'package:tasks/pages/login/login_state.dart';
import 'package:tasks/pages/login/widgets/change_login_method.dart';
import 'package:tasks/pages/login/widgets/email_login.dart';
import 'package:tasks/pages/login/widgets/normal_login.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/providers/user_store.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/agree_privacy.dart';
import 'package:tasks/widgets/app_logo.dart';
import 'package:tasks/widgets/common_app_bar.dart';

class LoginPage extends StatefulWidget {
  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  late LoginState _state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    _state = LoginState();
    // 监听登录结果
    reaction((_) => _state.event, (r) {
      _state.clearEvent();
      // 需要同意服务协议
      if (r == LoginEvent.notAgree) {
        showConfirm(context, content: l10n.loginConfirm, onConfirm: () {
          _state.updateAgree(true);
          // 重新执行登录
          if (_state.loginType == LoginType.normal ||
              _state.loginType == LoginType.email) {
            _state.login();
          }
        });
        return;
      }
      // 登录成功
      if (r == LoginEvent.loginSuccess) {
        ToastUtils.success(context, l10n.loginSuccessTips);
        // 刷新用户信息
        getIt.get<UserStore>().syncFetchUserInfo();
        context.pop();
        return;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    ColorScheme colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: CommonAppBar(
        title: "",
        backgroundColor: colorScheme.surface,
      ),
      resizeToAvoidBottomInset: true,
      body: StateWarp(
        store: _state,
        child: Center(
          child: SingleChildScrollView(
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo
                    AppLogo(
                      size: 88,
                    ),
                    SizedBox(
                      height: 24,
                    ),
                    Observer(builder: (context) {
                      return ChangeLoginMethod(
                          value: _state.loginType,
                          onChanged: _state.setLoginType);
                    }),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 8.0, bottom: 12),
                        child: Text(
                          "首次使用验证码登录将会注册账号",
                          style: textTheme.labelSmall,
                        ),
                      ),
                    ),
                    // 登录内容
                    Observer(builder: (context) {
                      return Visibility(
                          visible: _state.loginType == LoginType.normal,
                          child: NormalLogin(
                              userName: _state.username,
                              password: _state.password,
                              onUserNameChange: _state.setUsername,
                              onPasswordChange: _state.setPassword));
                    }),
                    // 邮箱登录
                    Observer(builder: (context) {
                      return Visibility(
                          visible: _state.loginType == LoginType.email,
                          child: EmailLoginInput(
                            email: _state.email,
                            onEmailChange: _state.setEmail,
                            onVerifyCodeChange: _state.setVerifyCode,
                            getVerifyCode: _state.getVerifyCode,
                          ));
                    }),
                    // 注册账号和忘记密码
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                          onPressed: () async {
                            // 注册账号逻辑
                            await context.push(Routers.register);
                            _state.init();
                          },
                          child: Text(
                            l10n.loginRegisterAccount,
                            style: textTheme.bodyMedium!
                                .copyWith(color: colorScheme.primary),
                          ),
                        ),
                        Spacer(),
                        TextButton(
                          onPressed: forgotPassword,
                          child: Text(l10n.loginForgetPassword,
                              style: textTheme.bodyMedium!
                                  .copyWith(color: colorScheme.primary)),
                        ),
                      ],
                    ),

                    // 登录按钮
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: ElevatedButton(
                        onPressed: () {
                          // 隐藏键盘
                          FocusScope.of(context).unfocus();
                          // 执行登录
                          _state.login();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          minimumSize: Size(double.infinity, 50),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              l10n.loginGoLogin,
                              style: textTheme.titleMedium!
                                  .copyWith(color: colorScheme.onPrimary),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 12,
                    ),
                    // 协议
                    Observer(builder: (context) {
                      return AgreePrivacy(
                        value: _state.agree,
                        onChanged: _state.updateAgree,
                      );
                    }),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(
                          height: 32,
                        ),
                        Text(
                          l10n.loginThirdLoginMethod,
                          style: textTheme.bodyMedium,
                        ),
                        SizedBox(
                          height: 16,
                        ),
                        Observer(builder: (context) {
                          return WechatLoginAction(
                            isAgree: _state.agree,
                            onAgree: () {
                              _state.updateAgree(true);
                            },
                          );
                        })
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void forgotPassword() {
    showConfirm(context, content: "请在设置->关于我中联系开发者重置密码", onConfirm: () {
      context.pushReplacement(Routers.aboutUs);
    });
  }
}
