import 'package:mobx/mobx.dart';
import 'package:tasks/data/user/LoginUseCase.dart';
import 'package:tasks/data/user/user_data_source.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/user/email_verify_code_type.dart';
import 'package:tasks/models/user/login_type.dart';
import 'package:tasks/pages/base_store.dart';

part 'login_state.g.dart';

class LoginState = _LoginState with _$LoginState;

enum LoginEvent { loginSuccess, bindWechat, notAgree }

abstract class _LoginState extends BaseStore with Store {
  LoginUseCase useCase = getIt.get();

  UserRepo repo = getIt.get();

  UserDataSource _source = getIt.get();

  @observable
  String username = "";

  @observable
  String password = "";

  @observable
  String email = "";

  @observable
  String _verifyCode = "";

  @observable
  LoginEvent? event;

  @observable
  bool agree = false;

  @observable
  LoginType loginType = LoginType.normal;

  _LoginState() {
    init();
  }

  @action
  void init() {
    final cacheInfo = _source.getLoginInfo();
    if (cacheInfo != null) {
      username = cacheInfo.username ?? "";
      password = cacheInfo.password ?? "";
      email = cacheInfo.email ?? "";
    }
  }

  @action
  void setLoginType(LoginType value) {
    loginType = value;
  }

  @action
  void setUsername(String value) {
    username = value;
  }

  @action
  void setPassword(String value) {
    password = value;
  }

  @action
  void setEmail(String value) {
    print('setEmail:${value}');
    email = value;
  }

  @action
  void setVerifyCode(String value) {
    _verifyCode = value;
  }

  /// 获取登录验证码
  Future<bool> getVerifyCode() async {
    if (email.isEmpty) {
      setError("请输入有效的邮箱");
      return false;
    }
    final r = await runWithLoading(() async {
      final result = await repo.getEmailVerifyCode(
          email, EmailVerifyCodeType.registerOrLogin);
      return result.isSuccess == true;
    });
    return r == true;
  }

  // 登录逻辑
  @action
  Future<void> login() async {
    if (loginType == LoginType.normal) {
      passwordLogin();
      return;
    }
    emailLogin();
  }

  @action
  Future<void> passwordLogin() async {
    // 数据校验
    if (username.isEmpty) {
      setError("用户名不能为空");
      return;
    }
    if (password.isEmpty) {
      setError("密码不能为空");
      return;
    }
    if (agree != true) {
      event = LoginEvent.notAgree;
      return;
    }
    runWithLoading(() async {
      final result = await useCase.login(username, password);
      if (result.isSuccess) {
        // 登录成功
        event = LoginEvent.loginSuccess;
        return;
      }
      setError(result.msg ?? "登录失败");
    });
  }

  @action
  Future<void> emailLogin() async {
    // 数据校验
    if (email.isEmpty) {
      setError("请输入有效的邮箱");
      return;
    }
    if (_verifyCode.isEmpty) {
      setError("请输入有效的验证码");
      return;
    }
    if (agree != true) {
      event = LoginEvent.notAgree;
      return;
    }
    runWithLoading(() async {
      final result = await useCase.emailLogin(email, _verifyCode);
      if (result.isSuccess) {
        // 登录成功
        event = LoginEvent.loginSuccess;
        return;
      }
      setError(result.msg ?? "登录失败");
    });
  }

  @action
  void updateAgree(bool? v) {
    agree = v ?? false;
  }

  @action
  void clearEvent() {
    event = null;
  }
}
