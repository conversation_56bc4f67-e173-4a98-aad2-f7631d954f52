import 'package:flutter/material.dart';
import 'package:tasks/models/user/login_type.dart';

class ChangeLoginMethod extends StatelessWidget {
  final LoginType value;
  final ValueChanged<LoginType> onChanged;

  const ChangeLoginMethod(
      {super.key, required this.value, required this.onChanged});

  bool get _isNormal => value == LoginType.normal;

  bool get _isEmail => value == LoginType.email;

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        TextButton(
          onPressed: () {
            onChanged(LoginType.normal);
          },
          child: Text(
            '密码登录',
            style: _isNormal
                ? textTheme.titleLarge!.copyWith(color: colorScheme.primary)
                : textTheme.bodyMedium,
          ),
        ),
        SizedBox(width: 4),
        TextButton(
          onPressed: () {
            onChanged(LoginType.email);
          },
          child: Text(
            '邮箱登录',
            style: _isEmail
                ? textTheme.titleLarge!.copyWith(color: colorScheme.primary)
                : textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }
}
