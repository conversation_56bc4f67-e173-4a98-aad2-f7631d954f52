import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/utils/reg_constans.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';
import 'package:tasks/widgets/textfield/verify_code_textfield.dart';

class EmailLoginInput extends StatelessWidget {
  final String? email;
  final ValueChanged<String> onEmailChange;
  final ValueChanged<String> onVerifyCodeChange;
  final Future<bool> Function() getVerifyCode;

  EmailLoginInput(
      {super.key,
      required this.onEmailChange,
      required this.onVerifyCodeChange,
      required this.getVerifyCode,
      this.email});

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      children: [
        // 账号输入框
        ClearInputTextField(
            hintText: l10n.accountEmailHint,
            icon: Icons.email,
            value: email,
            fillColor: colorScheme.surfaceContainer,
            keyboardType: TextInputType.emailAddress,
            filterPattern: RegFilterConstant.regEmail,
            textInputAction: TextInputAction.next,
            onChange: onEmailChange),
        SizedBox(
          height: 12,
        ),
        VerifyCodeTextField(
          hintText: l10n.accountVerifyCodeHint,
          icon: Icons.verified,
          onChange: onVerifyCodeChange,
          onGetCode: getVerifyCode,
          fillColor: colorScheme.surfaceContainer,
          filterPattern: RegFilterConstant.inputVerifyCode,
        ),
      ],
    );
  }
}
