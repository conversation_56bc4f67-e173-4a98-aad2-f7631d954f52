import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/utils/reg_constans.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';
import 'package:tasks/widgets/textfield/password_textfield.dart';

class NormalLogin extends StatelessWidget {
  final String? userName;
  final String? password;
  final ValueChanged<String> onUserNameChange;
  final ValueChanged<String> onPasswordChange;

  NormalLogin(
      {super.key,
      this.userName,
      this.password,
      required this.onUserNameChange,
      required this.onPasswordChange});

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      children: [
        // 账号输入框
        ClearInputTextField(
            hintText: l10n.loginUserNameTips,
            icon: Icons.person,
            keyboardType: TextInputType.emailAddress,
            filterPattern: RegFilterConstant.regUsername,
            textInputAction: TextInputAction.next,
            value: userName,
            fillColor: colorScheme.surfaceContainer,
            onChange: onUserNameChange),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 12,
        ),
        PasswordTextField(
            value: password,
            fillColor: colorScheme.surfaceContainer,
            hintText: l10n.loginPasswordTips,
            onChange: onPasswordChange),
      ],
    );
  }
}
