import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:fluwx/fluwx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/assets.gen.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/providers/user_store.dart';
import 'package:tasks/providers/wechat_state.dart';

class WechatLoginAction extends StatefulWidget {
  final bool isAgree;

  final VoidCallback onAgree;

  const WechatLoginAction(
      {super.key, required this.isAgree, required this.onAgree});

  @override
  State<WechatLoginAction> createState() => _WechatLoginActionState();
}

class _WechatLoginActionState extends State<WechatLoginAction> {
  WechatState get state => getIt.get();

  bool isWxInstall = false;

  @override
  void initState() {
    super.initState();
    // 事件监听
    reaction((_) => state.event, (r) {
      // 需要注册
      if (r == WechatEvent.needRegister) {
        showConfirm(context,
            content: "当前微信未绑定账号，确认将会注册账号！如您已经注册账号，请登录后在个人信息中绑定微信！",
            onConfirm: () {
          // 跳转注册页面，注册并绑定
          state.register();
        });
        return;
      }
      // 登录成功
      if (r == WechatEvent.loginSuccess) {
        // 刷新用户信息
        getIt.get<UserStore>().syncFetchUserInfo();
        context.pop();
        return;
      }
      // 注册成功
      if (r == WechatEvent.registerSuccess) {
        // 刷新用户信息
        getIt.get<UserStore>().syncFetchUserInfo();
        context.pop();
        return;
      }
    });
    // 判断微信是否安装
    initWechat();
  }

  /// 初始化微信
  Future<void> initWechat() async {
    final isInstall = await Fluwx().isWeChatInstalled;
    setState(() {
      isWxInstall = isInstall;
    });
  }

  @override
  Widget build(BuildContext context) {
    return StateWarp(
      store: state,
      child: Observer(builder: (context) {
        return Visibility(
          visible: isWxInstall,
          child: GestureDetector(
            onTap: () {
              // 未同意协议先同意协议
              if (!widget.isAgree) {
                final l10n = S.of(context);
                showConfirm(context, content: l10n.accountWechatLoginConfirm,
                    onConfirm: () {
                  widget.onAgree();
                  state.login();
                });
                return;
              }
              // 调用微信登录
              state.login();
            },
            child: Image.asset(
              Assets.images.iconWechat.path,
              width: 32,
              height: 32,
            ),
          ),
        );
      }),
    );
  }
}
