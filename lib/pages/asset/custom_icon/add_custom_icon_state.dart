import 'package:mobx/mobx.dart';
import 'package:tasks/data/icons/icons_repository.dart';
import 'package:tasks/data/icons/response/category_response.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/models/icon/category_entity.dart';
import 'package:tasks/models/icon/icon_entity.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:uuid/uuid.dart';

part 'add_custom_icon_state.g.dart';

class AddCustomIconState = _AddCustomIconState with _$AddCustomIconState;

abstract class _AddCustomIconState extends BaseStore with Store {
  IconsRepository repository = getIt.get();

  @observable
  CategoryIconEntity? categoryIconEntity;

  @observable
  bool isVip = true;

  @observable
  String iconName = "";

  @observable
  AppIcon? appIcon;

  _AddCustomIconState() {
    loadIcons();
    loadVip();
  }

  @action
  Future<void> loadIcons() async {
    final category = await repository.getCustomIcons();
    final icons = category.icons.where((i) => i.id != "-1").toList();
    categoryIconEntity = CategoryIconEntity(
      id: category.id,
      name: category.name,
      icons: icons,
      index: category.index,
    );
  }

  @action
  Future<void> loadVip() async {
    final vip = await UserRepo.isVip();
    isVip = vip;
  }

  @action
  void onIconNameChanged(String value) {
    iconName = value;
  }

  @action
  void onIconChanged(AppIcon? value) {
    appIcon = value;
  }

  @action
  Future<void> addIcon() async {
    // 判断数据有效性
    if (iconName.isEmpty) {
      setError("请输入图标名");
      return;
    }
    if (appIcon == null) {
      setError("请输入图标链接");
      return;
    }
    final iconEntity = IconItem(
      id: Uuid().v4(),
      index: 1,
      name: iconName,
      url: appIcon?.value,
      iconType: appIcon?.type ?? IconType.remote,
      requiredVip: true,
    );
    await repository.addIcon(iconEntity);
    await loadIcons();
  }

  @action
  Future<void> deleteIcon(IconEntity icon) async {
    await repository.deleteIcon(icon.id);
    await loadIcons();
  }
}
