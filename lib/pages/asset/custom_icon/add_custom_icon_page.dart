import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/modal/iconselect/widgets/icon_item_container.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/pages/asset/custom_icon/add_custom_icon_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/image_utils.dart';
import 'package:tasks/widgets/app_icon_widget.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/select/item_select_input.dart';

class AddCustomIconPage extends StatefulWidget {
  @override
  _AddCustomIconPageState createState() => _AddCustomIconPageState();
}

class _AddCustomIconPageState extends State<AddCustomIconPage> {
  late AddCustomIconState _state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    _state = AddCustomIconState();
  }

  @override
  Widget build(BuildContext context) {
    return StateWarp(
      store: _state,
      child: Scaffold(
        appBar: CommonAppBar(
          title: "自定义图标",
          actions: [
            Observer(builder: (context) {
              return TextButton(
                  onPressed: () {
                    _state.addIcon();
                  },
                  child: Text(l10n.commonAdd, style: textTheme.titleMedium));
            })
          ],
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Observer(builder: (context) {
                  return ItemSelectInput(
                    title: "输入图标名",
                    value: _state.iconName,
                    onValueChanged: _state.onIconNameChanged,
                  );
                }),
                SizedBox(height: 16.0),
                Observer(builder: (context) {
                  return ItemSelectInput(
                    title: "输入图片地址或选择图片",
                    value: _state.appIcon?.value ?? "",
                    onValueChanged: (v) {
                      _state.onIconChanged(AppIcon(
                        type: IconType.remote,
                        value: v,
                      ));
                    },
                    tail: InkWell(
                      onTap: _pickImage,
                      child: _state.appIcon != null
                          ? AppIconWidget(
                              icon: _state.appIcon!,
                              size: 24,
                            )
                          : Icon(Icons.image),
                    ),
                  );
                }),
                SizedBox(height: 16.0),
                Observer(builder: (context) {
                  return Visibility(
                      visible: _state.categoryIconEntity != null,
                      child: Observer(builder: (context) {
                        return Container(
                          decoration: BoxDecoration(
                            color: colorScheme.surfaceContainer,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconItemContainer(
                              isVip: true,
                              value: _state.categoryIconEntity!,
                              onSelected: (v) {
                                showConfirm(context, content: "删除当前图标?",
                                    onConfirm: () {
                                  _state.deleteIcon(v);
                                });
                              }),
                        );
                      }));
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _pickImage() async {
    AppIcon? icon = await ImageUtils.pickImage(context);
    if (icon != null) {
      _state.onIconChanged(icon);
    }
  }
}
