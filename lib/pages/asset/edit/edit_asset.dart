import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/modal/datepicker/period_date_picker.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/pages/asset/edit/asset_form_store.dart';
import 'package:tasks/pages/asset/edit/child/edit_price_method.dart';
import 'package:tasks/pages/asset/edit/edit_asset_state.dart';
import 'package:tasks/pages/asset/widgets/category_select.dart';
import 'package:tasks/pages/asset/widgets/buy_channel_select.dart';
import 'package:tasks/pages/asset/widgets/storage_location_select.dart';
import 'package:tasks/pages/asset/widgets/remark_display.dart';
import 'package:tasks/pages/asset/widgets/section_content.dart';
import 'package:tasks/pages/asset/widgets/select_date_item.dart';
import 'package:tasks/pages/asset/widgets/select_icon.dart';
import 'package:tasks/pages/asset/widgets/select_switch_item.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/glassmorphism_container.dart';

class AssetEditPage extends StatefulWidget {
  final String assetId; // 添加资产ID参数

  const AssetEditPage({Key? key, required this.assetId}) : super(key: key);

  @override
  _AssetEditPageState createState() => _AssetEditPageState();
}

class _AssetEditPageState extends State<AssetEditPage> {
  late final AssetFormStore formStore;
  late final EditAssetState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    formStore = AssetFormStore();
    state = EditAssetState(store: formStore, assetId: widget.assetId);

    // 监听事件
    reaction((_) => state.event, (e) {
      switch (e) {
        case EditAssetEvent.updateSuccess:
          context.pop();
          break;
        case EditAssetEvent.deleteSuccess:
          ToastUtils.success(context, l10n.homeDeleteSuccess);
          context.pop();
          break;
        case EditAssetEvent.updateSuccessAndTip:
          showConfirm(context, content: l10n.assetCacheTips, onConfirm: () {
            context.pushReplacement(Routers.cacheCenter);
          }, onCancel: () {
            context.pop();
          });
          break;
        default:
          break;
      }
    });
  }

  @override
  void dispose() {
    state.dispose(); // 释放资源
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StateWarp(
      store: formStore,
      child: Observer(builder: (context) {
        return PopScope(
          canPop: !state.isDataChanged,
          onPopInvokedWithResult: (canPop, didPop) async {
            if (canPop) {
              return;
            }
            // 提示用户返回拦截
            await showConfirm(context,
                content: l10n.assetEditRetentionTips,
                confirmText: l10n.saveAndExit, onCancel: () {
              context.pop();
            }, onConfirm: () {
              state.submitForm();
            });
          },
          child: Scaffold(
            backgroundColor: colorScheme.surface,
            appBar: CommonAppBar(
              title: l10n.assetEditAsset,
              actions: [
                Observer(builder: (context) {
                  return IconButton(
                      onPressed: () {
                        state.updateFavorite();
                      },
                      icon: formStore.isFavorite
                          ? Icon(
                              Icons.favorite,
                              color: colorScheme.primary,
                            )
                          : Icon(Icons.favorite_border));
                })
              ],
            ),
            body: SafeArea(
              child: Column(
                children: [
                  Expanded(
                    child: ListView(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      children: [
                        // 图标选择
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 16),
                          child: Center(
                            child: SelectIcon(
                                icon: formStore.selectedIcon,
                                onChange: (v) {
                                  formStore.setIcon(v);
                                }),
                          ),
                        ),
                        // 分类选择
                        CategorySelect(
                          categoryEntity: formStore.selectedCategory,
                          onChange: (v) {
                            formStore.setSelectedCategory(v.id);
                          },
                        ),

                        // 基本信息 section
                        SectionContentWidget(
                            title: l10n.assetBasicInfo,
                            children: [
                              // 资产名称
                              _buildTextField(
                                formStore.nameController,
                                formStore.onNameChanged,
                                placeholder:
                                    l10n.assetAssetNameInputPlaceholder,
                              ),
                              // 价格
                              _buildTextField(formStore.priceController,
                                  formStore.onPriceChanged,
                                  isNumber: true,
                                  placeholder: l10n.assetPriceInputPlaceholder),
                              // 购买日期
                              Observer(builder: (context) {
                                return SelectDateItem(
                                  title: l10n.assetPurchaseDate,
                                  date: formStore.purchaseDate,
                                  maxDate: DateTime.now(),
                                  onChange: (v) {
                                    formStore.onPurchaseDateChanged(v);
                                  },
                                );
                              }),
                            ]),
                        // 保修
                        SectionContentWidget(
                            title: l10n.assetWarrantyStatus,
                            enableDivider: false,
                            children: [
                              // 指定保修
                              Observer(builder: (context) {
                                return SelectSwitchItem(
                                  title: l10n.assetSetWarrantyStatus,
                                  value: formStore.showWarranty,
                                  onChanged: (v) {
                                    formStore.updateWarranty(v);
                                  },
                                );
                              }),
                              Visibility(
                                visible: formStore.showWarranty,
                                child: Observer(builder: (context) {
                                  return SelectDateItem(
                                    title: l10n.assetWarrantyExpirationDate,
                                    date: formStore.warrantyDate,
                                    minDate: formStore.purchaseDate,
                                    baseDate: formStore.purchaseDate,
                                    showPeriodButtons: true,
                                    periodOptions:
                                        DefaultPeriodOptions.defaultOptions,
                                    onChange: (v) {
                                      formStore.onWarrantyDateChanged(v);
                                    },
                                  );
                                }),
                              )
                            ]),
                        // 计价方式
                        EditPriceMethod(formStore: formStore),
                        // 服役状态 section
                        SectionContentWidget(
                            title: l10n.assetServiceStatus,
                            enableDivider: false,
                            children: [
                              // 服役状态开关
                              Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 4),
                                child: Row(
                                  children: [
                                    Text(
                                      l10n.assetServiceStatus,
                                      style: textTheme.titleMedium,
                                    ),
                                    Spacer(),
                                    Switch(
                                      value: formStore.isInService,
                                      onChanged: formStore.updateInService,
                                    ),
                                  ],
                                ),
                              ),
                              // 未服役时显示退役时间选择
                              Observer(builder: (context) {
                                final initDate = (formStore.retireDate
                                        .isBefore(formStore.purchaseDate))
                                    ? formStore.purchaseDate
                                    : formStore.retireDate;
                                return Visibility(
                                    visible: !formStore.isInService,
                                    child: SelectDateItem(
                                        title: l10n.assetRetirementDate,
                                        date: initDate,
                                        minDate: formStore.purchaseDate,
                                        onChange: (v) {
                                          formStore.updateRetireDate(
                                              v ?? DateTime.now());
                                        }));
                              }),
                              // 二手价格输入框
                              _buildTextField(formStore.resalePriceController,
                                  formStore.onResalePriceChanged,
                                  placeholder:
                                      l10n.assetSecondhandPricePlaceholder,
                                  isNumber: true)
                            ]),
                        SizedBox(
                          height: 12,
                        ),
                        Observer(builder: (context) {
                          return GestureDetector(
                            onTap: () async {
                              final result = await context.push(
                                Routers.extraFeesManage,
                                extra: {'extraFees': formStore.extraFees},
                              );
                              if (result != null &&
                                  result is List<ExtraFeesEntity>) {
                                formStore.onExtraFeesChanged(result);
                              }
                            },
                            child: Container(
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: colorScheme.surfaceContainer,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.receipt_long_outlined,
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                                  SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          l10n.assetAdditionalLabel,
                                          style: textTheme.titleMedium,
                                        ),
                                        SizedBox(height: 4),
                                        Text(
                                          formStore.extraFees.isEmpty
                                              ? l10n
                                                  .assetAdditionalItemsEmptyTips
                                              : l10n.assetAdditionalItemsCount(
                                                  formStore.extraFees.length),
                                          style: textTheme.bodyMedium?.copyWith(
                                            color: colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.chevron_right,
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                        // 备注信息
                        SectionContentWidget(
                          title: l10n.assetExtra,
                          enableDivider: true,
                          children: [
                            // 购买渠道
                            Observer(builder: (context) {
                              return BuyChannelSelect(
                                selectedChannelId:
                                    formStore.selectedBuyChannel?.id,
                                onChange: (channel) {
                                  formStore.setSelectedBuyChannel(channel);
                                },
                              );
                            }),
                            SizedBox(height: 12),
                            // 存放位置
                            Observer(builder: (context) {
                              return StorageLocationSelect(
                                selectedLocationId:
                                    formStore.selectedStorageLocation?.id,
                                onChange: (location) {
                                  formStore
                                      .setSelectedStorageLocation(location);
                                },
                              );
                            }),
                            Observer(builder: (context) {
                              return RemarkDisplay(
                                remark: formStore.remark,
                                assetName: formStore.name,
                                remarkHistory: formStore.getRemarkHistory(),
                                onRemarkChanged: (newRemark) {
                                  formStore.setRemark(newRemark);
                                },
                                onClearHistory: (clearedHistory) {
                                  formStore.clearRemarkHistory();
                                },
                                onDeleteRemark: (remarkId) {
                                  formStore.deleteRemarkFromHistory(remarkId);
                                },
                              );
                            }),
                          ],
                        ),
                        SizedBox(
                          height: 24,
                        ),
                      ],
                    ),
                  ),
                  GlassmorphismContainer(
                    gradientColors: [
                      colorScheme.surface,
                      colorScheme.surfaceBright
                    ],
                    borderColor: colorScheme.surface.withOpacity(0.3),
                    shadowColors: [
                      colorScheme.surface.withOpacity(0.3),
                      colorScheme.surface.withOpacity(0.4),
                    ],
                    borderRadius: 0,
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        // 删除按钮
                        Expanded(
                          child: OutlinedButton(
                              onPressed: () async {
                                await showConfirm(context,
                                    content: l10n.homeDeleteAssetConfirmation(
                                        state.store.asset?.name ?? ""),
                                    confirmText: l10n.delete, onConfirm: () {
                                  state.deleteAsset(state.assetId);
                                });
                              },
                              style: OutlinedButton.styleFrom(
                                foregroundColor: colorScheme.error,
                                side: BorderSide(color: colorScheme.error),
                              ),
                              child: Text(
                                l10n.delete,
                                style: textTheme.titleMedium!
                                    .copyWith(color: colorScheme.error),
                              )),
                        ),
                        SizedBox(
                          width: 8,
                        ),
                        Expanded(
                          child: FilledButton(
                            onPressed: () {
                              state.submitForm();
                            },
                            child: Text(l10n.commonSave,
                                style: textTheme.bodyMedium!
                                    .copyWith(color: colorScheme.onPrimary)),
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildTextField(
      TextEditingController controller, ValueChanged<String> onChanged,
      {bool isNumber = false, String? placeholder}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 12),
      child: TextField(
        controller: controller,
        keyboardType:
            isNumber ? TextInputType.numberWithOptions(decimal: true) : null,
        decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: textTheme.labelMedium,
            filled: false,
            contentPadding: EdgeInsets.zero,
            border: InputBorder.none),
        onChanged: onChanged,
        style: textTheme.bodyMedium,
      ),
    );
  }
}
