import 'package:bottom_picker/bottom_picker.dart';
import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset/asset_price_method.dart';
import 'package:tasks/widgets/select/item_select_form.dart';

class PriceMethodSelect extends StatefulWidget {
  const PriceMethodSelect(
      {super.key, required this.value, required this.onChange});

  final PriceMethod? value;
  final ValueChanged<PriceMethod> onChange;

  @override
  State<PriceMethodSelect> createState() => _PriceMethodSelectState();
}

class _PriceMethodSelectState extends State<PriceMethodSelect> {
  int index = 0;

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  S get l10n => S.of(context);

  List<PriceMethodEntity> items = PriceMethodEntity.values;

  @override
  void initState() {
    super.initState();
    setState(() {
      measureIndex();
    });
  }

  @override
  void didUpdateWidget(covariant PriceMethodSelect oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value) {
      measureIndex();
    }
  }

  void measureIndex() {
    final index = items.indexWhere((i) => i.method == widget.value);
    if (index != -1) {
      setState(() {
        this.index = index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedMethod = items[index];

    return ItemSelectForm(
        title: l10n.assetSelectPriceMethod,
        value: selectedMethod.label,
        onTap: showPriceMethod);
  }

  void showPriceMethod() {
    BottomPicker(
      items: [
        ...List.generate(items.length, (index) {
          final item = items[index];
          return Center(
            key: Key(index.toString()),
            child: Text(item.label),
          );
        })
      ],
      onChange: (item) {
        if (item is int) {
          index = item;
        }
      },
      selectedItemIndex: index,
      pickerTitle: SizedBox(
        height: 56,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // 关闭弹窗
              },
              child: Text(
                l10n.cancel,
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
            Text(
              l10n.assetSelectPriceMethod,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                widget.onChange(items[index].method);
                Navigator.pop(context); // 关闭弹窗
              },
              child: Text(
                l10n.confirm,
                style: TextStyle(
                  color: colorScheme.primary,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
      titleAlignment: Alignment.center,
      displaySubmitButton: false,
      displayCloseIcon: false,
      pickerTextStyle: TextStyle(
        color: Colors.black,
        fontWeight: FontWeight.bold,
      ),
    ).show(context);
  }
}
