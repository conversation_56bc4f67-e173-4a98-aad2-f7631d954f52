import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/utils/reg_constans.dart';

class UsageCountAction extends StatefulWidget {
  const UsageCountAction({super.key, this.value, required this.onChange});

  final int? value;

  final ValueChanged<int> onChange;

  @override
  State<UsageCountAction> createState() => _UsageCountActionState();
}

class _UsageCountActionState extends State<UsageCountAction> {
  TextEditingController controller = TextEditingController();

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    controller.text = widget.value?.toString() ?? "";
  }

  @override
  void didUpdateWidget(covariant UsageCountAction oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      controller.text = widget.value?.toString() ?? "";
    }
  }

  @override
  void dispose() {
    super.dispose();
    controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        children: [
          Text(
            l10n.assetTimesLabel,
            style: textTheme.titleMedium,
          ),
          SizedBox(
            width: 64,
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(3),
              child: TextField(
                controller: controller,
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.numberWithOptions(),
                style: textTheme.titleMedium,
                decoration: InputDecoration(
                  contentPadding: EdgeInsets.symmetric(vertical: 12),
                  hintText: l10n.assetTimesPlaceholder,
                  hintStyle: textTheme.bodyMedium,
                  hoverColor: colorScheme.primary,
                  prefixIcon: InkWell(
                      child: Icon(Icons.exposure_plus_1),
                      onTap: () {
                        widget.onChange((widget.value ?? 0) + 1);
                      }),
                  suffixIcon: IconButton(
                    onPressed: () {
                      if (widget.value == 0) {
                        return;
                      }
                      widget.onChange((widget.value ?? 0) - 1);
                    },
                    icon: const Icon(Icons.exposure_minus_1),
                  ),
                  filled: true,
                  fillColor: colorScheme.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
                onChanged: (v) {
                  if (v.isEmpty) {
                    widget.onChange(0);
                  } else {
                    widget.onChange(int.tryParse(v) ?? 0);
                  }
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegFilterConstant.intAmount)
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
