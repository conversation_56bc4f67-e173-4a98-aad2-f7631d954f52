import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset/cycle_price_value.dart';
import 'package:tasks/pages/asset/edit/widgets/cycle_type_select.dart';

class CyclePrice extends StatefulWidget {
  final CyclePriceValue value;

  final ValueChanged<CyclePriceValue> onChange;

  const CyclePrice({super.key, required this.value, required this.onChange});

  @override
  State<CyclePrice> createState() => _CyclePriceState();
}

class _CyclePriceState extends State<CyclePrice> {
  final countController = TextEditingController();

  S get l10n => S.of(context);

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  @override
  void initState() {
    super.initState();
    widget.onChange(widget.value);
    countController.text = widget.value.count.toString();
  }

  @override
  void didUpdateWidget(covariant CyclePrice oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value) {
      widget.onChange(widget.value);
      countController.text = widget.value.count.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CyclePriceTypeSelect(
          value: widget.value.type,
          onChange: (v) {
            widget.onChange(widget.value.copyWith(type: v));
          },
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 12),
          child: TextField(
            controller: countController,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            decoration: InputDecoration(
                hintText: l10n.assetCycleValuePlaceHolder,
                hintStyle: textTheme.labelMedium,
                filled: false,
                contentPadding: EdgeInsets.zero,
                border: InputBorder.none),
            onChanged: (v) {
              final value = int.tryParse(v);
              if (value == null) {
                return;
              }
              widget.onChange(widget.value.copyWith(count: int.tryParse(v)));
            },
            style: textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }
}
