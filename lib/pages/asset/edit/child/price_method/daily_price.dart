import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/pages/asset/edit/asset_form_store.dart';
import 'package:tasks/pages/asset/widgets/select_date_item.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';

class DailyPrice extends StatefulWidget {
  final AssetFormStore formStore;

  const DailyPrice({super.key, required this.formStore});

  @override
  State<DailyPrice> createState() => _DailyPriceState();
}

class _DailyPriceState extends State<DailyPrice> {
  S get l10n => S.of(context);

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  AssetFormStore get formStore => widget.formStore;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Observer(builder: (context) {
          return ClearInputTextField(
            value: formStore.dayPrice,
            hintText: l10n.assetDailyPricePlaceHolder,
            onChange: formStore.onDailyPriceChanged,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            fillColor: colorScheme.surfaceContainer,
          );
        }),
        Observer(builder: (context) {
          return SelectDateItem(
              title: l10n.assetExpectedRetirementDate,
              date: formStore.calculatedRetireDate,
              minDate: formStore.purchaseDate,
              showPeriodButtons: true,
              onChange: (v) {
                formStore.updateDailyRetireDate(v);
              });
        }),
      ],
    );
  }
}
