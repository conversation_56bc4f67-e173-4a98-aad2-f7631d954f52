import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset/usage_price_value.dart';
import 'package:tasks/pages/asset/edit/asset_form_store.dart';
import 'package:tasks/pages/asset/edit/widgets/usage_count_action.dart';
import 'package:tasks/utils/string_ext.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';

class UsageCount extends StatelessWidget {
  final AssetFormStore formStore;

  const UsageCount({super.key, required this.formStore});

  @override
  Widget build(BuildContext context) {
    S l10n = S.of(context);
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Column(
      children: [
        Observer(builder: (context) {
          return UsageCountAction(
            value: formStore.usagePriceValue?.count,
            onChange: (v) {
              final newValue = UsagePriceValue(
                  count: v, allCount: formStore.usagePriceValue?.allCount);
              formStore.onUsagePriceChanged(newValue);
            },
          );
        }),
        Observer(builder: (context) {
          return ClearInputTextField(
              value: formStore.usagePriceValue?.allCount?.toString(),
              hintText: l10n.assetUsageAllCount,
              fillColor: colorScheme.surfaceContainer,
              onChange: (v) {
                final newValue = UsagePriceValue(
                    count: formStore.usagePriceValue?.count,
                    allCount: v.toIntOrNull);
                formStore.onUsagePriceChanged(newValue);
              });
        })
      ],
    );
  }
}
