import 'package:flutter/material.dart';

class PriceMethodTips extends StatelessWidget {
  const PriceMethodTips({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.all(16),
      child: ListView(
        children: [
          const Text(
            '计价方式',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          const TipsWithSubList(title: '1、日均计价', subList: [
            '- 日均计价为默认计价规则，计价规则为资产购买日期（含附加费用）到当前日期（如果资产已退役，应该为退役时间）之间，按日计算价格',
            "- 指定日均时，会自动计算退役时间；残值计算规则为：（资产价格+附加费用）*（当前时间-购买时间）/（退役时间-购买时间）",
            "- 不指定日均时，会根据当前时间，计算日均；无残值",
            '- 已退役的资产，残值为二手价格',
          ]),
          const SizedBox(height: 12),
          const TipsWithSubList(title: '2、按使用次数计价', subList: [
            '- 按使用次数计价适用于消耗品，如化妆品。',
            '- 按次计价规则为（资产价格+附加费用）/使用次数',
          ]),
          const SizedBox(height: 12),
          const TipsWithSubList(title: '3、循环计价', subList: [
            '- 循环计价适用于订阅类产品，如月卡、年卡。',
            '- 循环计价总价格：价格*循环次数',
            "- 循环计价下，残值计算规制为当前循环剩余比例*资产价格"
          ]),
          const SizedBox(height: 12),
          const TipsWithSubList(title: '4、不计价', subList: [
            '- 不计价时，不计入总日均，但是计入总资产',
          ]),
        ],
      ),
    );
  }
}

class TipsWithSubList extends StatelessWidget {
  final String title;

  final List<String> subList;

  const TipsWithSubList(
      {super.key, required this.title, required this.subList});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: textTheme.titleMedium,
          ),
          if (subList.isNotEmpty)
            for (var subtitle in subList)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Text(
                  subtitle,
                  style: textTheme.bodyMedium,
                ),
              ),
        ],
      ),
    );
  }
}

void showPriceMethodTips(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    constraints: BoxConstraints(
      maxHeight: MediaQuery.of(context).size.height * 0.8, // 设置最大高度为屏幕高度的80%
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(16.0),
        topRight: Radius.circular(16.0),
      ),
    ),
    builder: (context) => PriceMethodTips(),
  );
}
