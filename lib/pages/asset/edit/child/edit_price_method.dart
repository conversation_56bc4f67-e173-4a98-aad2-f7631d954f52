import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset/asset_price_method.dart';
import 'package:tasks/models/asset/cycle_price_value.dart';
import 'package:tasks/pages/asset/edit/asset_form_store.dart';
import 'package:tasks/pages/asset/edit/child/price_method/cycle_price.dart';
import 'package:tasks/pages/asset/edit/child/price_method/daily_price.dart';
import 'package:tasks/pages/asset/edit/child/price_method/usage_count.dart';
import 'package:tasks/pages/asset/edit/child/price_method_tips.dart';
import 'package:tasks/pages/asset/edit/widgets/price_method_select.dart';
import 'package:tasks/pages/asset/widgets/section_content.dart';

class EditPriceMethod extends StatefulWidget {
  final AssetFormStore formStore;

  const EditPriceMethod({super.key, required this.formStore});

  @override
  State<EditPriceMethod> createState() => _EditPriceMethodState();
}

class _EditPriceMethodState extends State<EditPriceMethod> {
  S get l10n => S.of(context);

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  AssetFormStore get formStore => widget.formStore;

  @override
  Widget build(BuildContext context) {
    return SectionContentWidget(
        title: l10n.assetSelectPricingMethod,
        enableDivider: false,
        titleWidget: InkWell(
          onTap: showTips,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Icon(
              Icons.info_outline,
              color: textTheme.bodyMedium!.color,
              size: 16,
            ),
          ),
        ),
        children: [
          Observer(builder: (context) {
            return PriceMethodSelect(
              value: formStore.priceMethod,
              onChange: (v) {
                formStore.onPriceMethodChanged(v);
              },
            );
          }),
          // 默认日均
          Observer(builder: (context) {
            return Visibility(
                visible: formStore.priceMethod == PriceMethod.defaultPrice,
                child: DailyPrice(formStore: formStore));
          }),
          // 次数
          Observer(builder: (context) {
            return Visibility(
              visible: formStore.priceMethod == PriceMethod.useCount,
              child: UsageCount(formStore: formStore),
            );
          }),
          // 循环计价
          Observer(builder: (context) {
            return Visibility(
              visible: formStore.priceMethod == PriceMethod.cyclePrice,
              child: CyclePrice(
                value: formStore.cyclePriceValue ??
                    CyclePriceValue(count: 1, type: CyclePriceType.month),
                onChange: (v) {
                  formStore.onCyclePriceChanged(v);
                },
              ),
            );
          }),
          // 指定时间
        ]);
  }

  void showTips() {
    showPriceMethodTips(context);
  }
}
