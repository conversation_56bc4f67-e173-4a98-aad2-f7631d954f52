import 'package:mobx/mobx.dart';
import 'package:tasks/data/asset/asset_local_data_source.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/pages/asset/edit/asset_form_store.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/asset_store.dart';
import 'package:tasks/utils/date_utils.dart';

part 'edit_asset_state.g.dart';

class EditAssetState = _EditAssetState with _$EditAssetState;

enum EditAssetEvent { updateSuccess, updateSuccessAndTip, deleteSuccess }

abstract class _EditAssetState extends BaseStore with Store {
  AssetRepo repo = getIt.get();
  AssetLocalDataSource _dataSource = getIt.get();

  AssetStore assetStore = getIt.get();

  String assetId;

  AssetFormStore store;

  @computed
  String get purchaseDateFormat =>
      DateFormatUtils.formatDate(store.purchaseDate);

  @computed
  String get retireDateFormat => DateFormatUtils.formatDate(store.retireDate);

  @observable
  EditAssetEvent? event;

  @computed
  bool get isDataChanged {
    final asset = store.asset;
    final newAsset = store.measureAssetModel;
    return asset != null && !newAsset.isValueEqual(asset);
  }

  _EditAssetState({required this.store, required this.assetId}) : super() {
    _loadAssetData();
  }

  // 加载资产数据
  @action
  Future<void> _loadAssetData() async {
    final asset = await _dataSource.getAssetById(assetId);
    if (asset == null) return;
    store.init(asset);
  }

  @action
  Future<void> submitForm() async {
    final asset = store.validate(assetId);
    if (!asset) return;
    await repo.updateAssetInfo(store.measureAssetModel);
    assetStore.fetchAssets();
    // 未登录时不提示备份
    if (!UserRepo.isLogin()) {
      event = EditAssetEvent.updateSuccess;
      return;
    }
    final tips = repo.checkNeedCacheTips();

    event = tips
        ? EditAssetEvent.updateSuccessAndTip
        : EditAssetEvent.updateSuccess;
  }

  @action
  Future<void> deleteAsset(String id) async {
    await repo.deleteAsset(id);
    assetStore.fetchAssets();
    event = EditAssetEvent.deleteSuccess;
  }

  @action
  Future<void> updateFavorite() async {
    if (store.asset == null) return;
    // 更新收藏状态
    final isFavorite = !store.isFavorite;
    store.onFavoriteChange(isFavorite);
    await repo.updateAssetInfo(store.asset!.copyWith(isFavorite: isFavorite));
    assetStore.fetchAssets();
  }

  @action
  void clearEvent() {
    event = null;
  }

  void dispose() {
    store.dispose();
  }
}
