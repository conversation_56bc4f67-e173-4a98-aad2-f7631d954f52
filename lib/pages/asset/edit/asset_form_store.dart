import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/data/buy_channel/buy_channel_local_data_source.dart';
import 'package:tasks/data/storage_location/storage_location_local_data_source.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/asset/asset_price_method.dart';
import 'package:tasks/models/asset/cycle_price_value.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/models/asset/usage_price_value.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/buy_channel.dart';
import 'package:tasks/models/storage_location.dart';
import 'package:tasks/models/asset_remark.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/utils/date_utils.dart';
import 'package:tasks/utils/string_ext.dart';
import 'package:uuid/uuid.dart';

part 'asset_form_store.g.dart';

class AssetFormStore = _AssetFormStore with _$AssetFormStore;

abstract class _AssetFormStore extends BaseStore with Store {
  CategoryLocalDataSource _categoryDataSource = getIt.get();
  BuyChannelLocalDataSource _buyChannelDataSource = getIt.get();
  StorageLocationLocalDataSource _storageLocationDataSource = getIt.get();
  AssetRepo repo = getIt.get();

  Asset? asset;

  final nameController = TextEditingController();
  final priceController = TextEditingController();

  final resalePriceController = TextEditingController();
  final buyChannelController = TextEditingController();
  final storageLocationController = TextEditingController();
  final remarkController = TextEditingController();

  @observable
  AppIcon? selectedIcon;

  @observable
  CategoryEntity? selectedCategory;

  @observable
  BuyChannelEntity? selectedBuyChannel;

  @observable
  StorageLocationEntity? selectedStorageLocation;

// 基本信息---------
  @observable
  String name = "";

  @observable
  String price = '';

  @observable
  DateTime purchaseDate = DateTime.now();

// 保修状态----------
  @observable
  bool showWarranty = false;

  @observable
  DateTime warrantyDate = DateTime.now();

// 计价----------
  @observable
  PriceMethod priceMethod = PriceMethod.defaultPrice;

  @observable
  UsagePriceValue? usagePriceValue;

  @observable
  CyclePriceValue? cyclePriceValue;

// 扩展信息----------
  @observable
  String buyChannel = "";

  @observable
  String storageLocation = "";

  @observable
  String dayPrice = '';

  @observable
  bool isInService = true;

  @observable
  DateTime retireDate = DateTime.now();

  @observable
  String resalePrice = "";

  @observable
  List<ExtraFeesEntity> extraFees = [];

  @observable
  String remark = "";

// 收藏
  @observable
  bool isFavorite = false;

  @computed
  DateTime? get calculatedRetireDate {
    try {
      double amount = repo.measureAmount(price, extraFees);
      final dailyPrice = dayPrice.toDoubleOrNull ?? 0;
      if (amount <= 0 || dailyPrice <= 0) return null;
      if (dailyPrice > amount) return DateTime.now();
      final days = (amount / dailyPrice).ceil();
      return purchaseDate.add(Duration(days: days - 1));
    } catch (e) {
      return null;
    }
  }

  @computed
  String get calculatedRetireDateFormat =>
      DateFormatUtils.formatDate(calculatedRetireDate);

  /// 通过当前表单数据生成资产对象
  @computed
  Asset get measureAssetModel {
    final isSpecificDayPrice =
        (priceMethod == PriceMethod.defaultPrice) && dayPrice.isNotEmpty;
    return Asset(
        id: asset?.id ?? Uuid().v1(),
        categoryId: selectedCategory?.id,
        name: name,
        icon: selectedIcon,
        price: price.toDoubleOrZero,
        purchaseDate: purchaseDate,
        warrantyDate: showWarranty ? warrantyDate : null,
        priceMethod: priceMethod,
        usagePriceValue: usagePriceValue,
        cyclePriceValue: cyclePriceValue,
        isInService: isInService,
        resalePrice: resalePrice.toDoubleOrNull,
        retireDate: retireDate,
        dailyPrice: isSpecificDayPrice ? dayPrice.toDoubleOrNull : null,
        isFavorite: isFavorite,
        extraFees: extraFees.where((i) => i.valid).toList(),
        buyChannel: buyChannel,
        storageLocation: storageLocation,
        remark: remark);
  }

  Future<void> init(Asset asset) async {
    this.asset = asset;
    // 图标
    selectedIcon = asset.icon;
    // 加载分类信息
    if (asset.categoryId != null) setSelectedCategory(asset.categoryId!);
    // 资产基本信息
    nameController.text = asset.name;
    onNameChanged(asset.name);
    priceController.text = asset.price.toString();
    onPriceChanged(asset.price.toString());
    purchaseDate = asset.purchaseDate;
    // 保修
    showWarranty = asset.warrantyDate != null;
    warrantyDate = asset.warrantyDate ?? DateTime.now();
    // 计价相关
    runInAction(() {
      priceMethod = asset.priceMethod ?? PriceMethod.defaultPrice;
    });
    // 日均计价
    if (priceMethod == PriceMethod.defaultPrice) {
      onDailyPriceChanged(asset.dailyPrice?.toString() ?? "");
    }
    // 使用次数计价
    if (priceMethod == PriceMethod.useCount) {
      usagePriceValue =
          asset.usagePriceValue ?? UsagePriceValue(count: asset.usageCount);
    }
    // 循环计价
    if (priceMethod == PriceMethod.cyclePrice) {
      onCyclePriceChanged(asset.cyclePriceValue);
    }
    // 服役状态
    isInService = asset.isInService;
    retireDate = asset.retireDate ?? DateTime.now();
    final resalePrice = asset.resalePrice;
    // 有效数据才回显
    if (resalePrice != null && resalePrice != 0.0) {
      resalePriceController.text = asset.resalePrice?.toString() ?? "";
    }
    onResalePriceChanged(asset.resalePrice?.toString() ?? "");
    // 额外费用
    extraFees = asset.extraFees;
    // 额外信息
    buyChannelController.text = asset.buyChannel ?? "";
    onBuyChannelChanged(asset.buyChannel ?? "");
    // 设置选中的购买渠道（使用Asset实体的方法处理历史数据）
    selectedBuyChannel = asset.getBuyChannelEntity();

    // 设置选中的存放位置（使用Asset实体的方法处理历史数据）
    selectedStorageLocation = asset.getStorageLocationEntity();
    storageLocationController.text = asset.storageLocation ?? "";
    onStorageLocationChanged(asset.storageLocation ?? "");
    // 使用新的备注实体方法，兼容旧数据
    final currentRemark = asset.currentRemarkContent ?? "";
    remarkController.text = currentRemark;
    onRemarkChanged(currentRemark);
    isFavorite = asset.isFavorite;
  }

  @action
  void setIcon(AppIcon icon) {
    selectedIcon = icon;
  }

  @action
  void setSelectedCategory(String categoryId) {
    selectedCategory = _categoryDataSource.getCategory(categoryId);
  }

  @action
  void onNameChanged(String value) {
    name = value;
  }

  @action
  void onPriceChanged(String value) {
    price = value;
  }

  @action
  void onPurchaseDateChanged(DateTime? value) {
    if (value == null) return;
    purchaseDate = value;
  }

  @action
  void onPriceMethodChanged(PriceMethod value) {
    priceMethod = value;
  }

  @action
  void onCyclePriceChanged(CyclePriceValue? value) {
    cyclePriceValue = value;
  }

  @action
  void onBuyChannelChanged(String value) {
    buyChannel = value;
  }

  @action
  void setSelectedBuyChannel(BuyChannelEntity? channel) {
    selectedBuyChannel = channel;
    buyChannel = channel?.name ?? "";
    buyChannelController.text = buyChannel;
  }

  @action
  void setSelectedStorageLocation(StorageLocationEntity? location) {
    selectedStorageLocation = location;
    storageLocation = location?.name ?? "";
    storageLocationController.text = storageLocation;
  }

  @action
  void setRemark(String? newRemark) {
    final newContent = newRemark ?? "";
    final currentContent = asset?.currentRemarkContent ?? "";

    // 如果新备注不为空且与当前备注不同，则添加到历史记录
    if (newContent.isNotEmpty && newContent != currentContent) {
      _addToRemarkHistory(newContent);
    }

    remark = newContent;
    remarkController.text = remark;
  }

  void _addToRemarkHistory(String newContent) {
    if (asset == null) return;

    // 获取当前历史记录
    List<AssetRemarkEntity> history = List.from(asset!.currentRemarkHistory);

    // 如果新备注已存在于历史记录中，先移除它
    history.removeWhere((entity) => entity.content == newContent);

    // 创建新的备注实体并添加到列表开头
    final newEntity = AssetRemarkEntity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: newContent,
      createdAt: DateTime.now(),
    );
    history.insert(0, newEntity);

    // 限制历史记录数量（最多保留10条）
    if (history.length > 10) {
      history = history.take(10).toList();
    }

    // 更新当前备注实体
    final currentEntity = AssetRemarkEntity(
      id: asset!.remarkEntity?.id ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      content: newContent,
      createdAt: asset!.remarkEntity?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // 更新资产的备注数据
    asset = asset!.copyWith(
      remarkEntity: currentEntity,
      remarkHistoryEntities: history,
      // 清空旧的字符串字段，完成迁移
      remark: null,
      remarkHistory: null,
    );
  }

  List<AssetRemarkEntity> getRemarkHistory() {
    return asset?.currentRemarkHistory ?? [];
  }

  @action
  void clearRemarkHistory() {
    if (asset != null) {
      asset = asset!.copyWith(remarkHistoryEntities: []);
    }
  }

  @action
  void deleteRemarkFromHistory(String remarkId) {
    if (asset == null) return;

    List<AssetRemarkEntity> history = List.from(asset!.currentRemarkHistory);
    history.removeWhere((entity) => entity.id == remarkId);

    asset = asset!.copyWith(remarkHistoryEntities: history);
  }

  @action
  void onStorageLocationChanged(String value) {
    storageLocation = value;
  }

  @action
  void updateRetireDate(DateTime retireDate) {
    this.retireDate = retireDate;
  }

  @action
  void updateWarranty(bool flag) {
    if (flag) {
      warrantyDate = purchaseDate.copyWith(year: purchaseDate.year + 3);
    }
    showWarranty = flag;
  }

  @action
  void onWarrantyDateChanged(DateTime? value) {
    if (value == null) return;
    warrantyDate = value;
  }

  @action
  void onDailyPriceChanged(String value) {
    dayPrice = value;
  }

  @action
  void updateDailyRetireDate(DateTime? dailyRetireDate) {
    // 当前预计退役时间为空时，清空日均
    if (dailyRetireDate == null) {
      dayPrice = "";
      print('dayPrice:$dayPrice');
      return;
    }
    final days =
        AppDateUtils.daysLength(purchaseDate, dailyRetireDate, inclusive: true);
    // 通过预计退役时间计算出日均
    final measureDailyPrice =
        (measureAssetModel.measureAmount) / days.toDouble();
    dayPrice = measureDailyPrice.toString();
  }

  @action
  void onUsagePriceChanged(UsagePriceValue? value) {
    usagePriceValue = value;
  }

  @action
  void updateInService(bool flag) {
    isInService = flag;
  }

  @action
  void onResalePriceChanged(String value) {
    resalePrice = value;
  }

  @action
  void addExtraFees(ExtraFeesEntity extra) {
    final newList = extraFees.toList();
    newList.add(extra);
    extraFees = newList;
  }

  @action
  void removeExtraFees(ExtraFeesEntity entity) {
    // 调用父组件传递的回调函数
    final newList = extraFees.where((i) => i.id != entity.id).toList();
    extraFees = newList;
  }

  @action
  void onExtraFeesChanged(List<ExtraFeesEntity> values) {
    extraFees = values;
  }

  @action
  void onRemarkChanged(String value) {
    remark = value;
  }

  @action
  void calculateInService() {
    if (calculatedRetireDate?.isAfter(purchaseDate) ?? false) {
      isInService = true;
    }
  }

  @action
  void onFavoriteChange(bool isFavorite) {
    this.isFavorite = isFavorite;
  }

  bool validate(String? assetId) {
    if (name.isNullOrEmpty) {
      setError(S.current.assetAssetNameInputPlaceholder);
      return false;
    }
    if (price.isNullOrEmpty) {
      setError(S.current.assetPriceInputPlaceholder);
      return false;
    }
    if (price.toDoubleOrNull == null) {
      setError(S.current.assetDailyPriceValid);
      return false;
    }
    if (resalePrice.isNotEmpty && resalePrice.toDoubleOrNull == null) {
      setError(S.current.assetSecondhandPriceValid);
      return false;
    }
    // 判断计价方式为日均
    final isSpecificDayPrice =
        (priceMethod == PriceMethod.defaultPrice) && dayPrice.isNotEmpty;
    if (isSpecificDayPrice && dayPrice.toDoubleOrNull == null) {
      setError(S.current.assetDailyPriceValid);
      return false;
    }
    // 校验使用次数
    if (priceMethod == PriceMethod.useCount) {
      if ((usagePriceValue?.count ?? 0) == 0) {
        setError(S.current.assetUsageCountValid);
        return false;
      }
    }
    // 校验循环计价
    if (priceMethod == PriceMethod.cyclePrice) {
      if (((cyclePriceValue?.count ?? 0) == 0)) {
        setError("请输入有效的循环计价量");
        return false;
      }
    }

    return true;
  }

  void dispose() {
    nameController.dispose();
    priceController.dispose();
    resalePriceController.dispose();
    remarkController.dispose();
  }
}
