import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/models/asset_remark.dart';

class AssetRemarkEditPage extends StatefulWidget {
  final String? initialRemark;
  final String? assetName;
  final List<AssetRemarkEntity>? remarkHistory;
  final Function(List<AssetRemarkEntity>)? onClearHistory;
  final Function(String)? onDeleteRemark;

  const AssetRemarkEditPage({
    Key? key,
    this.initialRemark,
    this.assetName,
    this.remarkHistory,
    this.onClearHistory,
    this.onDeleteRemark,
  }) : super(key: key);

  @override
  _AssetRemarkEditPageState createState() => _AssetRemarkEditPageState();
}

class _AssetRemarkEditPageState extends State<AssetRemarkEditPage> {
  late TextEditingController _remarkController;
  final FocusNode _focusNode = FocusNode();
  List<AssetRemarkEntity> _remarkHistory = [];

  S get l10n => S.of(context);

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    super.initState();
    _remarkController = TextEditingController(text: widget.initialRemark ?? '');
    _loadRemarkHistory();

    // 自动聚焦到输入框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _remarkController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _loadRemarkHistory() {
    // 使用传入的真实历史数据
    _remarkHistory = List.from(widget.remarkHistory ?? []);
  }

  void _saveRemark() {
    final remark = _remarkController.text.trim();
    Navigator.of(context).pop(remark);
  }

  void _clearRemark() {
    _remarkController.clear();
  }

  void _selectHistoryRemark(String content) {
    _remarkController.text = content;
    _remarkController.selection = TextSelection.fromPosition(
      TextPosition(offset: content.length),
    );
  }

  void _clearRemarkHistory() {
    setState(() {
      _remarkHistory.clear();
    });
    if (widget.onClearHistory != null) {
      widget.onClearHistory!([]);
    }
  }

  void _deleteRemarkFromHistory(String remarkId) {
    setState(() {
      _remarkHistory.removeWhere((entity) => entity.id == remarkId);
    });
    if (widget.onDeleteRemark != null) {
      widget.onDeleteRemark!(remarkId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: CommonAppBar(
        backgroundColor: colorScheme.surface,
        title: l10n.assetRemarkEdit,
        actions: [
          TextButton(
            onPressed: _saveRemark,
            child: Text(
              l10n.commonSave,
              style: TextStyle(color: colorScheme.primary),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 输入框标题和操作按钮
            Row(
              children: [
                Text(
                  l10n.assetRemarkLabel,
                  style: textTheme.titleMedium,
                ),
                Spacer(),
              ],
            ),
            SizedBox(height: 12),

            // 固定高度的多行输入框
            Container(
              height: 200, // 固定高度
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              child: Stack(
                children: [
                  TextField(
                    controller: _remarkController,
                    focusNode: _focusNode,
                    maxLines: null,
                    expands: true,
                    textAlignVertical: TextAlignVertical.top,
                    onChanged: (value) {
                      setState(() {}); // 更新文本长度指示器
                    },
                    decoration: InputDecoration(
                      hintText: l10n.assetRemarkPlaceholder,
                      border: InputBorder.none,
                      contentPadding:
                          EdgeInsets.fromLTRB(16, 16, 16, 40), // 为底部按钮留出空间
                      hintStyle: TextStyle(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                    style: textTheme.bodyMedium,
                  ),
                  // 底部指示器和按钮
                  Positioned(
                    left: 16,
                    right: 16,
                    bottom: 8,
                    child: Row(
                      children: [
                        // 文本长度指示器
                        Text(
                          '${_remarkController.text.length}',
                          style: textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                        Spacer(),
                        // 清空按钮
                        if (_remarkController.text.isNotEmpty)
                          InkWell(
                            onTap: _clearRemark,
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              child: Text(
                                l10n.commonClear,
                                style: textTheme.bodySmall?.copyWith(
                                  color: colorScheme.error,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // 历史备注区域
            if (_remarkHistory.isNotEmpty) ...[
              SizedBox(height: 24),
              Row(
                children: [
                  Text(
                    l10n.assetRemarkHistory,
                    style: textTheme.titleMedium,
                  ),
                  Spacer(),
                  TextButton(
                    onPressed: _clearRemarkHistory,
                    child: Text(
                      l10n.commonClear,
                      style: textTheme.bodyMedium
                          ?.copyWith(color: colorScheme.error),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Expanded(
                child: ListView.separated(
                  itemCount: _remarkHistory.length,
                  separatorBuilder: (context, index) => SizedBox(height: 8),
                  itemBuilder: (context, index) {
                    final remarkEntity = _remarkHistory[index];
                    return InkWell(
                      onTap: () => _selectHistoryRemark(remarkEntity.content),
                      child: Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: colorScheme.surfaceContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                remarkEntity.content,
                                style: textTheme.bodyMedium,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            SizedBox(width: 8),
                            // 删除按钮
                            InkWell(
                              onTap: () =>
                                  _deleteRemarkFromHistory(remarkEntity.id),
                              child: Container(
                                padding: EdgeInsets.all(4),
                                child: Icon(
                                  Icons.delete_outline,
                                  size: 18,
                                  color: colorScheme.error,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
