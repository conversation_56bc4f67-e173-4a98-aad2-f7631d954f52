import 'package:mobx/mobx.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/pages/base_store.dart';

part 'extra_fees_manage_state.g.dart';

class ExtraFeesManageState = _ExtraFeesManageState with _$ExtraFeesManageState;

abstract class _ExtraFeesManageState extends BaseStore with Store {
  final List<ExtraFeesEntity> initialExtraFees;

  _ExtraFeesManageState(this.initialExtraFees) {
    extraFees = ObservableList.of(initialExtraFees);
  }

  @observable
  ObservableList<ExtraFeesEntity> extraFees = ObservableList<ExtraFeesEntity>();

  @action
  void addExtraFees(ExtraFeesEntity extra) {
    extraFees.add(extra);
  }

  @action
  void updateExtraFees(ExtraFeesEntity updatedExtra) {
    final index = extraFees.indexWhere((item) => item.id == updatedExtra.id);
    if (index != -1) {
      extraFees[index] = updatedExtra;
    }
  }

  @action
  void removeExtraFees(ExtraFeesEntity extra) {
    extraFees.removeWhere((item) => item.id == extra.id);
  }

  @action
  void onExtraFeesChanged(List<ExtraFeesEntity> newList) {
    extraFees.clear();
    extraFees.addAll(newList);
  }

  /// 检查是否有变更
  bool get hasChanges {
    if (extraFees.length != initialExtraFees.length) {
      return true;
    }

    for (int i = 0; i < extraFees.length; i++) {
      final current = extraFees[i];
      final initial = initialExtraFees.firstWhere(
        (item) => item.id == current.id,
        orElse: () => ExtraFeesEntity(), // 新添加的项目
      );

      if (initial.id.isEmpty || !current.isValueEqual(initial)) {
        return true;
      }
    }

    return false;
  }

  /// 获取当前的附加费用列表
  List<ExtraFeesEntity> getCurrentExtraFees() {
    return extraFees.toList();
  }
}
