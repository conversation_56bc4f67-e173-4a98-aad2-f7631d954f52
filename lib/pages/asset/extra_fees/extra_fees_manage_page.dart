import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/modal/extra_fees/edit_extra_fess_modal.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/pages/asset/extra_fees/extra_fees_manage_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/double_ext.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/glassmorphism_container.dart';

class DateGroup {
  final String dateKey;
  final DateTime date;
  final List<ExtraFeesEntity> fees;
  final double incomeTotal;
  final double expenseTotal;

  DateGroup({
    required this.dateKey,
    required this.date,
    required this.fees,
    required this.incomeTotal,
    required this.expenseTotal,
  });
}

class ExtraFeesManagePage extends StatefulWidget {
  final List<ExtraFeesEntity> extraFees;

  const ExtraFeesManagePage({
    Key? key,
    required this.extraFees,
  }) : super(key: key);

  @override
  _ExtraFeesManagePageState createState() => _ExtraFeesManagePageState();
}

class _ExtraFeesManagePageState extends State<ExtraFeesManagePage> {
  late ExtraFeesManageState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;
  TextTheme get textTheme => Theme.of(context).textTheme;
  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = ExtraFeesManageState(widget.extraFees);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final shouldPop = await _onWillPop();
        if (shouldPop && context.mounted) {
          context.pop();
        }
      },
      child: Scaffold(
        backgroundColor: colorScheme.surface,
        appBar: CommonAppBar(
          title: l10n.assetAdditionalManageTitle,
          actions: [
            TextButton(
              onPressed: _onSave,
              child: Text(
                l10n.commonSave,
                style: TextStyle(color: colorScheme.primary),
              ),
            ),
          ],
        ),
        body: StateWarp(
          store: state,
          child: Stack(
            children: [
              // 主要内容
              Column(
                children: [
                  // 提示信息区域
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16),
                    child: GlassmorphismContainer(
                      gradientColors: [
                        colorScheme.surfaceContainer.withValues(alpha: 0.8),
                        colorScheme.surfaceContainer.withValues(alpha: 0.6),
                      ],
                      borderColor: colorScheme.outline.withValues(alpha: 0.2),
                      shadowColors: [
                        colorScheme.shadow.withValues(alpha: 0.1),
                      ],
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: colorScheme.onSurfaceVariant,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                l10n.assetAdditionalManageTips,
                                style: textTheme.bodyMedium?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // 附加费用列表
                  Expanded(
                    child: Observer(
                      builder: (context) {
                        if (state.extraFees.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.receipt_long_outlined,
                                  size: 64,
                                  color: colorScheme.onSurfaceVariant
                                      .withValues(alpha: 0.5),
                                ),
                                SizedBox(height: 16),
                                Text(
                                  l10n.assetAdditionalItemsEmptyTips,
                                  style: textTheme.bodyLarge?.copyWith(
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }

                        return _buildAccountingStyleList();
                      },
                    ),
                  ),
                ],
              ),

              // 右下角浮动添加按钮
              Positioned(
                right: 16,
                bottom: 16,
                child: FloatingActionButton(
                  onPressed: _onAddExtraFees,
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  child: Icon(Icons.add),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccountingStyleList() {
    // 按日期分组
    final groupedFees = _groupFeesByDate();

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16),
      itemCount: groupedFees.length,
      itemBuilder: (context, index) {
        final dateGroup = groupedFees[index];
        return _buildDateGroup(dateGroup);
      },
    );
  }

  List<DateGroup> _groupFeesByDate() {
    final Map<String, List<ExtraFeesEntity>> grouped = {};

    for (final fee in state.extraFees) {
      final dateKey = _formatDateKey(fee.createAt);
      if (!grouped.containsKey(dateKey)) {
        grouped[dateKey] = [];
      }
      grouped[dateKey]!.add(fee);
    }

    // 按日期排序（最新的在前）
    final sortedKeys = grouped.keys.toList()..sort((a, b) => b.compareTo(a));

    return sortedKeys.map((key) {
      final fees = grouped[key]!;
      final incomeTotal = fees
          .where((f) => !f.paid)
          .fold(0.0, (sum, f) => sum + f.amount.abs());
      final expenseTotal =
          fees.where((f) => f.paid).fold(0.0, (sum, f) => sum + f.amount.abs());

      return DateGroup(
        dateKey: key,
        date: fees.first.createAt,
        fees: fees,
        incomeTotal: incomeTotal,
        expenseTotal: expenseTotal,
      );
    }).toList();
  }

  String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Widget _buildDateGroup(DateGroup group) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      child: GlassmorphismContainer(
        gradientColors: [
          colorScheme.surfaceContainer.withValues(alpha: 0.8),
          colorScheme.surfaceContainer.withValues(alpha: 0.6),
        ],
        borderColor: colorScheme.outline.withValues(alpha: 0.2),
        shadowColors: [
          colorScheme.shadow.withValues(alpha: 0.1),
        ],
        child: Column(
          children: [
            _buildDateHeader(group),
            ...group.fees.map((fee) => _buildAccountingFeeItem(fee)),
            SizedBox(height: 12),
            _buildIncludeInTotalSection(group.fees),
          ],
        ),
      ),
    );
  }

  Widget _buildDateHeader(DateGroup group) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _formatDisplayDate(group.date),
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                _formatWeekday(group.date),
                style: textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          Spacer(),
          if (group.incomeTotal > 0) ...[
            Text(
              '${l10n.assetAdditionalItemsIncome}：',
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              group.incomeTotal.toStringAsFixed(0),
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.primary,
              ),
            ),
            SizedBox(width: 16),
          ],
          if (group.expenseTotal > 0) ...[
            Text(
              '${l10n.assetAdditionalItemsConsume}：',
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              group.expenseTotal.toStringAsFixed(0),
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.error,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatDisplayDate(DateTime date) {
    return '${date.month.toString().padLeft(2, '0')}月${date.day.toString().padLeft(2, '0')}日';
  }

  String _formatWeekday(DateTime date) {
    const weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
    return weekdays[date.weekday - 1];
  }

  Widget _buildAccountingFeeItem(ExtraFeesEntity fee) {
    return InkWell(
      onTap: () => _onEditFee(fee),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // 左侧图标
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getFeeIconColor(fee),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getFeeIcon(fee),
                color: Colors.white,
                size: 20,
              ),
            ),
            SizedBox(width: 12),
            // 中间内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          fee.name,
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // 计入总价值标识
                      if (fee.includeInTotal)
                        Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            l10n.assetAdditionalIncludeInTotalShort,
                            style: textTheme.labelSmall?.copyWith(
                              color: colorScheme.primary,
                              fontSize: 10,
                            ),
                          ),
                        ),
                    ],
                  ),
                  if (fee.note.isNotEmpty) ...[
                    SizedBox(height: 2),
                    Text(
                      fee.note,
                      style: textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            SizedBox(width: 12),
            // 右侧金额
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  fee.paid
                      ? '-${fee.amount.abs().toDisplayMoney(maxDigits: 0)}'
                      : fee.amount.abs().toDisplayMoney(maxDigits: 0),
                  style: textTheme.titleMedium?.copyWith(
                    color: fee.paid ? colorScheme.error : colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4),
                InkWell(
                  onTap: () => _onDeleteFee(fee),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: EdgeInsets.all(4),
                    child: Icon(
                      Icons.delete_outline,
                      size: 16,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getFeeIconColor(ExtraFeesEntity fee) {
    if (fee.paid) {
      return Colors.orange; // 支出用橙色（类似购物）
    } else {
      return Colors.green; // 收入用绿色（类似工资）
    }
  }

  IconData _getFeeIcon(ExtraFeesEntity fee) {
    if (fee.paid) {
      // 根据费用名称选择合适的图标
      final name = fee.name.toLowerCase();
      if (name.contains('购物') || name.contains('买') || name.contains('商品')) {
        return Icons.shopping_bag;
      } else if (name.contains('餐') ||
          name.contains('吃') ||
          name.contains('食')) {
        return Icons.restaurant;
      } else if (name.contains('交通') ||
          name.contains('车') ||
          name.contains('油')) {
        return Icons.directions_car;
      } else if (name.contains('娱乐') ||
          name.contains('游戏') ||
          name.contains('电影')) {
        return Icons.movie;
      } else {
        return Icons.shopping_bag; // 默认支出图标
      }
    } else {
      // 收入图标
      final name = fee.name.toLowerCase();
      if (name.contains('工资') || name.contains('薪水') || name.contains('收入')) {
        return Icons.work;
      } else if (name.contains('奖金') ||
          name.contains('红包') ||
          name.contains('奖励')) {
        return Icons.card_giftcard;
      } else if (name.contains('投资') ||
          name.contains('理财') ||
          name.contains('股票')) {
        return Icons.trending_up;
      } else {
        return Icons.work; // 默认收入图标
      }
    }
  }

  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: color,
          ),
          SizedBox(width: 8),
          Text(
            title,
            style: textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncludeInTotalSection(List<ExtraFeesEntity> fees) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calculate_outlined,
                size: 16,
                color: colorScheme.primary,
              ),
              SizedBox(width: 6),
              Text(
                l10n.assetAdditionalIncludeInTotalSectionTitle,
                style: textTheme.titleSmall?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            l10n.assetAdditionalIncludeInTotalSectionHint,
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 12),
          ...fees.map((fee) => _buildIncludeInTotalItem(fee)),
        ],
      ),
    );
  }

  Widget _buildIncludeInTotalItem(ExtraFeesEntity fee) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Text(
              fee.name,
              style: textTheme.bodyMedium,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 8),
          Switch(
            value: fee.includeInTotal,
            onChanged: (value) {
              final updatedFee = fee.copyWith(includeInTotal: value);
              state.updateExtraFees(updatedFee);
            },
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  Future<void> _onAddExtraFees() async {
    final newFees = await showEditExtraFeesModal(context, extraFees: null);
    if (newFees != null) {
      state.addExtraFees(newFees);
    }
  }

  Future<void> _onEditFee(ExtraFeesEntity fee) async {
    final updatedFee = await showEditExtraFeesModal(context, extraFees: fee);
    if (updatedFee != null) {
      state.updateExtraFees(updatedFee);
    }
  }

  void _onDeleteFee(ExtraFeesEntity fee) {
    showConfirm(
      context,
      title: l10n.tips,
      content: l10n.confirmDeleteTips,
      onConfirm: () {
        state.removeExtraFees(fee);
      },
    );
  }

  void _onSave() {
    context.pop(state.getCurrentExtraFees());
  }

  Future<bool> _onWillPop() async {
    if (!state.hasChanges) {
      return true;
    }

    final result = await showConfirm(
      context,
      title: l10n.tips,
      content: l10n.assetEditRetentionTips,
      confirmText: l10n.commonSave,
      cancelText: l10n.cancel,
    );

    if (result == true) {
      _onSave();
      return false;
    }

    return result == false;
  }
}
