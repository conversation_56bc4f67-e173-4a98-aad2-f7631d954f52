import 'package:flutter/material.dart';
import 'package:tasks/modal/select_image_type/select_image_type.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/widgets/app_icon_widget.dart';

class SelectIcon extends StatefulWidget {
  final Function(AppIcon icon) onChange;

  final AppIcon? icon;

  const SelectIcon({Key? key, required this.onChange, this.icon})
      : super(key: key);

  @override
  State<SelectIcon> createState() => _SelectIconState();
}

class _SelectIconState extends State<SelectIcon> {
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return GestureDetector(
      onTap: () async {
        showImageSourceTypeSelect(context, (v) async {
          if (v == null) return;
          widget.onChange(v);
        });
      },
      child: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer,
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.zero,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: _buildIconWidget(context, widget.icon),
        ),
      ),
    );
  }

  Widget _buildIconWidget(BuildContext context, AppIcon? icon) {
    if (icon == null) {
      return Icon(
        Icons.add,
        color: colorScheme.primary,
      );
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Stack(
        children: [
          Container(
            color: colorScheme.surfaceContainer,
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: AppIconWidget(
              icon: icon,
              size: 80,
              fit: BoxFit.contain,
            ),
          )
        ],
      ),
    );
  }
}
