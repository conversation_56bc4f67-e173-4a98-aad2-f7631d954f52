import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/buy_channel.dart';
import 'package:tasks/providers/buy_channel_store.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/widgets/select/item_select_form.dart';

class BuyChannelSelect extends StatelessWidget {
  const BuyChannelSelect({
    super.key,
    required this.selectedChannelId,
    required this.onChange,
  });

  final String? selectedChannelId;
  final ValueChanged<BuyChannelEntity?> onChange;

  @override
  Widget build(BuildContext context) {
    final buyChannelStore = getIt.get<BuyChannelStore>();
    final l10n = S.of(context);

    return Observer(builder: (context) {
      final selectedChannel = selectedChannelId != null
          ? buyChannelStore.buyChannels
              .where((channel) => channel.id == selectedChannelId)
              .firstOrNull
          : null;

      return ItemSelectForm(
        title: l10n.assetBuyChannelLabel,
        value: selectedChannel?.name ?? l10n.commonPleaseSelect,
        onTap: () {
          _showChannelSelector(context, buyChannelStore.buyChannels.toList());
        },
      );
    });
  }

  void _showChannelSelector(
      BuildContext context, List<BuyChannelEntity> channels) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _ChannelSelectorBottomSheet(
        channels: channels,
        selectedChannelId: selectedChannelId,
        onChannelSelected: (channel) {
          onChange(channel);
          Navigator.of(context).pop();
        },
        onManageChannels: () {
          Navigator.of(context).pop();
          context.push(Routers.buyChannelManage);
        },
      ),
    );
  }
}

class _ChannelSelectorBottomSheet extends StatelessWidget {
  const _ChannelSelectorBottomSheet({
    required this.channels,
    required this.selectedChannelId,
    required this.onChannelSelected,
    required this.onManageChannels,
  });

  final List<BuyChannelEntity> channels;
  final String? selectedChannelId;
  final ValueChanged<BuyChannelEntity?> onChannelSelected;
  final VoidCallback onManageChannels;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final l10n = S.of(context);

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部拖拽指示器
          Container(
            margin: EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // 标题
          Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  l10n.assetBuyChannelSelect,
                  style: textTheme.titleLarge,
                ),
                Spacer(),
                TextButton(
                  onPressed: onManageChannels,
                  child: Text(l10n.assetBuyChannelManage),
                ),
              ],
            ),
          ),
          // 清除选择选项
          ListTile(
            leading: Icon(Icons.clear, color: colorScheme.onSurfaceVariant),
            title: Text(l10n.assetBuyChannelNone),
            trailing: selectedChannelId == null
                ? Icon(Icons.check, color: colorScheme.primary)
                : null,
            onTap: () => onChannelSelected(null),
          ),
          Divider(height: 1),
          // 渠道列表
          ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.5,
            ),
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: channels.length,
              separatorBuilder: (context, index) => Divider(
                height: 1,
                thickness: 1,
                color: colorScheme.outlineVariant,
                indent: 16,
                endIndent: 16,
              ),
              itemBuilder: (context, index) {
                final channel = channels[index];
                final isSelected = channel.id == selectedChannelId;

                return ListTile(
                  title: Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: Text(channel.name),
                  ),
                  trailing: isSelected
                      ? Icon(Icons.check, color: colorScheme.primary)
                      : null,
                  onTap: () => onChannelSelected(channel),
                );
              },
            ),
          ),
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}
