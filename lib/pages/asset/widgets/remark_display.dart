import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/pages/asset/remark/asset_remark_edit.dart';
import 'package:tasks/models/asset_remark.dart';

class RemarkDisplay extends StatelessWidget {
  const RemarkDisplay({
    super.key,
    required this.remark,
    required this.assetName,
    required this.onRemarkChanged,
    this.remarkHistory,
    this.onClearHistory,
    this.onDeleteRemark,
  });

  final String? remark;
  final String? assetName;
  final ValueChanged<String?> onRemarkChanged;
  final List<AssetRemarkEntity>? remarkHistory;
  final Function(List<AssetRemarkEntity>)? onClearHistory;
  final Function(String)? onDeleteRemark;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final l10n = S.of(context);

    return InkWell(
      onTap: () async {
        final result = await Navigator.of(context).push<String>(
          MaterialPageRoute(
            builder: (context) => AssetRemarkEditPage(
              initialRemark: remark,
              assetName: assetName,
              remarkHistory: remarkHistory,
              onClearHistory: onClearHistory,
              onDeleteRemark: onDeleteRemark,
            ),
          ),
        );

        if (result != null) {
          onRemarkChanged(result.isEmpty ? null : result);
        }
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  l10n.assetRemarkLabel,
                  style: textTheme.bodyMedium,
                ),
                Spacer(),
                Icon(
                  Icons.edit_outlined,
                  size: 16,
                  color: colorScheme.onSurfaceVariant,
                ),
              ],
            ),
            SizedBox(height: 8),
            if (remark != null && remark!.isNotEmpty) ...[
              Text(
                remark!,
                style: textTheme.bodyMedium,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ] else ...[
              Text(
                l10n.assetRemarkPlaceholder,
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
