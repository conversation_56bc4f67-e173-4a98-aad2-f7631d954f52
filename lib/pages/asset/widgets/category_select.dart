import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/pages/category/select_category/category_grid_select.dart';
import 'package:tasks/widgets/app_icon_widget.dart';

class CategorySelect extends StatelessWidget {
  const CategorySelect(
      {super.key, required this.categoryEntity, required this.onChange});

  final CategoryEntity? categoryEntity;
  final ValueChanged<CategoryEntity> onChange;

  CategoryEntity? get _selectedCategory => categoryEntity;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    S l10n = S.of(context);
    return InkWell(
      onTap: () async {
        // 使用新的网格选择器
        final result = await showCategoryGridSelect(context, categoryEntity);
        if (result == null) return;
        onChange(result);
      },
      child: Container(
        padding: EdgeInsets.all(16),
        constraints: BoxConstraints(maxWidth: 200),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer, // 使用相同的背景色
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              l10n.assetSelectCategory,
              style: textTheme.bodyMedium,
            ),
            const SizedBox(width: 64),
            Expanded(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Visibility(
                      visible: _selectedCategory != null,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: AppIconWidget(icon: _selectedCategory?.icon),
                      )),
                  Flexible(
                    flex: 1,
                    child: Text(
                      _selectedCategory?.name ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: textTheme.titleMedium,
                      maxLines: 1,
                    ),
                  ),
                  SizedBox(
                    width: 6,
                  ),
                  Icon(
                    Icons.expand,
                    size: 16,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
