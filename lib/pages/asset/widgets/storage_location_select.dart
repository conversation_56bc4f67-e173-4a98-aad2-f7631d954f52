import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/storage_location.dart';
import 'package:tasks/providers/storage_location_store.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/widgets/select/item_select_form.dart';

class StorageLocationSelect extends StatelessWidget {
  const StorageLocationSelect({
    super.key,
    required this.selectedLocationId,
    required this.onChange,
  });

  final String? selectedLocationId;
  final ValueChanged<StorageLocationEntity?> onChange;

  @override
  Widget build(BuildContext context) {
    final storageLocationStore = getIt.get<StorageLocationStore>();
    final l10n = S.of(context);

    return Observer(builder: (context) {
      final selectedLocation = selectedLocationId != null
          ? storageLocationStore.storageLocations
              .where((location) => location.id == selectedLocationId)
              .firstOrNull
          : null;

      return ItemSelectForm(
        title: l10n.assetStorageLocationLabel,
        value: selectedLocation?.name ?? l10n.commonPleaseSelect,
        onTap: () {
          _showLocationSelector(
              context, storageLocationStore.storageLocations.toList());
        },
      );
    });
  }

  void _showLocationSelector(
      BuildContext context, List<StorageLocationEntity> locations) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _LocationSelectorBottomSheet(
        locations: locations,
        selectedLocationId: selectedLocationId,
        onLocationSelected: (location) {
          onChange(location);
          Navigator.of(context).pop();
        },
        onManageLocations: () {
          Navigator.of(context).pop();
          context.push(Routers.storageLocationManage);
        },
      ),
    );
  }
}

class _LocationSelectorBottomSheet extends StatelessWidget {
  const _LocationSelectorBottomSheet({
    required this.locations,
    required this.selectedLocationId,
    required this.onLocationSelected,
    required this.onManageLocations,
  });

  final List<StorageLocationEntity> locations;
  final String? selectedLocationId;
  final ValueChanged<StorageLocationEntity?> onLocationSelected;
  final VoidCallback onManageLocations;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final l10n = S.of(context);

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部拖拽指示器
          Container(
            margin: EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // 标题
          Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  l10n.assetStorageLocationSelect,
                  style: textTheme.titleLarge,
                ),
                Spacer(),
                TextButton(
                  onPressed: onManageLocations,
                  child: Text(l10n.assetStorageLocationManage),
                ),
              ],
            ),
          ),
          // 清除选择选项
          ListTile(
            leading: Icon(Icons.clear, color: colorScheme.onSurfaceVariant),
            title: Text(l10n.assetStorageLocationNone),
            trailing: selectedLocationId == null
                ? Icon(Icons.check, color: colorScheme.primary)
                : null,
            onTap: () => onLocationSelected(null),
          ),
          Divider(height: 1),
          // 存放位置列表
          ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.5,
            ),
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: locations.length,
              separatorBuilder: (context, index) => Divider(
                height: 1,
                thickness: 1,
                color: colorScheme.outlineVariant,
                indent: 16,
                endIndent: 16,
              ),
              itemBuilder: (context, index) {
                final location = locations[index];
                final isSelected = location.id == selectedLocationId;

                return ListTile(
                  title: Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: Text(location.name),
                  ),
                  trailing: isSelected
                      ? Icon(Icons.check, color: colorScheme.primary)
                      : null,
                  onTap: () => onLocationSelected(location),
                );
              },
            ),
          ),
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}
