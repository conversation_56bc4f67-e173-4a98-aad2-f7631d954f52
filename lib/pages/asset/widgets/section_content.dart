import 'package:flutter/material.dart';

class SectionContentWidget extends StatefulWidget {
  final String title;

  final Widget? titleWidget;

  final List<Widget> children;

  final enableDivider;

  SectionContentWidget(
      {Key? key,
      required this.title,
      this.titleWidget,
      required this.children,
      this.enableDivider = true})
      : super(key: key) {}

  @override
  State<SectionContentWidget> createState() => _SectionContentWidgetState();
}

class _SectionContentWidgetState extends State<SectionContentWidget> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final list = List.generate(widget.children.length, (index) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          widget.children[index],
          if (widget.enableDivider && index != widget.children.length - 1)
            Divider(
              height: 1,
              indent: 12,
              endIndent: 12,
              color: colorScheme.outline.withOpacity(0.1),
            ),
        ],
      );
    });
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 16),
        Row(
          children: [
            Text(
              widget.title,
              textAlign: TextAlign.start,
              style: textTheme.titleMedium,
            ),
            if (widget.titleWidget != null) widget.titleWidget!,
          ],
        ),
        SizedBox(height: 16),
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Container(
            color: colorScheme.surfaceContainer,
            child: Column(
              children: [...list],
            ),
          ),
        )
      ],
    );
  }
}
