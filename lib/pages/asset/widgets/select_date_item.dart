import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/datepicker/date_picker.dart';
import 'package:tasks/modal/datepicker/period_date_picker.dart';
import 'package:tasks/utils/date_utils.dart';

class SelectDateItem extends StatefulWidget {
  SelectDateItem(
      {super.key,
      required this.title,
      this.date,
      this.baseDate,
      this.maxDate,
      this.minDate,
      required this.onChange,
      this.padding = const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12),
      this.periodOptions,
      this.showPeriodButtons = false});

  final String title;
  final DateTime? date;
  final DateTime? baseDate;
  final DateTime? maxDate;
  final DateTime? minDate;
  final ValueChanged<DateTime?> onChange;
  final EdgeInsetsGeometry padding;
  final List<PeriodOption>? periodOptions;
  final bool showPeriodButtons;

  @override
  State<SelectDateItem> createState() => _SelectDateItemState();
}

class _SelectDateItemState extends State<SelectDateItem> {
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  String get title => widget.title;

  DateTime get _date => widget.date ?? DateTime.now();

  S get l10n => S.of(context);

  String get dateFormat {
    return widget.date == null
        ? l10n.commonPleaseSelect
        : DateFormatUtils.formatDate(_date);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      child: InkWell(
        onTap: () async {
          // 弹窗时移除焦点
          FocusScope.of(context).unfocus();
          // 时间选择弹窗，包含1个月，3个月，6个月，9个月，1年，3年等时间段选项
          final selectedDate = await showAppDatePicker(
            context,
            _date,
            baseDate: widget.baseDate,
            title: widget.title,
            max: widget.maxDate,
            min: widget.minDate,
            periodOptions: widget.periodOptions,
            showPeriodButtons: widget.showPeriodButtons,
          );

          // 如果用户选择了日期，调用回调
          widget.onChange(selectedDate);
        },
        child: Row(
          children: [
            Text(
              widget.title,
              style: textTheme.titleMedium,
            ),
            Spacer(),
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: Container(
                color: colorScheme.surfaceContainerHigh,
                padding: EdgeInsets.all(4),
                child: Text(
                  dateFormat,
                  style: textTheme.bodyMedium,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
