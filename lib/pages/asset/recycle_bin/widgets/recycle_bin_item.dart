import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/recycle_bin/recycle_bin_item.dart';
import 'package:tasks/utils/date_utils.dart';
import 'package:tasks/widgets/app_icon_widget.dart';
import 'package:tasks/widgets/glassmorphism_container.dart';

class RecycleBinItemWidget extends StatelessWidget {
  final RecycleBinItem item;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onRestore;
  final VoidCallback? onPermanentlyDelete;
  final VoidCallback? onLongPress;

  const RecycleBinItemWidget({
    super.key,
    required this.item,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onRestore,
    this.onPermanentlyDelete,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final l10n = S.of(context);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Slidable(
        enabled: !isSelectionMode,
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          extentRatio: 0.6,
          children: [
            // 恢复
            SlidableAction(
              onPressed: (context) => onRestore?.call(),
              backgroundColor: colorScheme.primary,
              foregroundColor: colorScheme.onPrimary,
              icon: Icons.restore_rounded,
              label: l10n.assetRecycleBinRestore,
            ),
            // 永久删除
            SlidableAction(
              onPressed: (context) => onPermanentlyDelete?.call(),
              backgroundColor: colorScheme.error,
              foregroundColor: colorScheme.onError,
              icon: Icons.delete_forever_rounded,
              label: l10n.assetRecycleBinDelete,
            ),
          ],
        ),
        child: GlassmorphismContainer(
          gradientColors: [
            colorScheme.surface.withValues(alpha: 0.1),
            colorScheme.surface.withValues(alpha: 0.05),
          ],
          borderColor: colorScheme.outline.withValues(alpha: 0.2),
          shadowColors: [
            colorScheme.shadow.withValues(alpha: 0.1),
          ],
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              onLongPress: onLongPress,
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    // 选择框（选择模式下显示）
                    if (isSelectionMode) ...[
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isSelected
                              ? colorScheme.primary
                              : Colors.transparent,
                          border: Border.all(
                            color: isSelected
                                ? colorScheme.primary
                                : colorScheme.outline,
                            width: 2,
                          ),
                        ),
                        child: isSelected
                            ? Icon(
                                Icons.check,
                                size: 16,
                                color: colorScheme.onPrimary,
                              )
                            : null,
                      ),
                      SizedBox(width: 12),
                    ],

                    // 资产图标
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: item.asset.icon != null
                            ? AppIconWidget(
                                icon: item.asset.icon!,
                                size: 32,
                              )
                            : Icon(
                                Icons.inventory_2_rounded,
                                color: colorScheme.onPrimaryContainer,
                                size: 24,
                              ),
                      ),
                    ),

                    SizedBox(width: 16),

                    // 资产信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 资产名称
                          Text(
                            item.asset.name,
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),

                          // 资产价格
                          Text(
                            '¥${item.asset.price.toStringAsFixed(2)}',
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 4),

                          // 删除时间
                          Text(
                            '${l10n.assetRecycleBinDeletedAt}: ${DateFormatUtils.formatDateYMD(item.deletedAt)}',
                            style: textTheme.bodySmall?.copyWith(
                              color:
                                  colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 右侧指示器（非选择模式下显示）
                    if (!isSelectionMode)
                      Icon(
                        Icons.chevron_right_rounded,
                        color: colorScheme.onSurface.withValues(alpha: 0.4),
                        size: 20,
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
