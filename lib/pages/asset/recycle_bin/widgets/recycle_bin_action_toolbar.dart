import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';

class RecycleBinActionToolbar extends StatelessWidget {
  final int selectedCount;
  final VoidCallback? onBatchRestore;
  final VoidCallback? onBatchDelete;

  const RecycleBinActionToolbar({
    super.key,
    required this.selectedCount,
    this.onBatchRestore,
    this.onBatchDelete,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final l10n = S.of(context);

    if (selectedCount == 0) {
      return SizedBox.shrink();
    }

    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.surface,
          border: Border(
            top: BorderSide(
              color: colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // 选中数量显示
                Expanded(
                  child: Text(
                    l10n.homeBatchSelectedCount(selectedCount),
                    style: textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                // 批量恢复按钮
                TextButton.icon(
                  onPressed: onBatchRestore,
                  icon: Icon(
                    Icons.restore_rounded,
                    size: 18,
                  ),
                  label: Text(l10n.assetRecycleBinBatchRestore),
                  style: TextButton.styleFrom(
                    foregroundColor: colorScheme.primary,
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(
                        color: colorScheme.primary.withValues(alpha: 0.3),
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 8),

                // 批量删除按钮
                TextButton.icon(
                  onPressed: onBatchDelete,
                  icon: Icon(
                    Icons.delete_forever_rounded,
                    color: colorScheme.error,
                    size: 18,
                  ),
                  label: Text(l10n.assetRecycleBinBatchDelete),
                  style: TextButton.styleFrom(
                    foregroundColor: colorScheme.error,
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(
                        color: colorScheme.error.withValues(alpha: 0.3),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
