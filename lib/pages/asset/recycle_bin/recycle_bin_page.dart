import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/pages/asset/recycle_bin/recycle_bin_state.dart';
import 'package:tasks/pages/asset/recycle_bin/widgets/recycle_bin_action_toolbar.dart';
import 'package:tasks/pages/asset/recycle_bin/widgets/recycle_bin_item.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/common_app_bar.dart';

class RecycleBinPage extends StatefulWidget {
  const RecycleBinPage({super.key});

  @override
  State<RecycleBinPage> createState() => _RecycleBinPageState();
}

class _RecycleBinPageState extends State<RecycleBinPage> {
  late RecycleBinState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;
  TextTheme get textTheme => Theme.of(context).textTheme;
  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = RecycleBinState();
    state.loadData();
  }

  @override
  Widget build(BuildContext context) {
    return StateWarp(
      store: state,
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Observer(builder: (context) {
          if (state.isLoading) {
            return Center(child: CircularProgressIndicator());
          }

          if (!state.hasItems) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              Expanded(child: _buildRecycleBinList()),
              RecycleBinActionToolbar(
                selectedCount: state.selectedCount,
                onBatchRestore: _handleBatchRestore,
                onBatchDelete: _handleBatchDelete,
              ),
            ],
          );
        }),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CommonAppBar(
      title: l10n.assetRecycleBin,
      actions: [
        Observer(builder: (context) {
          if (state.isSelectionMode) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextButton(
                  onPressed: () => state.selectAll(),
                  child: Text(
                    l10n.homeBatchSelectAll,
                    style: textTheme.bodyMedium?.copyWith(
                      color: colorScheme.primary,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () => state.exitSelectionMode(),
                  child: Text(
                    l10n.cancel,
                    style: textTheme.bodyMedium?.copyWith(
                      color: colorScheme.primary,
                    ),
                  ),
                ),
              ],
            );
          }
          return SizedBox.shrink();
        }),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.delete_outline_rounded,
            size: 80,
            color: colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          SizedBox(height: 16),
          Text(
            l10n.assetRecycleBinEmpty,
            style: textTheme.headlineSmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          SizedBox(height: 8),
          Text(
            l10n.assetRecycleBinEmptyDesc,
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecycleBinList() {
    return Observer(builder: (context) {
      return ListView.builder(
        padding: EdgeInsets.symmetric(vertical: 8),
        itemCount: state.recycleBinItems.length,
        itemBuilder: (context, index) {
          final item = state.recycleBinItems[index];
          final isSelected = state.selectedItemIds.contains(item.id);

          return RecycleBinItemWidget(
            item: item,
            isSelected: isSelected,
            isSelectionMode: state.isSelectionMode,
            onTap: () => state.onItemTap(item.id),
            onLongPress: () {
              if (!state.isSelectionMode) {
                state.enterSelectionMode();
                state.toggleItemSelection(item.id);
              }
            },
            onRestore: () => _handleRestore(item.id),
            onPermanentlyDelete: () => _handlePermanentlyDelete(item.id),
          );
        },
      );
    });
  }

  void _handleRestore(String itemId) {
    showConfirm(
      context,
      content: l10n.assetRecycleBinRestoreConfirm,
      onConfirm: () async {
        await state.restoreItem(itemId);
        ToastUtils.success(context, l10n.assetRecycleBinRestoreSuccess);
      },
    );
  }

  void _handlePermanentlyDelete(String itemId) {
    showConfirm(
      context,
      content: l10n.assetRecycleBinDeleteConfirm,
      onConfirm: () async {
        await state.permanentlyDeleteItem(itemId);
        ToastUtils.success(context, l10n.assetRecycleBinDeleteSuccess);
      },
    );
  }

  void _handleBatchRestore() {
    showConfirm(
      context,
      content: l10n.assetRecycleBinBatchRestoreConfirm(state.selectedCount),
      onConfirm: () async {
        await state.batchRestore();
        ToastUtils.success(context, l10n.assetRecycleBinRestoreSuccess);
      },
    );
  }

  void _handleBatchDelete() {
    showConfirm(
      context,
      content: l10n.assetRecycleBinBatchDeleteConfirm(state.selectedCount),
      onConfirm: () async {
        await state.batchPermanentlyDelete();
        ToastUtils.success(context, l10n.assetRecycleBinDeleteSuccess);
      },
    );
  }
}
