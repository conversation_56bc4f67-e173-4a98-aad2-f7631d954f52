import 'package:mobx/mobx.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/data/recycle_bin/recycle_bin_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/recycle_bin/recycle_bin_item.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/asset_store.dart';

part 'recycle_bin_state.g.dart';

class RecycleBinState = _RecycleBinState with _$RecycleBinState;

abstract class _RecycleBinState extends BaseStore with Store {
  final RecycleBinLocalDataSource _recycleBinDataSource = getIt.get();
  final AssetRepo _assetRepo = getIt.get();
  final AssetStore _assetStore = getIt.get();

  @observable
  ObservableList<RecycleBinItem> recycleBinItems =
      ObservableList<RecycleBinItem>();

  @observable
  ObservableSet<String> selectedItemIds = ObservableSet<String>();

  @observable
  bool isSelectionMode = false;

  @computed
  int get selectedCount => selectedItemIds.length;

  @computed
  bool get hasItems => recycleBinItems.isNotEmpty;

  @computed
  bool get hasSelection => selectedItemIds.isNotEmpty;

  @action
  Future<void> loadData() async {
    try {
      setLoading(true);
      final items = await _recycleBinDataSource.getRecycleBinItems();

      runInAction(() {
        recycleBinItems.clear();
        recycleBinItems.addAll(items);
      });
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }

  @action
  void toggleItemSelection(String itemId) {
    if (selectedItemIds.contains(itemId)) {
      selectedItemIds.remove(itemId);
    } else {
      selectedItemIds.add(itemId);
    }

    // 如果没有选中项，退出选择模式
    if (selectedItemIds.isEmpty) {
      isSelectionMode = false;
    }
  }

  @action
  void onItemTap(String itemId) {
    if (isSelectionMode) {
      // 如果已经在选择模式，则切换选中状态
      toggleItemSelection(itemId);
    } else {
      // 如果不在选择模式，则进入选择模式并选中该项
      isSelectionMode = true;
      selectedItemIds.clear();
      selectedItemIds.add(itemId);
    }
  }

  @action
  void enterSelectionMode() {
    isSelectionMode = true;
  }

  @action
  void exitSelectionMode() {
    isSelectionMode = false;
    selectedItemIds.clear();
  }

  @action
  void selectAll() {
    selectedItemIds.clear();
    selectedItemIds.addAll(recycleBinItems.map((item) => item.id));
    isSelectionMode = true;
  }

  @action
  void clearSelection() {
    selectedItemIds.clear();
    isSelectionMode = false;
  }

  @action
  Future<void> restoreItem(String itemId) async {
    try {
      setLoading(true);

      final asset = await _recycleBinDataSource.restoreFromRecycleBin(itemId);
      if (asset != null) {
        // 将资产添加回资产列表
        await _assetRepo.createAsset(asset);

        // 刷新内存中的资产数据
        await _assetStore.fetchAssets();

        // 从回收站列表中移除
        runInAction(() {
          recycleBinItems.removeWhere((item) => item.id == itemId);
          selectedItemIds.remove(itemId);
        });
      }
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }

  @action
  Future<void> permanentlyDeleteItem(String itemId) async {
    try {
      setLoading(true);

      await _recycleBinDataSource.permanentlyDelete(itemId);

      runInAction(() {
        recycleBinItems.removeWhere((item) => item.id == itemId);
        selectedItemIds.remove(itemId);
      });
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }

  @action
  Future<void> batchRestore() async {
    try {
      setLoading(true);

      final assets = await _recycleBinDataSource.batchRestoreFromRecycleBin(
        selectedItemIds.toList(),
      );

      // 将资产批量添加回资产列表
      for (final asset in assets) {
        await _assetRepo.createAsset(asset);
      }

      // 刷新内存中的资产数据
      await _assetStore.fetchAssets();

      // 从回收站列表中移除
      runInAction(() {
        recycleBinItems
            .removeWhere((item) => selectedItemIds.contains(item.id));
        clearSelection();
      });
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }

  @action
  Future<void> batchPermanentlyDelete() async {
    try {
      setLoading(true);

      await _recycleBinDataSource.batchPermanentlyDelete(
        selectedItemIds.toList(),
      );

      runInAction(() {
        recycleBinItems
            .removeWhere((item) => selectedItemIds.contains(item.id));
        clearSelection();
      });
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }

  @action
  Future<void> clearRecycleBin() async {
    try {
      setLoading(true);

      await _recycleBinDataSource.clearRecycleBin();

      runInAction(() {
        recycleBinItems.clear();
        clearSelection();
      });
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }
}
