import 'package:mobx/mobx.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/user_preferences/user_preferences_entity.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/user_preferences_store.dart';

part 'asset_settings_state.g.dart';

class AssetSettingsState = _AssetSettingsState with _$AssetSettingsState;

abstract class _AssetSettingsState extends BaseStore with Store {
  UserPreferencesStore userPreferencesStore = getIt.get();

  @computed
  UserPreferencesEntity get entity => userPreferencesStore.preferencesEntity;

  @computed
  String get displayMoneySymbol {
    return '${entity.currencyCode} (${entity.currencySymbol})';
  }

  @computed
  bool get defaultIncludeExtraFeesInTotal {
    return entity.defaultIncludeExtraFeesInTotal;
  }

  @computed
  bool get excludeRetiredFromTotal {
    return entity.excludeRetired;
  }

  @action
  Future<void> updateDefaultIncludeExtraFeesInTotal(bool value) async {
    final updatedEntity =
        entity.copyWith(defaultIncludeExtraFeesInTotal: value);
    await userPreferencesStore.updatePreferences(updatedEntity);
  }

  @action
  Future<void> updateExcludeRetiredFromTotal(bool value) async {
    final updatedEntity = entity.copyWith(excludeRetired: value);
    await userPreferencesStore.updatePreferences(updatedEntity);
  }
}
