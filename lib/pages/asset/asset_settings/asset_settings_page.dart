import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/pages/asset/asset_settings/asset_settings_state.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/widgets/common_app_bar.dart';
import 'package:tasks/widgets/section_container.dart';

class AssetSettingsPage extends StatefulWidget {
  const AssetSettingsPage({super.key});

  @override
  State<AssetSettingsPage> createState() => _AssetSettingsPageState();
}

class _AssetSettingsPageState extends State<AssetSettingsPage> {
  late AssetSettingsState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = AssetSettingsState();
  }

  @override
  Widget build(BuildContext context) {
    return StateWarp(
      store: state,
      child: Scaffold(
        backgroundColor: colorScheme.surface,
        appBar: CommonAppBar(
          title: l10n.homeAssetSettings,
          backgroundColor: colorScheme.surface,
        ),
        body: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // 统计设置部分
            Observer(builder: (context) {
              return SectionWarp(
                  title: l10n.settingStatisticalSettings,
                  children: [
                    ListTile(
                      title: Text(
                        l10n.settingRetiredAsset,
                        style: textTheme.bodyMedium,
                      ),
                      trailing: Switch(
                        value: state.excludeRetiredFromTotal,
                        onChanged: (value) {
                          state.updateExcludeRetiredFromTotal(value);
                        },
                      ),
                    ),
                    // 添加货币设置选项
                    ListTile(
                      title: Text(
                        l10n.settingCurrencySetting,
                        style: textTheme.bodyMedium,
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Observer(builder: (context) {
                            return Text(state.displayMoneySymbol);
                          }),
                          Icon(Icons.arrow_forward_ios, size: 16),
                        ],
                      ),
                      onTap: () async {
                        await context.push(Routers.currencySetting);
                        // 用户偏好设置现在通过computed属性自动更新
                      },
                    ),
                    // 附加费用默认计入总价值设置
                    Observer(builder: (context) {
                      return ListTile(
                        title: Text(
                          l10n.assetDefaultIncludeExtraFeesInTotal,
                          style: textTheme.bodyMedium,
                        ),
                        subtitle: Text(
                          l10n.assetDefaultIncludeExtraFeesInTotalHint,
                          style: textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                        trailing: Switch(
                          value: state.defaultIncludeExtraFeesInTotal,
                          onChanged: (value) {
                            state.updateDefaultIncludeExtraFeesInTotal(value);
                          },
                        ),
                      );
                    }),
                  ]);
            }),
          ],
        ),
      ),
    );
  }
}
