import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/pages/asset/asset_add/widgets/extra_fees_item.dart';

class ExtraFeesList extends StatefulWidget {
  final List<ExtraFeesEntity> extras;

  final ValueChanged<List<ExtraFeesEntity>> onChange;

  const ExtraFeesList(
      {super.key, required this.extras, required this.onChange});

  @override
  State<ExtraFeesList> createState() => _ExtraFeesListState();
}

class _ExtraFeesListState extends State<ExtraFeesList> {
  S get l10n => S.of(context);

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  Widget build(BuildContext context) {
    if (widget.extras.isEmpty) {
      return Container(
        padding: EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 48,
              color: colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
            ),
            SizedBox(height: 12),
            Text(
              l10n.assetAdditionalItemsEmptyTips,
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: widget.extras.asMap().entries.map((entry) {
        final index = entry.key;
        final element = entry.value;
        final isLast = index == widget.extras.length - 1;

        return Column(
          children: [
            ExtraFeesItem(
              entity: element,
              onDelete: (item) => onDelete(item),
              onUpdate: onChange,
            ),
            if (!isLast)
              Divider(
                height: 1,
                thickness: 0.5,
                indent: 68, // 对齐内容，跳过左侧图标
                endIndent: 16,
                color: colorScheme.outlineVariant.withValues(alpha: 0.5),
              ),
          ],
        );
      }).toList(),
    );
  }

  void onChange(ExtraFeesEntity entity) {
    // 调用父组件传递的回调函数
    final newList = widget.extras.toList();
    final index = newList.indexWhere((i) => i.id == entity.id);
    if (index != -1) {
      newList[index] = entity;
    }
    widget.onChange(newList);
  }

  void onDelete(ExtraFeesEntity entity) {
    // 调用父组件传递的回调函数
    final newList = widget.extras.where((i) => i.id != entity.id).toList();
    widget.onChange(newList);
  }
}
