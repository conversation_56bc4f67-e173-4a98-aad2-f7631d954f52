import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/modal/extra_fees/edit_extra_fess_modal.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/utils/date_utils.dart';
import 'package:tasks/utils/double_ext.dart';

class ExtraFeesItem extends StatefulWidget {
  final ExtraFeesEntity entity;

  final ValueChanged<ExtraFeesEntity> onDelete;

  final ValueChanged<ExtraFeesEntity> onUpdate;

  ExtraFeesItem(
      {super.key,
      required this.entity,
      required this.onUpdate,
      required this.onDelete}) {}

  @override
  State<ExtraFeesItem> createState() => _ExtraFeesItemState();
}

class _ExtraFeesItemState extends State<ExtraFeesItem> {
  S get l10n => S.of(context);

  TextEditingController nameController = TextEditingController();

  TextEditingController valueController = TextEditingController();

  @override
  void initState() {
    super.initState();
    nameController.text = widget.entity.name;
    valueController.text = widget.entity.value;
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    TextTheme textStyle = Theme.of(context).textTheme;

    ExtraFeesEntity value = widget.entity;

    return InkWell(
      onTap: onItemPress,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 左侧图标
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: value.paid
                    ? colorScheme.errorContainer.withValues(alpha: 0.3)
                    : colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                value.paid ? Icons.remove : Icons.add,
                color: value.paid ? colorScheme.error : colorScheme.primary,
                size: 20,
              ),
            ),
            SizedBox(width: 12),
            // 中间内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          value.name,
                          style: textStyle.titleMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // 计入总价值标识
                      if (value.includeInTotal)
                        Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            l10n.assetAdditionalIncludeInTotalShort,
                            style: textStyle.labelSmall?.copyWith(
                              color: colorScheme.primary,
                              fontSize: 10,
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        DateFormatUtils.formatDateYMD(value.createAt),
                        style: textStyle.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      if (value.note.isNotEmpty) ...[
                        SizedBox(width: 8),
                        Text(
                          '•',
                          style: textStyle.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            value.note,
                            style: textStyle.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(width: 12),
            // 右侧金额和操作
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value.amount.toDisplayMoney(maxDigits: 3),
                  style: textStyle.titleMedium?.copyWith(
                    color: value.paid ? colorScheme.error : colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4),
                InkWell(
                  onTap: onDelete,
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: EdgeInsets.all(4),
                    child: Icon(
                      Icons.delete_outline,
                      size: 16,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void onDelete() {
    showConfirm(context, content: l10n.confirmDeleteTips, onConfirm: () {
      widget.onDelete(widget.entity);
    });
  }

  Future<void> onItemPress() async {
    final newEntity =
        await showEditExtraFeesModal(context, extraFees: widget.entity);
    if (newEntity != null) {
      widget.onUpdate(newEntity);
    }
  }
}
