import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/iconselect/icon_select_utils.dart';
import 'package:tasks/modal/select_image_type/select_image_type.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/widgets/app_icon_widget.dart';

class SelectIconWidget extends StatefulWidget {
  final AppIcon? value;

  final ValueChanged<AppIcon> onChange;

  SelectIconWidget({super.key, required this.value, required this.onChange}) {}

  @override
  State<SelectIconWidget> createState() => _SelectIconWidgetState();
}

class _SelectIconWidgetState extends State<SelectIconWidget> {
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: _handlePress,
        child: Container(
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: colorScheme.surfaceContainer),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 4,
              ),
              AppIconWidget(
                icon: widget.value,
                size: 16,
              ),
              SizedBox(
                width: 4,
              ),
              Flexible(
                flex: 1,
                child: Text(
                  l10n.assetSelectIcon,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: textTheme.bodySmall!
                      .copyWith(color: colorScheme.onSurface),
                ),
              )
            ],
          ),
        ));
  }

  Future<void> _handlePress() async {
    showImageSourceTypeSelect(context, (v) async {
      if (v == null) return;
      widget.onChange(v);
    });
  }
}
