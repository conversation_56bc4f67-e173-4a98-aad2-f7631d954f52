import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/asset/extra_fees.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/utils/string_ext.dart';

part 'asset_add_state.g.dart';

class AssetAddState = _AssetAddState with _$AssetAddState;

enum AssetAddEvent { addSuccess }

abstract class _AssetAddState with Store {
  AssetRepo repo = getIt.get();

  final nameController = TextEditingController();
  final priceController = TextEditingController();

  @observable
  String assetName = '';

  @observable
  String price = "";

  @observable
  DateTime purchaseDate = DateTime.now();

  @observable
  CategoryEntity? categoryEntity;

  @observable
  AppIcon? icon;

  @observable
  String? errorMsg;

  @observable
  AssetAddEvent? event;

  @observable
  List<ExtraFeesEntity> extraFees = [];

  @computed
  bool get enableBack {
    return price.isEmpty && assetName.isEmpty && extraFees.isEmpty;
  }

  @action
  void changePrice(String value) {
    price = value;
  }

  @action
  void changeAssetName(String value) {
    assetName = value;
  }

  @action
  void changePurchaseDate(DateTime date) {
    purchaseDate = date;
  }

  @action
  void changeCategory(CategoryEntity? categoryEntity) {
    this.categoryEntity = categoryEntity;
  }

  @action
  void changeIcon(AppIcon? icon) {
    this.icon = icon;
  }

  @action
  void addExtraFees(ExtraFeesEntity extra) {
    final newList = extraFees.toList();
    newList.add(extra);
    extraFees = newList;
  }

  @action
  void removeExtraFees(int index) {
    final newList = extraFees.toList();
    newList.removeAt(index);
    extraFees = newList;
  }

  @action
  void changeExtraFees(List<ExtraFeesEntity> list) {
    extraFees = list;
  }

  @action
  Future<void> confirm() async {
    if (assetName.isNullOrEmpty) {
      errorMsg = '请输入资产名称';
      return;
    }
    final realPrice = price.toDoubleOrNull ?? 0.0;
    // 构造资产数据
    final asset = Asset(
      name: assetName,
      price: realPrice,
      purchaseDate: purchaseDate,
      extraFees: extraFees.where((i) => i.valid).toList(),
      categoryId: categoryEntity?.id,
      icon: icon,
    );
    final result = await repo.createAsset(asset);
    // 添加成功
    result.onSuccess((_) {
      event = AssetAddEvent.addSuccess;
    }).onError((e, code) {
      runInAction(() {
        errorMsg = e;
      });
    });
  }

  @action
  void reDo() {
    nameController.clear();
    priceController.clear();
    changeAssetName("");
    changePrice("");
    changeExtraFees([]);
    changePurchaseDate(DateTime.now());
  }

  @action
  void clearMsg() {
    errorMsg = null;
  }

  @action
  void clearEvent() {
    event = null;
  }
}
