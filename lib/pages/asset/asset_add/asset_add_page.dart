import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/pages/asset/asset_add/asset_add_state.dart';
import 'package:tasks/pages/asset/asset_add/widgets/select_icon.dart';
import 'package:tasks/pages/asset/widgets/select_date_item.dart';
import 'package:tasks/pages/category/select_category/select_category.dart';
import 'package:tasks/providers/asset_store.dart';
import 'package:tasks/utils/reg_constans.dart';
import 'package:tasks/utils/string_ext.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/textfield/clear_input_textfield.dart';

class AssetAddPage extends StatefulWidget {
  const AssetAddPage({super.key});

  @override
  State<AssetAddPage> createState() => _AssetAddPageState();
}

class _AssetAddPageState extends State<AssetAddPage> {
  late AssetAddState state;
  AssetStore assetStore = getIt.get();

  AssetRepo repo = getIt.get();

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = AssetAddState();
    // 处理页面事件
    reaction((_) => state.errorMsg, (msg) {
      if (msg.isNullOrEmpty) return;
      state.clearMsg();
      ToastUtils.error(context, msg!);
    });
    // 监听页面事件
    reaction((_) => state.event, (event) async {
      if (event == null) return;
      state.clearEvent();
      switch (event) {
        case AssetAddEvent.addSuccess:
          ToastUtils.success(context, l10n.assetAddSuccess);
          // 清空数据，不隐藏弹窗
          await assetStore.fetchAssets();
          final enable = await repo.checkAddAvailable();
          if (!enable) {
            Navigator.pop(context);
            return;
          }
          // 判断会员及上限
          state.reDo();
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Observer(builder: (context) {
      return PopScope(
        canPop: state.enableBack,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) {
            return;
          }
          await showConfirm(context, content: l10n.assetAddRetentionTips,
              onConfirm: () {
            context.pop();
          });
        },
        child: Container(
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainer, // 设置背景颜色
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12), // 左上角圆角
              topRight: Radius.circular(12), // 右上角圆角
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom, // 处理键盘弹出
              ),
              child: SingleChildScrollView(
                  child: Column(
                children: [
                  // 资产名称
                  Observer(builder: (context) {
                    return ClearInputTextField(
                        hintText: l10n.assetAssetNameInputPlaceholder,
                        value: state.assetName,
                        textInputAction: TextInputAction.next,
                        fillColor: colorScheme.surfaceContainer,
                        onChange: (v) {
                          state.changeAssetName(v);
                        });
                  }),
                  // 价格
                  Observer(builder: (context) {
                    return ClearInputTextField(
                        hintText: l10n.assetPriceInputPlaceholder,
                        value: state.price,
                        keyboardType:
                            TextInputType.numberWithOptions(decimal: true),
                        filterPattern: RegFilterConstant.regAmount,
                        fillColor: colorScheme.surfaceContainer,
                        onChange: state.changePrice);
                  }),
                  // 购买日期
                  Observer(builder: (context) {
                    return SelectDateItem(
                      title: l10n.assetPurchaseDate,
                      date: state.purchaseDate,
                      maxDate: DateTime.now(),
                      onChange: (v) {
                        state.changePurchaseDate(v ?? DateTime.now());
                      },
                    );
                  }),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 8.0, horizontal: 12),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SelectCategoryWidget(
                          value: state.categoryEntity,
                          onChange: (v) {
                            state.changeCategory(v);
                          },
                        ),
                        const SizedBox(
                          width: 6,
                        ),
                        SelectIconWidget(
                          value: state.icon,
                          onChange: (v) {
                            state.changeIcon(v);
                          },
                        ),
                        const SizedBox(
                          width: 12,
                        ),
                        const Spacer(),
                        SizedBox(
                          height: 30,
                          child: FilledButton(
                              onPressed: () {
                                state.confirm();
                              },
                              child: Text(
                                l10n.commonAdd,
                                style: textTheme.bodySmall!
                                    .copyWith(color: colorScheme.onPrimary),
                              )),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 48,
                  )
                ],
              )),
            ),
          ),
        ),
      );
    });
  }
}
