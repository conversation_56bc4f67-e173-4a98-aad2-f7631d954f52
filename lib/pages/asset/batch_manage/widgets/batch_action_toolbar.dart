import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/providers/theme_provider.dart';

class BatchActionToolbar extends StatelessWidget {
  final int selectedCount;
  final List<CategoryEntity> categories;
  final VoidCallback onDelete;
  final ValueChanged<String?> onUpdateCategory;
  final VoidCallback onRetire;

  const BatchActionToolbar({
    Key? key,
    required this.selectedCount,
    required this.categories,
    required this.onDelete,
    required this.onUpdateCategory,
    required this.onRetire,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDark = themeProvider.customTheme.themeMode == ThemeMode.dark ||
        (themeProvider.customTheme.themeMode == ThemeMode.system &&
            MediaQuery.of(context).platformBrightness == Brightness.dark);
    final l10n = S.of(context);

    return Container(
      decoration: BoxDecoration(
        color: isDark
            ? colorScheme.surfaceContainer.withOpacity(0.95)
            : colorScheme.surface.withOpacity(0.95),
        border: Border(
          top: BorderSide(
            color: colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            SizedBox(
              width: 4,
            ),
            // 选中数量提示
            Expanded(
              child: Text(
                S.of(context).homeBatchSelectedCount(selectedCount),
                style: textTheme.bodySmall!.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.8),
                ),
              ),
            ),

            // 批量分类按钮
            _buildActionButton(
              context,
              icon: Icons.category_outlined,
              label: S.of(context).homeBatchCategory,
              onTap: () => _showCategorySelector(context),
            ),

            SizedBox(width: 12),

            // 批量退役按钮
            _buildActionButton(
              context,
              icon: Icons.archive_outlined,
              label: S.of(context).homeBatchRetire,
              onTap: onRetire,
            ),

            SizedBox(width: 12),

            // 批量删除按钮
            _buildActionButton(
              context,
              icon: Icons.delete_outline,
              label: l10n.delete,
              onTap: onDelete,
              isDestructive: true,
            ),
            SizedBox(
              width: 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isDestructive
                  ? colorScheme.error.withOpacity(0.3)
                  : colorScheme.outline.withOpacity(0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 18,
                color: isDestructive
                    ? colorScheme.error
                    : colorScheme.onSurface.withOpacity(0.8),
              ),
              SizedBox(width: 4),
              Text(
                label,
                style: textTheme.bodySmall!.copyWith(
                  color: isDestructive
                      ? colorScheme.error
                      : colorScheme.onSurface.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCategorySelector(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: colorScheme.outline.withOpacity(0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    S.of(context).homeBatchSelectCategory,
                    style: textTheme.titleMedium!.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // 分类列表
            Container(
              constraints: BoxConstraints(maxHeight: 300),
              child: ListView(
                shrinkWrap: true,
                children: [
                  // 未分类选项
                  ListTile(
                    leading: Icon(Icons.category_outlined),
                    title: Text(S.of(context).homeBatchUncategorized),
                    onTap: () {
                      Navigator.of(context).pop();
                      onUpdateCategory(null);
                    },
                  ),

                  // 分类选项
                  ...categories.map((category) => ListTile(
                        leading: Icon(Icons.folder_outlined),
                        title: Text(category.name),
                        onTap: () {
                          Navigator.of(context).pop();
                          onUpdateCategory(category.id);
                        },
                      )),
                ],
              ),
            ),

            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }
}
