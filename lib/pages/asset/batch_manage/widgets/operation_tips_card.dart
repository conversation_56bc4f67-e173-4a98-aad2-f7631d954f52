import 'package:flutter/material.dart';
import 'package:tasks/generated/l10n.dart';

/// 批量管理操作提示卡片
class OperationTipsCard extends StatelessWidget {
  final bool isReorderMode;
  final bool hasSelectedAssets;
  final int selectedCount;

  const OperationTipsCard({
    Key? key,
    required this.isReorderMode,
    required this.hasSelectedAssets,
    required this.selectedCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final l10n = S.of(context);

    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      margin: EdgeInsets.fromLTRB(16, 8, 16, 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: _getBackgroundColor(colorScheme),
        border: Border.all(
          color: _getBorderColor(colorScheme),
        ),
      ),
      child: Row(
        children: [
          _buildIcon(colorScheme),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getTitle(l10n),
                  style: textTheme.titleSmall!.copyWith(
                    color: _getTextColor(colorScheme),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  _getDescription(l10n),
                  style: textTheme.bodySmall!.copyWith(
                    color: _getTextColor(colorScheme).withValues(alpha: 0.8),
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
          if (hasSelectedAssets && !isReorderMode) ...[
            SizedBox(width: 8),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: colorScheme.primary,
              ),
              child: Text(
                '$selectedCount',
                style: textTheme.labelSmall!.copyWith(
                  color: colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getBackgroundColor(ColorScheme colorScheme) {
    if (isReorderMode) {
      return colorScheme.primaryContainer.withOpacity(0.3);
    } else if (hasSelectedAssets) {
      return colorScheme.secondaryContainer.withOpacity(0.3);
    } else {
      return colorScheme.surfaceContainer.withOpacity(0.5);
    }
  }

  Color _getBorderColor(ColorScheme colorScheme) {
    if (isReorderMode) {
      return colorScheme.primary.withOpacity(0.3);
    } else if (hasSelectedAssets) {
      return colorScheme.secondary.withOpacity(0.3);
    } else {
      return colorScheme.outline.withOpacity(0.2);
    }
  }

  Color _getTextColor(ColorScheme colorScheme) {
    if (isReorderMode) {
      return colorScheme.primary;
    } else if (hasSelectedAssets) {
      return colorScheme.secondary;
    } else {
      return colorScheme.onSurface.withOpacity(0.7);
    }
  }

  Widget _buildIcon(ColorScheme colorScheme) {
    IconData iconData;
    if (isReorderMode) {
      iconData = Icons.swap_vert_rounded;
    } else if (hasSelectedAssets) {
      iconData = Icons.checklist_rounded;
    } else {
      iconData = Icons.touch_app_rounded;
    }

    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: _getTextColor(colorScheme).withOpacity(0.1),
      ),
      child: Icon(
        iconData,
        size: 20,
        color: _getTextColor(colorScheme),
      ),
    );
  }

  String _getTitle(S l10n) {
    if (isReorderMode) {
      return '排序模式';
    } else if (hasSelectedAssets) {
      return '已选择 $selectedCount 个物品';
    } else {
      return '批量管理';
    }
  }

  String _getDescription(S l10n) {
    if (isReorderMode) {
      return '拖拽物品可调整排序，完成后点击右上角保存';
    } else if (hasSelectedAssets) {
      return '可以对选中的物品进行批量操作';
    } else {
      return '点击选择物品，长按右侧拖拽图标可调整排序';
    }
  }
}
