import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/providers/theme_provider.dart';
import 'package:tasks/utils/double_ext.dart';
import 'package:tasks/widgets/app_icon_widget.dart';

class BatchAssetItem extends StatelessWidget {
  final AssetCardEntity asset;
  final bool isSelected;
  final ValueChanged<bool>? onSelectionChanged;
  final bool isReorderMode;
  final VoidCallback? onLongPressToReorder;
  final VoidCallback? onDragHandleLongPress;

  const BatchAssetItem({
    Key? key,
    required this.asset,
    required this.isSelected,
    this.onSelectionChanged,
    this.isReorderMode = false,
    this.onLongPressToReorder,
    this.onDragHandleLongPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDark = themeProvider.customTheme.themeMode == ThemeMode.dark ||
        (themeProvider.customTheme.themeMode == ThemeMode.system &&
            MediaQuery.of(context).platformBrightness == Brightness.dark);
    final l10n = S.of(context);

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: isDark
            ? colorScheme.surfaceContainer.withOpacity(0.8)
            : colorScheme.surface.withOpacity(0.9),
        border: Border.all(
          color: isSelected
              ? colorScheme.primary.withOpacity(0.5)
              : colorScheme.outline.withOpacity(0.2),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: isReorderMode
              ? null
              : () {
                  onSelectionChanged?.call(!isSelected);
                },
          onLongPress: isReorderMode
              ? null
              : () {
                  // 长按进入排序模式
                  onLongPressToReorder?.call();
                },
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                // 选择框（非排序模式下显示）
                if (!isReorderMode)
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected
                            ? colorScheme.primary
                            : colorScheme.outline.withOpacity(0.5),
                        width: 2,
                      ),
                      color:
                          isSelected ? colorScheme.primary : Colors.transparent,
                    ),
                    child: isSelected
                        ? Icon(
                            Icons.check,
                            size: 12,
                            color: colorScheme.onPrimary,
                          )
                        : null,
                  ),

                SizedBox(width: 16),

                // 资产图标
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: AppIconWidget(
                      icon: asset.icon,
                      size: 28,
                    ),
                  ),
                ),

                SizedBox(width: 16),

                // 资产信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 资产名称
                      Text(
                        asset.name,
                        style: textTheme.titleMedium!.copyWith(
                          fontWeight: FontWeight.w600,
                          color: colorScheme.onSurface,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      SizedBox(height: 4),

                      // 分类名称
                      if (asset.showCategoryName)
                        Text(
                          asset.categoryName!,
                          style: textTheme.bodySmall!.copyWith(
                            color: colorScheme.onSurface.withOpacity(0.7),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                      if (!asset.showCategoryName)
                        Text(
                          l10n.homeBatchUncategorized,
                          style: textTheme.bodySmall!.copyWith(
                            color: colorScheme.onSurface.withOpacity(0.5),
                          ),
                        ),
                    ],
                  ),
                ),

                SizedBox(width: 16),

                // 价格信息
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      asset.price.toDisplayMoney(),
                      style: textTheme.titleMedium!.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.primary,
                      ),
                    ),
                    if (!asset.isInService)
                      Container(
                        margin: EdgeInsets.only(top: 4),
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: colorScheme.errorContainer,
                        ),
                        child: Text(
                          l10n.homeStatusRetired,
                          style: textTheme.labelSmall!.copyWith(
                            color: colorScheme.onErrorContainer,
                          ),
                        ),
                      ),
                  ],
                ),

                // 右侧拖拽图标（始终显示）
                SizedBox(width: 12),
                _buildDragHandle(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建拖拽手柄
  Widget _buildDragHandle(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (isReorderMode) {
      // 在排序模式下，使用ReorderableDragStartListener包装拖拽手柄
      return ReorderableDragStartListener(
        index: 0, // 这个index会在父组件中被正确设置
        child: _buildDragHandleContent(colorScheme),
      );
    } else {
      // 在非排序模式下，使用GestureDetector处理长按进入排序模式
      return GestureDetector(
        onLongPress: () {
          onDragHandleLongPress?.call();
        },
        child: _buildDragHandleContent(colorScheme),
      );
    }
  }

  /// 构建拖拽手柄内容
  Widget _buildDragHandleContent(ColorScheme colorScheme) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 200),
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: isReorderMode
            ? colorScheme.primary.withValues(alpha: 0.15)
            : colorScheme.surfaceContainer.withValues(alpha: 0.7),
        border: isReorderMode
            ? Border.all(
                color: colorScheme.primary.withValues(alpha: 0.3),
                width: 1,
              )
            : null,
      ),
      child: Icon(
        Icons.drag_handle,
        color: isReorderMode
            ? colorScheme.primary
            : colorScheme.onSurface.withValues(alpha: 0.6),
        size: 20,
      ),
    );
  }
}
