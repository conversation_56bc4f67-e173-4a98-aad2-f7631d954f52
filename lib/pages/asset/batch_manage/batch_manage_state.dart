import 'package:mobx/mobx.dart';
import 'package:tasks/data/asset/asset_local_data_source.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/providers/asset_store.dart';

part 'batch_manage_state.g.dart';

class BatchManageState = _BatchManageState with _$BatchManageState;

abstract class _BatchManageState extends BaseStore with Store {
  final AssetRepo _assetRepo = getIt.get();
  final AssetLocalDataSource _assetDataSource = getIt.get();
  final CategoryLocalDataSource _categoryDataSource = getIt.get();
  final AssetStore assetStore = getIt.get();

  // 外部传入的数据
  List<AssetCardEntity>? externalAssets;
  List<CategoryEntity>? externalCategories;

  @observable
  ObservableList<AssetCardEntity> assets = ObservableList<AssetCardEntity>();

  @observable
  ObservableList<CategoryEntity> categories = ObservableList<CategoryEntity>();

  // 初始化方法，支持外部传入数据
  void initialize({
    List<AssetCardEntity>? assets,
    List<CategoryEntity>? categories,
  }) {
    externalAssets = assets;
    externalCategories = categories;
  }

  @observable
  ObservableSet<String> selectedAssetIds = ObservableSet();

  @observable
  bool isReorderMode = false;

  @computed
  bool get hasSelectedAssets => selectedAssetIds.isNotEmpty;

  @computed
  bool get isAllSelected =>
      selectedAssetIds.length == assets.length && assets.isNotEmpty;

  @computed
  int get selectedCount => selectedAssetIds.length;

  @action
  Future<void> loadData() async {
    try {
      setLoading(true);
      // 全局更新资产数据
      assetStore.fetchAssets();
      if (externalAssets != null && externalCategories != null) {
        // 使用外部传入的数据
        runInAction(() {
          assets.clear();
          assets.addAll(externalAssets!);
          categories.clear();
          categories.addAll(externalCategories!);
        });
      } else {
        // 从本地加载数据
        final assetList = await _assetDataSource.getAssets();
        final categoryList = await _categoryDataSource.getCategories();

        runInAction(() {
          assets.clear();
          assets.addAll(assetList
              .map((asset) => _assetRepo.assetToCard(asset, categoryList))
              .toList());
          categories.clear();
          categories.addAll(categoryList);
        });
      }
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }

  @action
  void toggleAssetSelection(String assetId) {
    if (selectedAssetIds.contains(assetId)) {
      selectedAssetIds.remove(assetId);
    } else {
      selectedAssetIds.add(assetId);
    }
  }

  @action
  void selectAll() {
    if (isAllSelected) {
      selectedAssetIds.clear();
    } else {
      selectedAssetIds.clear();
      selectedAssetIds.addAll(assets.map((asset) => asset.id));
    }
  }

  @action
  void clearSelection() {
    selectedAssetIds.clear();
  }

  @action
  void toggleReorderMode() {
    isReorderMode = !isReorderMode;
    if (!isReorderMode) {
      clearSelection();
    }
  }

  @action
  void reorderAssets(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final item = assets.removeAt(oldIndex);
    assets.insert(newIndex, item);

    // 排序操作只在内存中进行，不立即保存到缓存
    // 如果需要保存排序结果，可以调用 saveOrder() 方法
  }

  @action
  Future<void> saveOrder() async {
    try {
      // 保存新的排序到缓存
      final assetIds = assets.map((asset) => asset.id).toList();
      await _assetRepo.swap(assetIds);
      // 全局更新资产数据
      assetStore.fetchAssets();
    } catch (e) {
      setError(e.toString());
    }
  }

  @action
  Future<void> batchDelete() async {
    try {
      setLoading(true);

      for (final assetId in selectedAssetIds) {
        await _assetRepo.deleteAsset(assetId);
      }

      // 重新加载数据
      await loadData();
      clearSelection();
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }

  @action
  Future<void> batchUpdateCategory(String? categoryId) async {
    try {
      setLoading(true);

      for (final assetId in selectedAssetIds) {
        final asset = assets.firstWhere((a) => a.id == assetId).asset;
        final updatedAsset = asset.copyWith(categoryId: categoryId);
        await _assetRepo.updateAsset(updatedAsset);
      }

      // 重新加载数据
      await loadData();
      clearSelection();
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }

  @action
  Future<void> batchRetire() async {
    try {
      setLoading(true);

      for (final assetId in selectedAssetIds) {
        final asset = assets.firstWhere((a) => a.id == assetId).asset;
        final updatedAsset = asset.copyWith(
          isInService: false,
          retireDate: DateTime.now(),
        );
        await _assetRepo.updateAsset(updatedAsset);
      }

      // 重新加载数据
      await loadData();
      clearSelection();
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }
}
