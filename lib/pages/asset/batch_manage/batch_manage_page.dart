import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/modal/confirm/confirm_dialog_utils.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/pages/asset/batch_manage/batch_manage_state.dart';
import 'package:tasks/pages/asset/batch_manage/widgets/batch_action_toolbar.dart';
import 'package:tasks/pages/asset/batch_manage/widgets/batch_asset_item.dart';
import 'package:tasks/pages/asset/batch_manage/widgets/operation_tips_card.dart';
import 'package:tasks/pages/state_warp.dart';
import 'package:tasks/utils/toast_utils.dart';
import 'package:tasks/widgets/app_icon_widget.dart';
import 'package:tasks/widgets/common_app_bar.dart';

class BatchManagePage extends StatefulWidget {
  final List<AssetCardEntity>? assets;
  final List<CategoryEntity>? categories;

  const BatchManagePage({
    Key? key,
    this.assets,
    this.categories,
  }) : super(key: key);

  @override
  _BatchManagePageState createState() => _BatchManagePageState();
}

class _BatchManagePageState extends State<BatchManagePage> {
  late BatchManageState state;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  S get l10n => S.of(context);

  @override
  void initState() {
    super.initState();
    state = BatchManageState();
    state.initialize(
      assets: widget.assets,
      categories: widget.categories,
    );
    state.loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: CommonAppBar(
        title: l10n.homeBatchManagement,
        backgroundColor: colorScheme.surface,
        actions: [
          Observer(builder: (context) {
            // 排序模式下显示保存按钮
            if (state.isReorderMode) {
              return TextButton(
                onPressed: () => _handleSortAction(),
                child: Text(
                  l10n.commonSave,
                  style: textTheme.titleMedium!.copyWith(
                    color: colorScheme.primary,
                  ),
                ),
              );
            }
            // 非排序模式下显示全选按钮
            return TextButton(
              onPressed: state.assets.isEmpty ? null : state.selectAll,
              child: Text(
                state.isAllSelected
                    ? l10n.homeBatchCancelAll
                    : l10n.homeBatchSelectAll,
                style: textTheme.titleMedium!.copyWith(
                  color: state.assets.isEmpty
                      ? colorScheme.onSurface.withOpacity(0.5)
                      : colorScheme.primary,
                ),
              ),
            );
          }),
        ],
      ),
      body: StateWarp(
        store: state,
        child: Observer(builder: (context) {
          if (state.assets.isEmpty) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              // 操作提示
              _buildOperationTips(),

              Expanded(
                child: state.isReorderMode
                    ? _buildReorderableList()
                    : _buildSelectableList(),
              ),
              Observer(builder: (context) {
                return AnimatedContainer(
                  duration: Duration(milliseconds: 300),
                  height: state.hasSelectedAssets ? 80 : 0,
                  child: state.hasSelectedAssets
                      ? BatchActionToolbar(
                          selectedCount: state.selectedCount,
                          categories: state.categories,
                          onDelete: _handleBatchDelete,
                          onUpdateCategory: _handleBatchUpdateCategory,
                          onRetire: _handleBatchRetire,
                        )
                      : SizedBox.shrink(),
                );
              }),
            ],
          );
        }),
      ),
    );
  }

  /// 构建操作提示
  Widget _buildOperationTips() {
    return Observer(builder: (context) {
      return OperationTipsCard(
        isReorderMode: state.isReorderMode,
        hasSelectedAssets: state.hasSelectedAssets,
        selectedCount: state.selectedCount,
      );
    });
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: colorScheme.onSurface.withOpacity(0.5),
          ),
          SizedBox(height: 16),
          Text(
            l10n.homeEmptyListTitle,
            style: textTheme.titleLarge!.copyWith(
              color: colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          SizedBox(height: 8),
          Text(
            l10n.homeEmptyListDesc,
            style: textTheme.bodyMedium!.copyWith(
              color: colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectableList() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: state.assets.length,
      itemBuilder: (context, index) {
        final asset = state.assets[index];
        return Observer(builder: (context) {
          return BatchAssetItem(
            asset: asset,
            isSelected: state.selectedAssetIds.contains(asset.id),
            onSelectionChanged: (selected) {
              state.toggleAssetSelection(asset.id);
            },
            onLongPressToReorder: () {
              // 长按物品进入排序模式（保留原有功能）
              state.clearSelection(); // 清除选择状态
              state.toggleReorderMode(); // 进入排序模式
            },
            onDragHandleLongPress: () {
              // 长按拖拽图标进入排序模式
              state.clearSelection(); // 清除选择状态
              state.toggleReorderMode(); // 进入排序模式
            },
          );
        });
      },
    );
  }

  Widget _buildReorderableList() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: ReorderableListView.builder(
        // padding: EdgeInsets.only(top: 16, bottom: 16),
        // 只保留上下padding
        itemCount: state.assets.length,
        onReorder: state.reorderAssets,
        proxyDecorator: (child, index, animation) {
          // Return a version of the child without margins
          return Padding(
            padding: EdgeInsets.zero,
            child: child,
          );
        },
        // 自定义拖拽时的装饰器
        itemBuilder: (context, index) {
          final asset = state.assets[index];
          return _buildReorderableAssetItem(asset, index);
        },
      ),
    );
  }

  /// 构建可重排序的资产项
  Widget _buildReorderableAssetItem(AssetCardEntity asset, int index) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 6),
      key: ValueKey(asset.id),
      child: _buildAssetItemWithDragHandle(asset, index),
    );
  }

  /// 构建带拖拽手柄的资产项
  Widget _buildAssetItemWithDragHandle(AssetCardEntity asset, int index) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: colorScheme.surface.withValues(alpha: 0.9),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              // 资产图标
              _buildAssetIcon(asset, colorScheme),
              SizedBox(width: 16),

              // 资产信息
              Expanded(
                child: _buildAssetInfo(asset, textTheme, colorScheme),
              ),

              SizedBox(width: 16),

              // 价格信息
              _buildPriceInfo(asset, textTheme, colorScheme),

              SizedBox(width: 12),

              // 拖拽手柄
              ReorderableDragStartListener(
                index: index,
                child: _buildDragHandleIcon(colorScheme),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 拖拽时的装饰器
  Widget _proxyDecorator(Widget child, int index, Animation<double> animation) {
    return AnimatedBuilder(
      animation: animation,
      builder: (BuildContext context, Widget? child) {
        final double animValue = Curves.easeInOut.transform(animation.value);
        final double elevation = lerpDouble(0, 6, animValue)!;
        final double scale = lerpDouble(1, 1.02, animValue)!;

        return Transform.scale(
          scale: scale,
          child: Material(
            elevation: elevation,
            borderRadius: BorderRadius.circular(16),
            shadowColor: colorScheme.shadow.withValues(alpha: 0.3),
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  void _handleBatchDelete() {
    showConfirm(
      context,
      title: l10n.tips,
      content: l10n.homeBatchDeleteConfirm(state.selectedCount),
      onConfirm: () async {
        await state.batchDelete();
        if (mounted) {
          ToastUtils.success(context, l10n.homeDeleteSuccess);
        }
      },
    );
  }

  void _handleBatchUpdateCategory(String? categoryId) async {
    await state.batchUpdateCategory(categoryId);
    if (mounted) {
      ToastUtils.success(context, l10n.homeBatchCategorySuccess);
    }
  }

  void _handleBatchRetire() {
    showConfirm(
      context,
      title: l10n.tips,
      content: l10n.homeBatchRetireConfirm(state.selectedCount),
      onConfirm: () async {
        await state.batchRetire();
        if (mounted) {
          ToastUtils.success(context, l10n.homeBatchRetireSuccess);
        }
      },
    );
  }

  /// 处理排序操作
  void _handleSortAction() async {
    if (state.isReorderMode) {
      // 保存排序模式 - 保存排序结果并退出排序模式
      await state.saveOrder();
      state.toggleReorderMode(); // 退出排序模式
      if (mounted) {
        ToastUtils.success(context, "排序已保存");
      }
    }
  }

  /// 构建资产图标
  Widget _buildAssetIcon(AssetCardEntity asset, ColorScheme colorScheme) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: asset.icon != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: AppIconWidget(
                icon: asset.icon,
                size: 48,
                fit: BoxFit.cover,
              ),
            )
          : Icon(
              Icons.inventory_2_outlined,
              color: colorScheme.primary,
              size: 24,
            ),
    );
  }

  /// 构建资产信息
  Widget _buildAssetInfo(
      AssetCardEntity asset, TextTheme textTheme, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          asset.name,
          style: textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (asset.categoryName != null) ...[
          SizedBox(height: 4),
          Text(
            asset.categoryName!,
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  /// 构建价格信息
  Widget _buildPriceInfo(
      AssetCardEntity asset, TextTheme textTheme, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '¥${asset.price.toStringAsFixed(2)}',
          style: textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.primary,
          ),
        ),
        if (!asset.isInService) ...[
          SizedBox(height: 4),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: colorScheme.error.withValues(alpha: 0.1),
            ),
            child: Text(
              '已退役',
              style: textTheme.labelSmall?.copyWith(
                color: colorScheme.error,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 构建拖拽手柄图标
  Widget _buildDragHandleIcon(ColorScheme colorScheme) {
    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: colorScheme.primary.withValues(alpha: 0.15),
        border: Border.all(
          color: colorScheme.primary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Icon(
        Icons.drag_handle,
        color: colorScheme.primary,
        size: 20,
      ),
    );
  }
}
