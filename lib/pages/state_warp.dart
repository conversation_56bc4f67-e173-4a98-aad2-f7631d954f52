import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/pages/base_store.dart';
import 'package:tasks/utils/string_ext.dart';
import 'package:tasks/utils/toast_utils.dart';

class StateWarp extends StatefulWidget {
  final BaseStore store;

  final Widget child;

  StateWarp({super.key, required this.store, required this.child});

  @override
  State<StateWarp> createState() => _StateWarpState();
}

class _StateWarpState extends State<StateWarp> {
  @override
  void initState() {
    super.initState();
    // loading
    reaction((_) => widget.store.isLoading, (v) {
      if (v) {
        context.loaderOverlay.show();
      } else {
        context.loaderOverlay.hide();
      }
    });
    // 提示信息
    reaction((_) => widget.store.errorMessage, (r) {
      widget.store.clearError();
      if (r.isNullOrEmpty) return;
      ToastUtils.error(context, r!);
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
