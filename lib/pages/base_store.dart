// lib/stores/base_store.dart
import 'dart:async';

import 'package:mobx/mobx.dart';

part 'base_store.g.dart';

abstract class BaseStore = _BaseStore with _$BaseStore;

abstract class _BaseStore with Store {
  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  @computed
  bool get hasError => errorMessage != null;

  @action
  void setLoading(bool value) {
    isLoading = value;
    if (value) errorMessage = null; // 加载时自动清除错误
  }

  @action
  void clearError() {
    errorMessage = null;
  }

  @action
  void setError(String message) {
    errorMessage = message;
    isLoading = false;
  }

  // 通用错误处理模板方法
  Future<T?> runWithLoading<T>(FutureOr<T?> Function() action) async {
    try {
      setLoading(true);
      return await action();
    } catch (e, stackTrace) {
      setError(e.toString());
      // 记录详细的错误信息
      print('Error in runWithLoading: $e\n$stackTrace');
      return null;
    } finally {
      setLoading(false);
    }
  }
}
