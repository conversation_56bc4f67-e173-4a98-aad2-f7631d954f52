import 'dart:convert';
import 'dart:io';

import 'package:home_widget/home_widget.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/asset/asset_statistics.dart';
import 'package:tasks/utils/double_ext.dart';

/// 基于home_widget包的小组件管理器
/// 提供更稳定的Flutter与原生小组件数据交互
class HomeWidgetManager {
  static final HomeWidgetManager _instance = HomeWidgetManager._internal();

  factory HomeWidgetManager() => _instance;

  HomeWidgetManager._internal();

  // 小组件数据键名
  static const String _keyTotalPrice = 'total_price';
  static const String _keyDailyPrice = 'daily_price';
  static const String _keyTotalAssets = 'total_assets';
  static const String _keyMaxDailyPrice = 'max_daily_price';
  static const String _keyMinDailyPrice = 'min_daily_price';
  static const String _keyCurrencySymbol = 'currency_symbol';
  static const String _keyCurrencyCode = 'currency_code';
  static const String _keyAppName = 'app_name';
  static const String _keyLastUpdate = 'last_update';
  static const String _keyIsDarkMode = 'is_dark_mode';

  /// 初始化home_widget
  Future<void> initialize() async {
    if (!Platform.isAndroid && !Platform.isIOS) return;

    try {
      print('HomeWidgetManager: Initializing home_widget...');

      // 设置App Group ID (仅iOS需要)
      if (Platform.isIOS) {
        await HomeWidget.setAppGroupId('group.com.looptry.jiwu');
        print('HomeWidgetManager: iOS App Group ID set');
      }

      // 注册点击回调
      HomeWidget.widgetClicked.listen(_onWidgetClicked);
      print('HomeWidgetManager: Widget click listener registered');

      print('HomeWidgetManager: Initialization completed');
    } catch (e) {
      print('HomeWidgetManager: Failed to initialize: $e');
    }
  }

  /// 更新小组件数据
  Future<void> updateWidgetData(
    AssetStatistics statistics, {
    String currencySymbol = '¥',
    String currencyCode = 'CNY',
    String appName = '极简记物',
    bool isDarkMode = false,
  }) async {
    if (!Platform.isAndroid && !Platform.isIOS) return;

    try {
      print('HomeWidgetManager: Updating widget data...');

      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // 将所有数据组装成JSON格式保存
      final widgetData = {
        'totalPrice': statistics.totalPrice,
        'dailyPrice': statistics.dailyPrice,
        'totalAssets': statistics.totalAssets,
        'maxDailyPrice': statistics.maxDailyPrice,
        'minDailyPrice': statistics.minDailyPrice,
        'currencySymbol': currencySymbol,
        'currencyCode': currencyCode,
        'appName': appName,
        'lastUpdate': timestamp,
        'isDarkMode': isDarkMode,
      };

      // 转换为JSON字符串并保存
      final jsonString = jsonEncode(widgetData);
      await HomeWidget.saveWidgetData<String>('widget_data', jsonString);

      print('HomeWidgetManager: Data saved successfully');
      print(
          'HomeWidgetManager: Statistics - Total: ${statistics.totalPrice.toDisplayMoney()}, Daily: ${statistics.dailyPrice.toDisplayMoney()}, Assets: ${statistics.totalAssets}');

      // 刷新小组件
      await _refreshWidget();
    } catch (e) {
      print('HomeWidgetManager: Failed to update widget data: $e');
    }
  }

  /// 刷新小组件显示
  Future<void> _refreshWidget() async {
    try {
      print('HomeWidgetManager: Refreshing widget display...');

      if (Platform.isIOS) {
        await HomeWidget.updateWidget(
          name: 'HomeWidget',
          iOSName: 'com.looptry.jiwu.HomeWidget',
        );
        print('HomeWidgetManager: iOS widget refreshed');
      } else if (Platform.isAndroid) {
        // Android需要分别刷新两个小组件
        await HomeWidget.updateWidget(
          qualifiedAndroidName: 'com.looptry.guiwu.widget.AssetWidgetReceiver',
        );
        await HomeWidget.updateWidget(
          qualifiedAndroidName:
              'com.looptry.guiwu.widget.SmallAssetWidgetReceiver',
        );
        print('HomeWidgetManager: Android widget refreshed');
      }
    } catch (e) {
      print('HomeWidgetManager: Failed to refresh widget: $e');
    }
  }

  /// 处理小组件点击事件
  void _onWidgetClicked(Uri? uri) {
    print('HomeWidgetManager: Widget clicked with URI: $uri');

    if (uri != null) {
      final action = uri.queryParameters['action'] ?? 'refresh';
      print('HomeWidgetManager: Processing action: $action');

      // 触发数据刷新
      _handleWidgetRefresh();
    }
  }

  /// 处理小组件刷新请求
  void _handleWidgetRefresh() async {
    try {
      print('HomeWidgetManager: Handling widget refresh request...');

      // 导入AssetRepo来触发实际的数据刷新
      final assetRepo = getIt.get<AssetRepo>();

      print('HomeWidgetManager: Triggering asset data refresh...');
      await assetRepo.refreshWidget();

      print('HomeWidgetManager: Widget refresh request processed successfully');
    } catch (e) {
      print('HomeWidgetManager: Failed to handle widget refresh: $e');
    }
  }

  /// 获取当前小组件数据（用于调试）
  Future<Map<String, dynamic>> getWidgetData() async {
    try {
      final data = <String, dynamic>{};

      data[_keyTotalPrice] =
          await HomeWidget.getWidgetData<double>(_keyTotalPrice);
      data[_keyDailyPrice] =
          await HomeWidget.getWidgetData<double>(_keyDailyPrice);
      data[_keyTotalAssets] =
          await HomeWidget.getWidgetData<int>(_keyTotalAssets);
      data[_keyMaxDailyPrice] =
          await HomeWidget.getWidgetData<double>(_keyMaxDailyPrice);
      data[_keyMinDailyPrice] =
          await HomeWidget.getWidgetData<double>(_keyMinDailyPrice);
      data[_keyCurrencySymbol] =
          await HomeWidget.getWidgetData<String>(_keyCurrencySymbol);
      data[_keyCurrencyCode] =
          await HomeWidget.getWidgetData<String>(_keyCurrencyCode);
      data[_keyAppName] = await HomeWidget.getWidgetData<String>(_keyAppName);
      data[_keyLastUpdate] =
          await HomeWidget.getWidgetData<int>(_keyLastUpdate);
      data[_keyIsDarkMode] =
          await HomeWidget.getWidgetData<bool>(_keyIsDarkMode);

      return data;
    } catch (e) {
      print('HomeWidgetManager: Failed to get widget data: $e');
      return {};
    }
  }

  /// 检查小组件是否可用
  Future<bool> isWidgetAvailable() async {
    if (!Platform.isAndroid && !Platform.isIOS) return false;

    try {
      // home_widget包会自动处理可用性检查
      return true;
    } catch (e) {
      print('HomeWidgetManager: Widget availability check failed: $e');
      return false;
    }
  }

  /// 打开系统小组件设置（如果支持）
  Future<void> openWidgetSettings() async {
    try {
      // home_widget包暂不直接支持打开设置，可以使用其他方式
      print(
          'HomeWidgetManager: Opening widget settings not directly supported by home_widget');
    } catch (e) {
      print('HomeWidgetManager: Failed to open widget settings: $e');
    }
  }
}
