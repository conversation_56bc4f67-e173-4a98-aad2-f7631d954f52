import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/storage_location/storage_location_local_data_source.dart';
import 'package:tasks/models/storage_location.dart';

part 'storage_location_store.g.dart';

@lazySingleton
class StorageLocationStore = StorageLocationStoreBase with _$StorageLocationStore;

abstract class StorageLocationStoreBase with Store {
  final StorageLocationLocalDataSource _dataSource;

  StorageLocationStoreBase(this._dataSource) {
    fetchStorageLocations();
  }

  @observable
  ObservableList<StorageLocationEntity> storageLocations = ObservableList<StorageLocationEntity>();

  // 初始获取存放位置数据
  @action
  void fetchStorageLocations() {
    runInAction(() async {
      final result = await _dataSource.getStorageLocations();
      storageLocations.clear();
      storageLocations.addAll(result);
    });
  }
}
