import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/user_preferences/user_preferences_lds.dart';
import 'package:tasks/models/user_preferences/user_preferences_entity.dart';

part 'user_preferences_store.g.dart';

@lazySingleton
class UserPreferencesStore = UserPreferencesStoreBase
    with _$UserPreferencesStore;

abstract class UserPreferencesStoreBase with Store {
  final UserPreferencesLds _userPreferencesLds;

  UserPreferencesStoreBase(this._userPreferencesLds) {
    _initPreferences();
  }

  @observable
  UserPreferencesEntity preferencesEntity =
      UserPreferencesEntity.defaultPreferences();

  void _initPreferences() {
    final preferences = _userPreferencesLds.getUserPreferences();
    if (preferences != null) {
      preferencesEntity = preferences;
    }
  }

  @action
  Future<void> updatePreferences(UserPreferencesEntity preferences) async {
    await _userPreferencesLds.saveUserPreferences(preferences);
    preferencesEntity = preferences;
  }

  @action
  Future<void> refreshPreferences() async {
    final preferences = _userPreferencesLds.getUserPreferences();
    if (preferences != null) {
      preferencesEntity = preferences;
    }
  }

  // 搜索历史相关方法
  @action
  Future<void> addSearchHistory(String searchText) async {
    await _userPreferencesLds.addSearchHistory(searchText);
    await refreshPreferences();
  }

  @action
  Future<List<String>> getSearchHistory() async {
    return await _userPreferencesLds.getSearchHistory();
  }

  @action
  Future<void> removeSearchHistory(String searchText) async {
    await _userPreferencesLds.removeSearchHistory(searchText);
    await refreshPreferences();
  }

  @action
  Future<void> clearSearchHistory() async {
    await _userPreferencesLds.clearSearchHistory();
    await refreshPreferences();
  }

  // 货币相关方法
  @action
  Future<void> addCustomCurrency(CustomCurrency currency) async {
    await _userPreferencesLds.addCustomCurrency(currency);
    await refreshPreferences();
  }

  @action
  Future<void> removeCustomCurrency(String currencyCode) async {
    await _userPreferencesLds.removeCustomCurrency(currencyCode);
    await refreshPreferences();
  }

  @action
  Future<void> updatePreference({
    bool? showTotalAssetValue,
    String? currencySymbol,
    String? currencyCode,
    List<CustomCurrency>? customCurrencies,
    bool? enablePrivacyBlur,
    double? privacyBlurSigma,
    List<String>? searchHistory,
    bool? excludeRetired,
    bool? defaultIncludeExtraFeesInTotal,
  }) async {
    await _userPreferencesLds.updatePreference(
      showTotalAssetValue: showTotalAssetValue,
      currencySymbol: currencySymbol,
      currencyCode: currencyCode,
      customCurrencies: customCurrencies,
      enablePrivacyBlur: enablePrivacyBlur,
      privacyBlurSigma: privacyBlurSigma,
      searchHistory: searchHistory,
      excludeRetired: excludeRetired,
      defaultIncludeExtraFeesInTotal: defaultIncludeExtraFeesInTotal,
    );
    await refreshPreferences();
  }

  // 隐私保护设置相关方法
  @action
  Future<void> updatePrivacyBlurSettings({
    bool? enablePrivacyBlur,
    double? privacyBlurSigma,
  }) async {
    await _userPreferencesLds.updatePrivacyBlurSettings(
      enablePrivacyBlur: enablePrivacyBlur,
      privacyBlurSigma: privacyBlurSigma,
    );
    await refreshPreferences();
  }
}
