import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/asset/asset_local_data_source.dart';
import 'package:tasks/data/asset/asset_repo.dart';
import 'package:tasks/models/asset/asset.dart';

part 'asset_store.g.dart';

@lazySingleton
class AssetStore = AssetStoreBase with _$AssetStore;

abstract class AssetStoreBase with Store {
  final AssetRepo assetRepo;
  final AssetLocalDataSource _assetLocalDataSource;

  AssetStoreBase(this.assetRepo, this._assetLocalDataSource) {
    fetchAssets();
  }

  @observable
  ObservableList<Asset> assets = ObservableList();

  @action
  Future<List<Asset>> fetchAssets() async {
    final result = await _assetLocalDataSource.getAssets();
    runInAction(() async {
      assets.clear();
      assets.addAll(result);
    });
    // 更新数据同时，刷新下小组件
    assetRepo.refreshWidget();

    return result;
  }

  @action
  Future<void> updateAsset(Asset asset) async {
    await assetRepo.updateAsset(asset);
    // 更新当前列表的数据
    runInAction(() {
      final index = assets.indexWhere((i) => i.id == asset.id);
      if (index != -1) {
        assets[index] = asset;
      }
    });
  }

  @action
  Future<void> swapAssets(List<String> assetIds) async {
    await assetRepo.swap(assetIds);
    fetchAssets();
  }
}
