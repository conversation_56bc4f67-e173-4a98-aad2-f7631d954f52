import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tasks/data/theme/locale_local_data_source.dart';
import 'package:tasks/data/theme/theme_local_data_source.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/custom_theme/custom_theme.dart';

class ThemeProvider with ChangeNotifier {
  ThemeLocalDataSource _dataSource = getIt.get();
  LocaleLocalDataSource _localeLocalDataSource = getIt.get();

  CustomTheme customTheme = CustomTheme.classicCustomTheme();

  // 为空是跟随系统 en 是英文 zh 是中文
  Locale? locale;

  ThemeProvider() {
    init();
  }

  Future<void> init() async {
    customTheme = await _dataSource.readTheme();
    locale = await _localeLocalDataSource.getLocale();
    notifyListeners();
  }

  // 获取当前主题模式的显示文本
  String get themeModeText => customTheme.themeModeText;

  // 设置主题模式
  Future<void> setThemeMode(BuildContext context, ThemeMode mode) async {
    customTheme = customTheme.copyWith(themeMode: mode);
    _dataSource.writeTheme(customTheme);
    // 主动设置一下顶部状态栏
    SystemChrome.setSystemUIOverlayStyle(
      MediaQuery.of(context).platformBrightness == Brightness.light
          ? SystemUiOverlayStyle.dark
          : SystemUiOverlayStyle.light,
    );
    notifyListeners();
  }

  // 切换明暗模式
  Future<void> toggleThemeMode(bool darkMode) async {
    final mode = !darkMode ? ThemeMode.dark : ThemeMode.light;
    customTheme = customTheme.copyWith(themeMode: mode);
    _dataSource.writeTheme(customTheme);
    // 主动设置一下顶部状态栏
    SystemChrome.setSystemUIOverlayStyle(
      darkMode ? SystemUiOverlayStyle.dark : SystemUiOverlayStyle.light,
    );
    notifyListeners();
  }

  Future<void> setEnableColor(bool enable) async {
    customTheme = customTheme.copyWith(enableDynamicColor: enable);
    _dataSource.writeTheme(customTheme);
    notifyListeners();
  }

  Future<void> setSeedColor(Color color) async {
    customTheme = customTheme.copyWith(seedColor: color);
    _dataSource.writeTheme(customTheme);
    notifyListeners();
  }

  Future<void> writeTheme(CustomTheme theme) async {
    customTheme = theme;
    _dataSource.writeTheme(theme);
    notifyListeners();
  }

  // 设置当前语言
  Future<void> setLocale(Locale? locale) async {
    this.locale = locale;
    _localeLocalDataSource.writeLocale(locale);
    notifyListeners();
  }
}
