import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/category/category_local_data_source.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/icon/category_entity.dart';

part 'category_store.g.dart';

@lazySingleton
class CategoryStore = CategoryStoreBase with _$CategoryStore;

abstract class CategoryStoreBase with Store {
  final CategoryLocalDataSource _dataSource;

  CategoryStoreBase(this._dataSource) {
    fetchCategories();
  }

  @observable
  ObservableList<CategoryEntity> categories = ObservableList<CategoryEntity>();

  /// 缓存icon列表
  List<CategoryIconEntity> iconList = [];

  // 初始获取分类数据
  @action
  void fetchCategories() {
    runInAction(() async {
      final result = await _dataSource.getCategories();
      categories.clear();
      categories.addAll(result);
    });
  }
}
