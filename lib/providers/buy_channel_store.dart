import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/buy_channel/buy_channel_local_data_source.dart';
import 'package:tasks/models/buy_channel.dart';

part 'buy_channel_store.g.dart';

@lazySingleton
class BuyChannelStore = BuyChannelStoreBase with _$BuyChannelStore;

abstract class BuyChannelStoreBase with Store {
  final BuyChannelLocalDataSource _dataSource;

  BuyChannelStoreBase(this._dataSource) {
    fetchBuyChannels();
  }

  @observable
  ObservableList<BuyChannelEntity> buyChannels = ObservableList<BuyChannelEntity>();

  // 初始获取购买渠道数据
  @action
  void fetchBuyChannels() {
    runInAction(() async {
      final result = await _dataSource.getBuyChannels();
      buyChannels.clear();
      buyChannels.addAll(result);
    });
  }
}
