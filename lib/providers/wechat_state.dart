import 'package:fluwx/fluwx.dart';
import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/user/LoginUseCase.dart';
import 'package:tasks/data/user/wechat_repo.dart';
import 'package:tasks/models/user/wechat_action.dart';
import 'package:tasks/pages/base_store.dart';

part 'wechat_state.g.dart';

@lazySingleton
class WechatState = _WechatState with _$WechatState;

enum WechatEvent {
  loginSuccess,
  needRegister,
  registerSuccess,
  bindWechatSuccess,
  unbindWechatSuccess,
}

abstract class _WechatState extends BaseStore with Store {
  static String scope = "snsapi_userinfo";

  final WechatRepo repo;
  final LoginUseCase useCase;

  _WechatState(this.repo, this.useCase) {
    fluwx.registerApi(
        appId: "wx6d7916b8019763ab", universalLink: "https://i.nihaocq.com/");
  }

  Fluwx fluwx = Fluwx();

  FluwxCancelable? cancelable;

  @observable
  WechatEvent? event;

  /// 监听绑定结果
  get listener => (response) {
        if (response is WeChatAuthResponse) {
          // 监听完成后，移除
          cancelable?.cancel();
          runInAction(() {
            cancelable = null;
          });
          if (response.errCode != 0) {
            setError("微信环境异常");
            return;
          }
          final state = response.state ?? "";
          final code = response.code ?? "";
          // 绑定微信
          if (state == WechatAction.wechat_bind.name) {
            runWithLoading(() async {
              final result = await repo.bindWechat(code);
              if (result.isSuccess) {
                setEvent(WechatEvent.bindWechatSuccess);
              }
            });
            return;
          }
          // 解绑微信
          if (state == WechatAction.wechat_unbind.name) {
            runWithLoading(() async {
              final result = await repo.unbindWechat(code);
              if (result.isSuccess) {
                setEvent(WechatEvent.unbindWechatSuccess);
              }
            });
            return;
          }
          // 登录逻辑
          if (response.state == WechatAction.login.name) {
            runWithLoading(() async {
              final result = await useCase.wechatLogin(code);
              if (result.isSuccess) {
                final d = result.data;
                if (d?.loginSuccess == true) {
                  setEvent(WechatEvent.loginSuccess);
                  return;
                }
                if (d?.bindWechat == true) {
                  setEvent(WechatEvent.needRegister);
                  return;
                }
              }
            });
          }
          // 注册
          if (response.state == WechatAction.register.name) {
            runWithLoading(() async {
              final result = await useCase.wechatRegister(code);
              if (result.isSuccess) {
                setEvent(WechatEvent.registerSuccess);
              }
            });
          }
        }
      };

  /// 微信绑定
  Future<void> bindWechat() async {
    cancelable = fluwx.addSubscriber(listener); // 订阅消息
    fluwx.authBy(
        which: NormalAuth(scope: scope, state: WechatAction.wechat_bind.name));
  }

  /// 微信解绑
  Future<void> unbindWechat() async {
    cancelable = fluwx.addSubscriber(listener); // 订阅消息
    fluwx.authBy(
        which:
            NormalAuth(scope: scope, state: WechatAction.wechat_unbind.name));
  }

  /// 微信登录
  Future<void> login() async {
    cancelable = fluwx.addSubscriber(listener); // 订阅消息
    fluwx.authBy(
        which: NormalAuth(scope: scope, state: WechatAction.login.name));
  }

  /// 微信注册
  Future<void> register() async {
    cancelable = fluwx.addSubscriber(listener); // 订阅消息
    fluwx.authBy(
        which: NormalAuth(scope: scope, state: WechatAction.register.name));
  }

  @action
  void setEvent(WechatEvent event) {
    this.event = event;
  }

  @action
  void clearEvent() {
    event = null;
  }
}
