import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/data/user/models/user_model.dart';
import 'package:tasks/data/user/user_repo.dart';

part 'user_store.g.dart';

@lazySingleton
class UserStore = UserStoreBase with _$UserStore;

abstract class UserStoreBase with Store {
  final UserRepo _repo;

  UserStoreBase(this._repo) {
    _initUserInfo();
  }

  void _initUserInfo() {
    fetchUserInfo();
  }

  @observable
  UserModel? userModel;

  @computed
  bool get isLogin {
    return userModel != null;
  }

  @computed
  bool get isVip {
    return userModel?.isVip == true;
  }

  // 初始时获取用户信息
  @action
  Future<void> fetchUserInfo() async {
    final userInfo = await _repo.getUserInfoFromCache();
    runInAction(() {
      userModel = userInfo;
    });
  }

  // 用户信息发生改变，获取最新用户信息
  @action
  Future<UserModel?> syncFetchUserInfo() async {
    final result = await _repo.getUserInfo();
    // 更新本地数据
    fetchUserInfo();
    return result.data;
  }

  @action
  void logout() {
    _repo.logout();
    syncFetchUserInfo();
  }
}
