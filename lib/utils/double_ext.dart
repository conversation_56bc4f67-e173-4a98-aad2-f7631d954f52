import 'package:intl/intl.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/models/user_preferences/user_preferences_entity.dart';
import 'package:tasks/providers/user_preferences_store.dart';

extension DoubleExtensions on double? {
  String toDisplayMoney({minDigits = 1, maxDigits = 2}) {
    // 获取用户偏好设置中的货币符号
    UserPreferencesStore userPreferencesStore = getIt.get();
    UserPreferencesEntity preferences;

    try {
      // 获取用户偏好设置
      preferences = userPreferencesStore.preferencesEntity;
    } catch (e) {
      // 如果获取失败，使用默认值
      preferences = UserPreferencesEntity.defaultPreferences();
    }

    NumberFormat formatter = NumberFormat.currency(
        symbol: preferences.currencySymbol, decimalDigits: 2);
    formatter.minimumFractionDigits = minDigits; // 最少小数位数
    formatter.maximumFractionDigits = maxDigits; // 最多小数位数
    return formatter.format(this);
  }
}
