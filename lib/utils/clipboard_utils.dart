import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/utils/toast_utils.dart';

class ClipboardUtils {
  static Future<void> copyText(BuildContext context, String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      if (!context.mounted) return;
      final l10n = S.of(context);
      ToastUtils.success(context, l10n.commonCopied);
    } catch (e) {
      print(e);
    }
  }

  static Future<String?> pasteText(BuildContext context) async {
    try {
      final data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data == null) {
        if (!context.mounted) return null;
        final l10n = S.of(context);
        ToastUtils.error(context, l10n.commonClipboardEmpty);
        return null;
      }
      return data.text;
    } catch (e) {
      print(e);
      return null;
    }
  }
}
