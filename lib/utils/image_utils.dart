import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/utils/io_utils.dart';
import 'package:uuid/uuid.dart';

class ImageUtils {
  // 从图库选择图片
  static Future<AppIcon?> pickImage(BuildContext context,
      {bool edit = true}) async {
    final ImagePicker picker = ImagePicker();
    final file =
        await picker.pickImage(source: ImageSource.gallery, imageQuality: 80);
    if (file == null) return null;
    if (!context.mounted) {
      return null;
    }
    // 不编辑时直接返回
    if (!edit) {
      final savePath =
          await saveImageToCache(file.path, await file.readAsBytes());
      return AppIcon(type: IconType.local, value: savePath);
    }
    // 启用编辑
    final result = await editImage(file);
    if (result != null) {
      return AppIcon(type: IconType.local, value: result.toString());
    }
    return null;
  }

  // 拍照
  static Future<AppIcon?> takePhoto(BuildContext context,
      {bool edit = true}) async {
    final ImagePicker picker = ImagePicker();
    final file =
        await picker.pickImage(source: ImageSource.camera, imageQuality: 80);
    if (file == null) return null;
    if (!context.mounted) {
      return null;
    }
    // 不编辑时直接返回
    if (!edit) {
      final savePath =
          await saveImageToCache(file.path, await file.readAsBytes());
      return AppIcon(type: IconType.local, value: savePath);
    }
    // 启用编辑
    final result = await editImage(file);
    if (result != null) {
      return AppIcon(type: IconType.local, value: result.toString());
    }
    return null;
  }

  // 启用编辑
  static Future<String?> editImage(XFile? file) async {
    if (file == null) {
      return null;
    }
    return processImage(file);
  }

  static Future<String?> processImage(XFile? selectedImage) async {
    try {
      if (selectedImage == null) return null;

      // 获取原始图片大小
      final originalFile = File(selectedImage.path);
      final originalSize = await originalFile.length();

      // 2. 图片裁剪
      final CroppedFile? croppedFile = await ImageCropper().cropImage(
        sourcePath: selectedImage.path,
        compressQuality: _calculateQuality(originalSize), // 动态计算质量
        compressFormat: _getOutputFormat(selectedImage.path), // 智能选择格式
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: '图片裁剪',
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: false,
          ),
          IOSUiSettings(
            title: '图片裁剪',
            aspectRatioLockEnabled: false,
          ),
        ],
      );
      if (croppedFile == null) return null;

      // 3. 二次压缩（如果需要）
      final compressedBytes = await _compressImageIfNeeded(
        await croppedFile.readAsBytes(),
        originalSize,
      );

      // 4. 保存到本地
      final originPath = croppedFile.path;
      return saveImageToCache(originPath, compressedBytes);
    } on PlatformException catch (e) {
      print('平台异常: ${e.message}');
      return null;
    } on IOException catch (e) {
      print('IO异常: $e');
      return null;
    } catch (e) {
      print('未知错误: $e');
      return null;
    }
  }

  /// 保存图片到本地
  static Future<String?> saveImageToCache(String path, Uint8List bytes) async {
    try {
      // 4. 保存到本地
      final Directory appDocDir = await IoUtils.getAppDir();
      final String fileName = 'image_${Uuid().v8()}${_getFileExtension(path)}';
      final File savedFile = File('${appDocDir.path}/$fileName');

      if (!await appDocDir.exists()) {
        await appDocDir.create(recursive: true);
      }
      await savedFile.writeAsBytes(bytes, flush: true);
      debugPrint("图片文件保存在:${savedFile.path}");
      return savedFile.path;
    } catch (e) {
      print('未知错误,降级为直接使用目录: $e');
      return path;
    }
  }
}

/// 根据原始大小动态计算质量参数
int _calculateQuality(int originalSize) {
  if (originalSize > 5 * 1024 * 1024) {
    // >5MB
    return 70;
  } else if (originalSize > 2 * 1024 * 1024) {
    // 2-5MB
    return 75;
  } else if (originalSize > 1 * 1024 * 1024) {
    // 1-2MB
    return 80;
  } else {
    // <1MB
    return 85; // 小文件使用较高质量
  }
}

/// 智能选择输出格式
ImageCompressFormat _getOutputFormat(String path) {
  return path.toLowerCase().endsWith('.png')
      ? ImageCompressFormat.png
      : ImageCompressFormat.jpg;
}

/// 获取文件扩展名
String _getFileExtension(String path) {
  return path.substring(path.lastIndexOf('.'));
}

/// 如果需要，进行二次压缩
Future<Uint8List> _compressImageIfNeeded(
    Uint8List bytes, int originalSize) async {
  if (bytes.lengthInBytes <= originalSize) {
    return bytes; // 如果已经比原始小，直接返回
  }
  debugPrint("尝试执行二次压缩,数据大小为:${bytes.lengthInBytes}，原始大小为:$originalSize");
  // 使用flutter_image_compress进行二次压缩
  final result = await FlutterImageCompress.compressWithList(
    bytes,
    quality: _calculateQuality(originalSize),
    format: _getOutputFormat('') == ImageCompressFormat.png
        ? CompressFormat.png
        : CompressFormat.jpeg,
  );

  return result;
}
