import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tasks/data/user/user_repo.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/utils/toast_utils.dart';

class RouterUtils {
  /// 检查用户登录状态，未登录跳转登录页面
  static bool checkLogin(BuildContext context) {
    if (!UserRepo.isLogin()) {
      ToastUtils.show("请先登录再操作");
      context.push(Routers.login);
      return false;
    }
    return true;
  }
}
