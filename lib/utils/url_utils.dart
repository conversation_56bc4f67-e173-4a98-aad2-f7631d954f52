import 'package:url_launcher/url_launcher.dart';

class UrlUtils {
  // 安全的打开外部链接
  static void openUrlOut(String? url) {
    try {
      if (url == null || url.isEmpty) {
        return;
      }
      launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      print(e);
    }
  }

  // 安全的打开外部链接
  static void openUrlIn(String? url) {
    try {
      if (url == null || url.isEmpty) {
        return;
      }
      if (url.startsWith('http') || url.startsWith('https')) {
        launchUrl(Uri.parse(url), mode: LaunchMode.inAppWebView);
      }
    } catch (e) {
      print(e);
    }
  }
}
