import 'package:tasks/generated/l10n.dart';

class DateFormatUtils {
  static String formatDate(DateTime? dateTime) {
    if (dateTime == null) {
      return "";
    }
    return "${dateTime.year}年${dateTime.month}月${dateTime.day}日";
  }

  static String formatDateYMD(DateTime? dateTime) {
    if (dateTime == null) {
      return "";
    }
    // 使用 padLeft 确保月份和日期始终是两位数
    String year = dateTime.year.toString();
    String month = dateTime.month.toString().padLeft(2, '0');
    String day = dateTime.day.toString().padLeft(2, '0');
    return "$year-$month-$day";
  }

  /// 1个月内展示x天
  /// 1年内展示x月
  /// 1-3年展示x年
  /// 大于3年展示超过3年
  static String displayDays(int days) {
    const daysInMonth = 30; // 近似值
    const daysInYear = 365; // 近似值
    if (days < daysInMonth) {
      return S.current.commonDayFormat(days.toString());
    } else if (days < daysInYear) {
      int months = (days / daysInMonth).ceil();
      return S.current.commonMonthFormat(months.toString());
    } else if (days < 3 * daysInYear) {
      int years = (days / daysInYear).ceil();
      return S.current.commonYearFormat(years.toString());
    } else {
      return S.current.commonYearsFormat;
    }
  }
}

class AppDateUtils {
  static int daysLength(DateTime startDate, DateTime endDate,
      {bool inclusive = false, bool absolute = true}) {
    // 将时间部分设置为 00:00:00
    DateTime startDateOnly =
        DateTime(startDate.year, startDate.month, startDate.day);
    DateTime endDateOnly = DateTime(endDate.year, endDate.month, endDate.day);

    // 计算自然日差异
    int differenceInDays = endDateOnly.difference(startDateOnly).inDays;

    // 如果包含结束日期，则加 1
    if (inclusive) {
      differenceInDays += 1;
    }

    // 如果需要返回绝对值，则取绝对值
    if (absolute) {
      differenceInDays = differenceInDays.abs();
    }

    // 返回天数差异
    return differenceInDays;
  }
}
