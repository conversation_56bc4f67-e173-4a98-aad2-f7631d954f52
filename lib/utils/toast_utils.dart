import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

class ToastUtils {
  /// 提示消息
  static void show(String message) {
    if (message.isEmpty) return;
    showToast(message);
  }

  // 便捷方法保持不变
  static void success(BuildContext? context, String message) {
    showToast(message);
  }

  static void error(BuildContext? context, String message) {
    showToast(message);
  }

  static void warning(BuildContext context, String message) {
    showToast(message);
  }

  static void info(BuildContext context, String message) {
    showToast(message);
  }
}
