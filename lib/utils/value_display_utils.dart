/// 值显示工具类
class ValueDisplayUtils {
  /// 根据原始值生成对应的隐藏显示
  /// 例如：11.11 → **.**
  ///      1234.56 → ****.**
  ///      ¥1,234.56 → ¥*,***.**
  static String generateHiddenValue(String originalValue) {
    if (originalValue.isEmpty) return '****';

    String result = '';

    for (int i = 0; i < originalValue.length; i++) {
      String char = originalValue[i];
      if (_isDigit(char)) {
        result += '*';
      }
    }

    return result;
  }

  /// 判断是否为数字
  static bool _isDigit(String char) {
    return char.codeUnitAt(0) >= 48 && char.codeUnitAt(0) <= 57;
  }

  /// 生成简单的星号隐藏（固定长度）
  static String generateSimpleHidden([int length = 4]) {
    return '*' * length;
  }
}
