import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';
import 'package:tasks/common/global/GlobalInfo.dart';
import 'package:tasks/flavor.dart';
import 'package:tasks/generated/l10n.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/providers/theme_provider.dart';
import 'package:tasks/routers.dart';
import 'package:tasks/services/app_lifecycle_service.dart';
import 'package:tasks/services/exception_handler_service.dart';
import 'package:tasks/theme.dart';
import 'package:tasks/widgets/loading/loading.dart';
import 'package:tasks/widgets/security_blur_overlay.dart';

void main() => setup().then((_) {
      initFlavor();
      // 初始化全局异常处理
      ExceptionHandlerService.instance.initialize();
      runApp(MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ],
        child: GlobalLoaderOverlay(
          overlayWidgetBuilder: (_) {
            //ignored progress for the moment
            return Center(
              child: SizedBox(width: 88, height: 88, child: LoadingIndicator()),
            );
          },
          child: OKToast(child: MyApp()),
        ),
      ));
    });

class MyApp extends StatefulWidget {
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // 初始化应用生命周期服务
    AppLifecycleManager.initialize();
  }

  @override
  void dispose() {
    // 清理生命周期服务
    AppLifecycleManager.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, _) {
        final seedColor = themeProvider.customTheme.seedColor;
        return MaterialApp.router(
          debugShowCheckedModeBanner: false,
          title: GlobalInfo.appName,
          builder: (context, child) {
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                textScaler: TextScaler.linear(1.0), // 禁用系统字体缩放
              ),
              child: SecurityBlurOverlay(
                child: child!,
              ),
            );
          },
          // 设置支持的语言
          supportedLocales: S.delegate.supportedLocales,
          locale: themeProvider.locale,
          localizationsDelegates: [
            S.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          theme: AppTheme.buildTheme(context,
              isDynamic: themeProvider.customTheme.enableDynamicColor,
              seedColor: seedColor,
              brightness: Brightness.light),
          darkTheme: AppTheme.buildTheme(context,
              isDynamic: themeProvider.customTheme.enableDynamicColor,
              seedColor: seedColor,
              brightness: Brightness.dark),
          themeMode: themeProvider.customTheme.themeMode,
          routerConfig: router,
        );
      },
    );
  }
}
