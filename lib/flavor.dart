import 'package:flutter/material.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:tasks/utils/platform.dart';

class FlavorConstants {
  static const String channel = "channel";
  static const String baseUrl = "baseUrl";
  static const String backupUrl = "backupUrl";
  static const String originPrice = "lifeMemberOriginPrice";
  static const String discountPrice = "lifeMemberDiscountPrice";
}

void initFlavor() {
  const channel = String.fromEnvironment('CHANNEL');
  if (channel == "googlePlay") {
    FlavorConfig(
      name: "googlePlay",
      color: Colors.red,
      location: BannerLocation.bottomStart,
      variables: {
        FlavorConstants.channel: "googlePlay",
        FlavorConstants.baseUrl: "https://guiwu.pajiatu.com/api/",
        FlavorConstants.backupUrl: "https://guiwubackup.nihaocq.com/api/",
        FlavorConstants.originPrice: "\$12",
        FlavorConstants.discountPrice: "\$9.99",
      },
    );
    return;
  }
  var newChannel = channel.isEmpty ? "default" : channel;
  FlavorConfig(
    name: newChannel,
    color: Colors.red,
    location: BannerLocation.bottomStart,
    variables: {
      FlavorConstants.channel: newChannel,
      FlavorConstants.baseUrl: "https://a.nihaocq.com/api/",
      FlavorConstants.backupUrl: "https://guiwubackup.nihaocq.com/api/",
      FlavorConstants.originPrice: "¥24",
      FlavorConstants.discountPrice: "¥9.99",
    },
  );
}

bool isGoogle() {
  return getChannel() == "googlePlay";
}

String getFlavorValue(String key) {
  return FlavorConfig.instance.variables[key];
}

String getChannel() {
  if (isIos()) {
    return "default";
  }
  return FlavorConfig.instance.variables['channel'];
}

String getFlavorBaseUrl() {
  return FlavorConfig.instance.variables['baseUrl'];
}
