import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:tasks/widgets/exception_report_dialog.dart';

/// 全局异常处理服务
class ExceptionHandlerService {
  static ExceptionHandlerService? _instance;

  static ExceptionHandlerService get instance =>
      _instance ??= ExceptionHandlerService._();

  /// Navigator的全局key，用于显示弹窗
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  ExceptionHandlerService._();

  /// 是否已经显示了异常弹窗（防止重复显示）
  bool _isShowingDialog = false;

  /// 初始化异常处理
  void initialize() {
    // 捕获Flutter框架异常
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleFlutterError(details);
    };

    // 捕获异步异常
    PlatformDispatcher.instance.onError = (error, stack) {
      _handlePlatformError(error, stack);
      return true;
    };

    // 捕获Isolate异常
    Isolate.current.addErrorListener(RawReceivePort((pair) async {
      final List<dynamic> errorAndStacktrace = pair;
      final error = errorAndStacktrace.first;
      final stackTrace = errorAndStacktrace.last;
      _handleIsolateError(error, stackTrace);
    }).sendPort);
  }

  /// 处理Flutter框架异常
  void _handleFlutterError(FlutterErrorDetails details) {
    // 在debug模式下，仍然打印到控制台
    if (kDebugMode) {
      FlutterError.presentError(details);
      // 显示异常报告弹窗
      _showExceptionDialog(
        error: details.exception,
        stackTrace: details.stack,
        errorType: 'Flutter Error',
      );
    }
  }

  /// 处理平台异常
  bool _handlePlatformError(Object error, StackTrace stack) {
    // 在debug模式下打印异常信息
    if (kDebugMode) {
      debugPrint('Platform Error: $error');
      debugPrint('Stack Trace: $stack');
    }

    // 显示异常报告弹窗
    _showExceptionDialog(
      error: error,
      stackTrace: stack,
      errorType: 'Platform Error',
    );

    return true;
  }

  /// 处理Isolate异常
  void _handleIsolateError(dynamic error, dynamic stackTrace) {
    // 在debug模式下打印异常信息
    if (kDebugMode) {
      debugPrint('Isolate Error: $error');
      debugPrint('Stack Trace: $stackTrace');
    }

    // 显示异常报告弹窗
    _showExceptionDialog(
      error: error,
      stackTrace: stackTrace is StackTrace ? stackTrace : null,
      errorType: 'Isolate Error',
    );
  }

  /// 显示异常报告弹窗
  void _showExceptionDialog({
    required Object error,
    StackTrace? stackTrace,
    required String errorType,
  }) {
    // 获取Navigator的context
    final context = navigatorKey.currentContext;

    // 在debug模式下打印调试信息
    if (kDebugMode) {
      debugPrint('=== Exception Handler Debug ===');
      debugPrint('Navigator context is null: ${context == null}');
      debugPrint('Is showing dialog: $_isShowingDialog');
      debugPrint('Error: $error');
      debugPrint('Error Type: $errorType');
    }

    // 如果没有context或者已经在显示弹窗，则不处理
    if (context == null || _isShowingDialog) {
      if (kDebugMode) {
        debugPrint(
            'Skipping dialog: context=${context == null ? 'null' : 'available'}, showing=$_isShowingDialog');
      }
      return;
    }

    // 确保在主线程中执行UI操作
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentContext = navigatorKey.currentContext;
      if (currentContext != null && !_isShowingDialog) {
        if (kDebugMode) {
          debugPrint('Showing exception dialog...');
        }
        _isShowingDialog = true;

        showDialog(
          context: currentContext,
          barrierDismissible: false,
          builder: (context) => ExceptionReportDialog(
            error: error,
            stackTrace: stackTrace,
            errorType: errorType,
          ),
        ).then((_) {
          _isShowingDialog = false;
          if (kDebugMode) {
            debugPrint('Exception dialog closed');
          }
        }).catchError((e) {
          _isShowingDialog = false;
          if (kDebugMode) {
            debugPrint('Error showing dialog: $e');
          }
        });
      } else {
        if (kDebugMode) {
          debugPrint(
              'Cannot show dialog: context=${currentContext == null ? 'null' : 'available'}, showing=$_isShowingDialog');
        }
      }
    });
  }

  /// 手动报告异常（供其他地方调用）
  void reportException({
    required Object error,
    StackTrace? stackTrace,
    String? errorType,
  }) {
    debugPrint('触发了异常');
    _showExceptionDialog(
      error: error,
      stackTrace: stackTrace,
      errorType: errorType ?? 'Manual Report',
    );
  }
}
