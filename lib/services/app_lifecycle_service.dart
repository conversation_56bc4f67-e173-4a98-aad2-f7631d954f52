import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobx/mobx.dart';
import 'package:tasks/injection.dart';
import 'package:tasks/providers/user_preferences_store.dart';

part 'app_lifecycle_service.g.dart';

/// 应用生命周期服务
/// 管理应用前后台切换时的模糊效果，类似支付宝的安全保护
class AppLifecycleService = _AppLifecycleServiceBase with _$AppLifecycleService;

abstract class _AppLifecycleServiceBase with Store, WidgetsBindingObserver {
  final UserPreferencesStore _userPreferencesStore = getIt.get();

  _AppLifecycleServiceBase() {
    _init();
  }

  /// 是否显示模糊遮罩
  @observable
  bool showBlurOverlay = false;

  /// 模糊强度
  @observable
  double blurSigma = 10.0;

  /// 是否启用安全模糊功能
  @observable
  bool enableSecurityBlur = true;

  /// 应用当前状态
  @observable
  AppLifecycleState? currentState;

  /// 初始化服务
  void _init() {
    WidgetsBinding.instance.addObserver(this);
    _loadUserPreferences();
  }

  /// 从用户偏好设置加载配置
  @action
  Future<void> _loadUserPreferences() async {
    try {
      final preferences = _userPreferencesStore.preferencesEntity;
      enableSecurityBlur = preferences.enablePrivacyBlur;
      blurSigma = preferences.privacyBlurSigma;
    } catch (e) {
      print('加载隐私保护设置失败: $e');
      // 使用默认值
      enableSecurityBlur = true;
      blurSigma = 10.0;
    }
  }

  /// 销毁服务
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    _handleLifecycleChange(state);
  }

  /// 处理应用生命周期变化
  @action
  void _handleLifecycleChange(AppLifecycleState state) {
    currentState = state;

    if (!enableSecurityBlur) return;

    switch (state) {
      case AppLifecycleState.inactive:
        // 应用失去焦点，显示模糊遮罩
        _showBlurOverlay();
        break;
      case AppLifecycleState.paused:
        // 应用进入后台，确保模糊遮罩显示
        _showBlurOverlay();
        break;
      case AppLifecycleState.resumed:
        // 应用恢复前台，隐藏模糊遮罩
        _hideBlurOverlay();
        break;
      case AppLifecycleState.detached:
        // 应用即将销毁
        _hideBlurOverlay();
        break;
      case AppLifecycleState.hidden:
        // 应用完全隐藏（iOS 13+）
        _showBlurOverlay();
        break;
    }
  }

  /// 显示模糊遮罩
  @action
  void _showBlurOverlay() {
    showBlurOverlay = true;
  }

  /// 隐藏模糊遮罩
  @action
  void _hideBlurOverlay() {
    showBlurOverlay = false;
  }

  /// 设置模糊强度
  @action
  Future<void> setBlurSigma(double sigma) async {
    final newSigma = sigma.clamp(0.0, 20.0);
    blurSigma = newSigma;

    // 保存到用户偏好设置
    try {
      await _userPreferencesStore.updatePrivacyBlurSettings(
          privacyBlurSigma: newSigma);
    } catch (e) {
      print('保存模糊强度设置失败: $e');
    }
  }

  /// 切换安全模糊功能
  @action
  Future<void> toggleSecurityBlur(bool enabled) async {
    enableSecurityBlur = enabled;
    if (!enabled) {
      _hideBlurOverlay();
    }

    // 保存到用户偏好设置
    try {
      await _userPreferencesStore.updatePrivacyBlurSettings(
          enablePrivacyBlur: enabled);
    } catch (e) {
      print('保存隐私保护开关设置失败: $e');
    }
  }

  /// 手动触发模糊效果（用于测试）
  @action
  void triggerBlur() {
    _showBlurOverlay();
    Future.delayed(Duration(seconds: 2), () {
      _hideBlurOverlay();
    });
  }

  /// 重新加载用户偏好设置
  @action
  Future<void> reloadPreferences() async {
    await _loadUserPreferences();
  }
}

/// 应用生命周期服务单例管理器
class AppLifecycleManager {
  static AppLifecycleService? _instance;

  /// 获取单例实例
  static AppLifecycleService get instance {
    _instance ??= AppLifecycleService();
    return _instance!;
  }

  /// 初始化服务
  static void initialize() {
    instance; // 触发初始化
  }

  /// 销毁服务
  static void dispose() {
    _instance?.dispose();
    _instance = null;
  }
}
