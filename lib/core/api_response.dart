 class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final int? code;

  ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.code,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(Map<String, dynamic>)? fromJson) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'],
      code: json['code'],
      data: json['data'] != null && fromJson != null ? fromJson(json['data']) : null,
    );
  }
}