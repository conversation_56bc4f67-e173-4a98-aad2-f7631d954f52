import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:injectable/injectable.dart';
import 'package:tasks/common/global/GlobalInfo.dart';
import 'package:tasks/config/api_constants.dart';
import 'package:tasks/core/result.dart';
import 'package:tasks/data/user/user_data_source.dart';
import 'package:tasks/flavor.dart';
import 'package:tasks/utils/toast_utils.dart';

@lazySingleton
class ApiClient {
  late final Dio dio;
  final UserDataSource _source;

  String _debugUrl = "http://192.168.1.5:50001/api/";

  bool _isPrimaryAvailable = true;

  bool _isSecondaryAvailable = true;

  get primaryUrl {
    // if (kDebugMode) {
    //   return _debugUrl;
    // }
    return FlavorConfig.instance.variables[FlavorConstants.baseUrl];
  }

  get backupUrl {
    // if (kDebugMode) {
    //   return _debugUrl;
    // }
    return FlavorConfig.instance.variables[FlavorConstants.backupUrl];
  }

  ApiClient(this._source) {
    final baseOptions = BaseOptions(
      baseUrl: primaryUrl,
      connectTimeout: Duration(milliseconds: ApiConstants.connectTimeout),
      receiveTimeout: Duration(milliseconds: ApiConstants.receiveTimeout),
      sendTimeout: Duration(milliseconds: ApiConstants.receiveTimeout),
      headers: {
        'Content-Type': 'application/json',
      },
      validateStatus: (status) {
        return status! >= 200 && status < 300;
      },
    );

    dio = Dio(baseOptions);
    _setupInterceptors();
  }

  void _setupInterceptors() {
    // 添加错误处理和认证拦截器
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 如果主域名不可用，切换到备用域名
        if (_isPrimaryAvailable == false && options.baseUrl == primaryUrl) {
          options.baseUrl = backupUrl;
        }
        // 添加token到header
        final token = _getToken();
        if (token != null) {
          options.headers['token'] = token;
        }
        // 添加版本信息
        options.headers['appversion'] = GlobalInfo.appVersion;
        // 添加平台信息
        options.headers['platform'] = Platform.isAndroid ? "Android" : "IOS";
        // 渠道默认为default，没上应用市场
        options.headers['channel'] = getChannel();
        options.headers["devicearch"] = GlobalInfo.appArch;
        options.headers["userid"] = _getUserId();
        options.headers["appid"] = GlobalInfo.appId;
        return handler.next(options);
      },
      onResponse: (response, handler) {
        // 统一处理响应
        if (response.data['code'] != 0) {
          handler.reject(
            DioException(
              requestOptions: response.requestOptions,
              response: response,
              error: response.data['msg'] ?? '请求失败',
            ),
            false,
          );
          return;
        }
        return handler.next(response);
      },
      onError: (e, handler) async {
        print('请求异常拦截器:${e.type},${e.message}');

        // 如果请求失败，检查备用域名是否可用
        if (e.type == DioExceptionType.unknown ||
            e.type == DioExceptionType.connectionError ||
            e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.badCertificate) {
          // 重新检查主域名和备用域名的可用性
          await _checkAndUpdateUrlAvailability();

          // 如果主域名不可用且当前请求使用的是主域名，则切换到备用域名
          if (_isPrimaryAvailable == false &&
              e.requestOptions.baseUrl == primaryUrl) {
            final newOptions = e.requestOptions.copyWith(baseUrl: backupUrl);
            try {
              final response = await dio.request(
                newOptions.path,
                data: newOptions.data,
                queryParameters: newOptions.queryParameters,
                options: Options(
                  method: newOptions.method,
                  headers: newOptions.headers,
                  contentType: newOptions.contentType,
                ),
                cancelToken: newOptions.cancelToken,
              );
              return handler.resolve(response); // 返回重试后的响应
            } catch (retryError, stackTrace) {
              // 如果重试仍然失败，记录错误信息并返回错误
              print('重试失败: $retryError\n$stackTrace');
              return handler.next(DioException(
                requestOptions: e.requestOptions,
                error: retryError.toString(),
                type: e.type,
              ));
            }
          }
        }

        // 如果备用域名也不可用或不是上述错误类型，返回原始错误
        return handler.next(e);
      },
    ));
    // 添加日志拦截器
    if (kDebugMode) {
      dio.interceptors.add(LogInterceptor(
        request: true,
        requestHeader: true,
        requestBody: true,
        responseHeader: true,
        responseBody: true,
        error: true,
      ));
    }
    // 定时检测是否主域名可用
    checkUrlAvailability();
  }

  String? _getToken() {
    return _source.getToken();
  }

  String? _getUserId() {
    return _source.getUserInfo()?.id;
  }

  Future<void> checkUrlAvailability() async {
    // 定期检查主域名的可用性
    Timer.periodic(Duration(minutes: 5), (_) {
      _checkAndUpdateUrlAvailability();
    });
  }

  // 检查并更新域名的可用性状态
  Future<void> _checkAndUpdateUrlAvailability() async {
    try {
      final primaryResponse = await Dio().get(primaryUrl);
      _isPrimaryAvailable = primaryResponse.statusCode == 200;
    } catch (_) {
      _isPrimaryAvailable = false;
    }

    try {
      final secondaryResponse = await Dio().get(backupUrl);
      _isSecondaryAvailable = secondaryResponse.statusCode == 200;
    } catch (_) {
      _isSecondaryAvailable = false;
    }
  }

  // 便捷方法
  Future<Result<T?>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      if (fromJson != null && response.data != null) {
        return Result.success(fromJson(response.data["data"]));
      }
      return Result.success(response.data);
    } on DioException catch (e) {
      return Result.failure(e.message);
    } on Error catch (e) {
      return Result.failure(e.toString());
    }
  }

  Future<Result<T?>> post<T>(String path,
      {dynamic data,
      Map<String, dynamic>? queryParameters,
      Options? options,
      CancelToken? cancelToken,
      T Function(Map<String, dynamic>)? fromJson,
      bool? hideMsg = false}) async {
    try {
      final response = await dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      if (fromJson != null && response.data != null) {
        return Result.success(fromJson(response.data["data"]));
      }
      return Result.success(null);
    } on DioException catch (e) {
      if (hideMsg != true) {
        String errorMessage = "请求失败";
        if (e.error != null && e.error is String) {
          errorMessage = e.error.toString();
        }
        ToastUtils.error(null, errorMessage);
      }
      return Result.failure(e.response?.data["msg"],
          code: e.response?.data["code"]);
    } on Error catch (e) {
      return Result.failure(e.toString());
    }
  }
}
