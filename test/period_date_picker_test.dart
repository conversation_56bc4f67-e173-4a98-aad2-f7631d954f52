import 'package:flutter_test/flutter_test.dart';

/// 安全地给日期添加月份（复制自 period_date_picker.dart 用于测试）
DateTime _addMonths(DateTime date, int months) {
  try {
    int newYear = date.year;
    int newMonth = date.month + months;

    // 处理月份溢出
    while (newMonth > 12) {
      newYear++;
      newMonth -= 12;
    }

    while (newMonth < 1) {
      newYear--;
      newMonth += 12;
    }

    int newDay = date.day;

    // 处理月末日期（如1月31日加1个月应该是2月28/29日）
    int daysInNewMonth = DateTime(newYear, newMonth + 1, 0).day;
    if (newDay > daysInNewMonth) {
      newDay = daysInNewMonth;
    }

    return DateTime(newYear, newMonth, newDay, date.hour, date.minute,
        date.second, date.millisecond, date.microsecond);
  } catch (e) {
    // 如果计算失败，返回原始日期
    return date;
  }
}

void main() {
  group('PeriodDatePicker Tests', () {
    test('should calculate correct dates based on base date', () {
      final baseDate = DateTime(2024, 1, 15); // 2024年1月15日

      // 测试1个月后的日期
      final oneMonthLater = _addMonths(baseDate, 1);
      expect(oneMonthLater, DateTime(2024, 2, 15));

      // 测试3个月后的日期
      final threeMonthsLater = _addMonths(baseDate, 3);
      expect(threeMonthsLater, DateTime(2024, 4, 15));

      // 测试6个月后的日期
      final sixMonthsLater = _addMonths(baseDate, 6);
      expect(sixMonthsLater, DateTime(2024, 7, 15));

      // 测试9个月后的日期
      final nineMonthsLater = _addMonths(baseDate, 9);
      expect(nineMonthsLater, DateTime(2024, 10, 15));

      // 测试1年后的日期
      final oneYearLater = _addMonths(baseDate, 12);
      expect(oneYearLater, DateTime(2025, 1, 15));

      // 测试3年后的日期
      final threeYearsLater = _addMonths(baseDate, 36);
      expect(threeYearsLater, DateTime(2027, 1, 15));
    });

    test('should handle edge cases for month calculations', () {
      // 测试月末日期的处理
      final baseDate = DateTime(2024, 1, 31); // 2024年1月31日

      // 2月没有31日，应该自动调整为2月29日（2024是闰年）
      final oneMonthLater = _addMonths(baseDate, 1);
      expect(oneMonthLater, DateTime(2024, 2, 29));

      // 测试非闰年的情况
      final baseDate2023 = DateTime(2023, 1, 31); // 2023年1月31日
      final oneMonthLater2023 = _addMonths(baseDate2023, 1);
      expect(oneMonthLater2023, DateTime(2023, 2, 28)); // 2023不是闰年，调整为2月28日
    });

    test('should handle year boundary correctly', () {
      final baseDate = DateTime(2024, 12, 15); // 2024年12月15日

      // 测试跨年的月份计算
      final oneMonthLater = _addMonths(baseDate, 1);
      expect(oneMonthLater, DateTime(2025, 1, 15));

      final threeMonthsLater = _addMonths(baseDate, 3);
      expect(threeMonthsLater, DateTime(2025, 3, 15));
    });

    test('should handle null safety correctly', () {
      // 测试边界情况不会产生null值
      final baseDate = DateTime(2024, 1, 15);

      final result1 = _addMonths(baseDate, 1);
      expect(result1, isNotNull);

      final result2 = _addMonths(baseDate, 12);
      expect(result2, isNotNull);

      final result3 = _addMonths(baseDate, 36);
      expect(result3, isNotNull);
    });
  });
}
