import 'package:flutter_test/flutter_test.dart';
import 'package:tasks/models/user_preferences/user_preferences_entity.dart';

void main() {
  group('Privacy Blur Settings Tests', () {
    test('UserPreferencesEntity should have default privacy blur settings', () {
      // 测试默认配置
      final defaultPrefs = UserPreferencesEntity.defaultPreferences();
      
      expect(defaultPrefs.enablePrivacyBlur, isTrue);
      expect(defaultPrefs.privacyBlurSigma, equals(10.0));
    });

    test('UserPreferencesEntity copyWith should update privacy blur settings', () {
      // 测试copyWith方法
      final originalPrefs = UserPreferencesEntity.defaultPreferences();
      
      final updatedPrefs = originalPrefs.copyWith(
        enablePrivacyBlur: false,
        privacyBlurSigma: 15.0,
      );
      
      expect(updatedPrefs.enablePrivacyBlur, isFalse);
      expect(updatedPrefs.privacyBlurSigma, equals(15.0));
      
      // 确保其他属性没有改变
      expect(updatedPrefs.showTotalAssetValue, equals(originalPrefs.showTotalAssetValue));
      expect(updatedPrefs.currencySymbol, equals(originalPrefs.currencySymbol));
      expect(updatedPrefs.currencyCode, equals(originalPrefs.currencyCode));
    });

    test('UserPreferencesEntity should serialize and deserialize correctly', () {
      // 测试JSON序列化
      final originalPrefs = UserPreferencesEntity(
        showTotalAssetValue: false,
        currencySymbol: '\$',
        currencyCode: 'USD',
        customCurrencies: [],
        enablePrivacyBlur: false,
        privacyBlurSigma: 20.0,
      );
      
      // 序列化到JSON
      final json = originalPrefs.toJson();
      
      // 从JSON反序列化
      final deserializedPrefs = UserPreferencesEntity.fromJson(json);
      
      // 验证所有属性
      expect(deserializedPrefs.showTotalAssetValue, equals(originalPrefs.showTotalAssetValue));
      expect(deserializedPrefs.currencySymbol, equals(originalPrefs.currencySymbol));
      expect(deserializedPrefs.currencyCode, equals(originalPrefs.currencyCode));
      expect(deserializedPrefs.enablePrivacyBlur, equals(originalPrefs.enablePrivacyBlur));
      expect(deserializedPrefs.privacyBlurSigma, equals(originalPrefs.privacyBlurSigma));
    });

    test('Privacy blur sigma should be within valid range', () {
      // 测试模糊强度范围
      final prefs1 = UserPreferencesEntity(privacyBlurSigma: 0.0);
      final prefs2 = UserPreferencesEntity(privacyBlurSigma: 20.0);
      final prefs3 = UserPreferencesEntity(privacyBlurSigma: 10.0);
      
      expect(prefs1.privacyBlurSigma, equals(0.0));
      expect(prefs2.privacyBlurSigma, equals(20.0));
      expect(prefs3.privacyBlurSigma, equals(10.0));
    });

    test('Default preferences should enable privacy blur', () {
      // 测试默认情况下隐私保护应该是开启的
      final defaultPrefs = UserPreferencesEntity();
      
      expect(defaultPrefs.enablePrivacyBlur, isTrue, 
             reason: 'Privacy blur should be enabled by default for security');
      expect(defaultPrefs.privacyBlurSigma, equals(10.0),
             reason: 'Default blur sigma should be 10.0');
    });
  });
}
