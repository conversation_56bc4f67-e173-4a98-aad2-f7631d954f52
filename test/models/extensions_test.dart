import 'package:flutter_test/flutter_test.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/asset/asset_extensions.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/category_extensions.dart';
import 'package:tasks/models/category_icon.dart';

void main() {
  group('Asset Extensions Tests', () {
    test('hasLocalImages should return true for local icon', () {
      final asset = Asset(
        name: 'Test Asset',
        price: 100.0,
        purchaseDate: DateTime.now(),
        icon: AppIcon.local('/path/to/image.jpg'),
      );

      expect(asset.hasLocalImages(), true);
    });

    test('hasLocalImages should return false for remote icon', () {
      final asset = Asset(
        name: 'Test Asset',
        price: 100.0,
        purchaseDate: DateTime.now(),
        icon: AppIcon.remote('https://example.com/image.jpg'),
      );

      expect(asset.hasLocalImages(), false);
    });

    test('hasLocalImages should return false for system icon', () {
      final asset = Asset(
        name: 'Test Asset',
        price: 100.0,
        purchaseDate: DateTime.now(),
        icon: AppIcon(type: IconType.system, value: 'system_icon'),
      );

      expect(asset.hasLocalImages(), false);
    });
  });

  group('Category Extensions Tests', () {
    test('hasLocalImages should return true for local icon', () {
      final category = CategoryEntity(
        id: '1',
        name: 'Test Category',
        order: 0,
        icon: AppIcon.local('/path/to/image.jpg'),
      );

      expect(category.hasLocalImages(), true);
    });

    test('hasLocalImages should return false for remote icon', () {
      final category = CategoryEntity(
        id: '1',
        name: 'Test Category',
        order: 0,
        icon: AppIcon.remote('https://example.com/image.jpg'),
      );

      expect(category.hasLocalImages(), false);
    });

    test('hasLocalImages should return false for system icon', () {
      final category = CategoryEntity(
        id: '1',
        name: 'Test Category',
        order: 0,
        icon: AppIcon(type: IconType.system, value: 'system_icon'),
      );

      expect(category.hasLocalImages(), false);
    });
  });
}
