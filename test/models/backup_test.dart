import 'package:flutter_test/flutter_test.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/backup/backup_data.dart';
import 'package:tasks/models/backup/backup_data_extensions.dart';
import 'package:tasks/models/backup/image_download_info.dart';
import 'package:tasks/models/category.dart';
import 'package:tasks/models/category_icon.dart';
import 'package:tasks/models/sync_data.dart';

void main() {
  group('BackupData Tests', () {
    test('should create BackupData from SyncData', () {
      final syncData = SyncData(
        assets: [
          Asset(
            name: 'Test Asset',
            price: 100.0,
            purchaseDate: DateTime.now(),
            icon: AppIcon.local('/path/to/image.jpg'),
          ),
        ],
        categories: [
          CategoryEntity(
            id: '1',
            name: 'Test Category',
            order: 0,
            icon: AppIcon.local('/path/to/category.jpg'),
          ),
        ],
      );

      final backupData = BackupData.create(syncData);

      expect(backupData.version, '1.0');
      expect(backupData.syncData.assets.length, 1);
      expect(backupData.syncData.categories.length, 1);
      expect(backupData.timestamp, isNotEmpty);
    });

    test('should create BackupData from Map', () {
      final map = {
        'timestamp': '2023-01-01T00:00:00.000Z',
        'version': '1.0',
        'syncData': {
          'assets': [
            {
              'id': '1',
              'name': 'Test Asset',
              'price': 100.0,
              'purchaseDate': '2023-01-01T00:00:00.000Z',
              'createdAt': '2023-01-01T00:00:00.000Z',
              'isInService': true,
              'isPinned': false,
              'isFavorite': false,
              'extraFees': [],
              'icon': {
                'type': 'local',
                'value': '/path/to/image.jpg',
              },
            },
          ],
          'categories': [
            {
              'id': '1',
              'name': 'Test Category',
              'order': 0,
              'icon': {
                'type': 'local',
                'value': '/path/to/category.jpg',
              },
            },
          ],
        },
      };

      final backupData = BackupData.fromMap(map);

      expect(backupData.version, '1.0');
      expect(backupData.timestamp, '2023-01-01T00:00:00.000Z');
      expect(backupData.syncData.assets.length, 1);
      expect(backupData.syncData.categories.length, 1);
    });
  });

  group('ImageDownloadInfo Tests', () {
    test('should create ImageDownloadInfo correctly', () {
      final info = ImageDownloadInfo(
        originalPath: '/original/path/image.jpg',
        filename: 'image.jpg',
        remotePath: '/remote/images/image.jpg',
        localPath: '/local/images/image.jpg',
        entityId: 'asset1',
        entityType: 'asset',
      );

      expect(info.filename, 'image.jpg');
      expect(info.entityType, 'asset');
      expect(info.entityId, 'asset1');
    });
  });
}
