import 'package:flutter_test/flutter_test.dart';
import 'package:tasks/pages/asset/batch_manage/batch_manage_state.dart';
import 'package:tasks/models/asset/asset_card.dart';
import 'package:tasks/models/asset/asset.dart';
import 'package:tasks/models/category.dart';

void main() {
  group('Batch Manage Tests', () {
    late BatchManageState state;

    setUp(() {
      state = BatchManageState();
    });

    test('should initialize with empty assets and categories', () {
      expect(state.assets.isEmpty, isTrue);
      expect(state.categories.isEmpty, isTrue);
      expect(state.selectedAssetIds.isEmpty, isTrue);
      expect(state.isReorderMode, isFalse);
    });

    test('should toggle asset selection correctly', () {
      // 创建测试资产
      final asset = Asset(
        name: 'Test Asset',
        price: 100.0,
        purchaseDate: DateTime.now(),
        categoryId: null,
      );

      final testAsset = AssetCardEntity(
        asset: asset,
        id: asset.id,
        name: asset.name,
        price: asset.price,
        isFavorite: asset.isFavorite,
        isInService: asset.isInService,
        icon: asset.icon,
        categoryName: null,
      );

      state.assets.add(testAsset);

      // 测试选择
      expect(state.selectedAssetIds.contains('test-1'), isFalse);
      state.toggleAssetSelection('test-1');
      expect(state.selectedAssetIds.contains('test-1'), isTrue);

      // 测试取消选择
      state.toggleAssetSelection('test-1');
      expect(state.selectedAssetIds.contains('test-1'), isFalse);
    });

    test('should handle select all correctly', () {
      // 添加多个测试资产
      for (int i = 0; i < 3; i++) {
        final asset = Asset(
          name: 'Test Asset $i',
          price: 100.0 * (i + 1),
          purchaseDate: DateTime.now(),
          categoryId: null,
        );

        final testAsset = AssetCardEntity(
          asset: asset,
          id: asset.id,
          name: asset.name,
          price: asset.price,
          isFavorite: asset.isFavorite,
          isInService: asset.isInService,
          icon: asset.icon,
          categoryName: null,
        );
        state.assets.add(testAsset);
      }

      // 测试全选
      expect(state.isAllSelected, isFalse);
      state.selectAll();
      expect(state.isAllSelected, isTrue);
      expect(state.selectedCount, equals(3));

      // 测试取消全选
      state.selectAll();
      expect(state.isAllSelected, isFalse);
      expect(state.selectedCount, equals(0));
    });

    test('should toggle reorder mode correctly', () {
      expect(state.isReorderMode, isFalse);

      state.toggleReorderMode();
      expect(state.isReorderMode, isTrue);

      state.toggleReorderMode();
      expect(state.isReorderMode, isFalse);
    });

    test('should clear selection when entering reorder mode', () {
      // 添加测试资产并选择
      final asset = Asset(
        name: 'Test Asset',
        price: 100.0,
        purchaseDate: DateTime.now(),
        categoryId: null,
      );

      final testAsset = AssetCardEntity(
        asset: asset,
        id: asset.id,
        name: asset.name,
        price: asset.price,
        isFavorite: asset.isFavorite,
        isInService: asset.isInService,
        icon: asset.icon,
        categoryName: null,
      );

      state.assets.add(testAsset);
      state.toggleAssetSelection('test-1');
      expect(state.selectedCount, equals(1));

      // 进入排序模式应该清除选择
      state.toggleReorderMode();
      expect(state.selectedCount, equals(0));
      expect(state.isReorderMode, isTrue);
    });

    test('should reorder assets correctly', () {
      // 添加多个测试资产
      for (int i = 0; i < 3; i++) {
        final asset = Asset(
          name: 'Test Asset $i',
          price: 100.0 * (i + 1),
          purchaseDate: DateTime.now(),
          categoryId: null,
        );

        final testAsset = AssetCardEntity(
          asset: asset,
          id: asset.id,
          name: asset.name,
          price: asset.price,
          isFavorite: asset.isFavorite,
          isInService: asset.isInService,
          icon: asset.icon,
          categoryName: null,
        );
        state.assets.add(testAsset);
      }

      // 验证初始顺序
      expect(state.assets[0].id, equals('test-0'));
      expect(state.assets[1].id, equals('test-1'));
      expect(state.assets[2].id, equals('test-2'));

      // 重新排序：将第一个移到最后
      state.reorderAssets(0, 3);

      // 验证新顺序
      expect(state.assets[0].id, equals('test-1'));
      expect(state.assets[1].id, equals('test-2'));
      expect(state.assets[2].id, equals('test-0'));
    });

    test('should calculate computed properties correctly', () {
      expect(state.hasSelectedAssets, isFalse);
      expect(state.selectedCount, equals(0));

      // 添加资产并选择
      final asset = Asset(
        name: 'Test Asset',
        price: 100.0,
        purchaseDate: DateTime.now(),
        categoryId: null,
      );

      final testAsset = AssetCardEntity(
        asset: asset,
        id: asset.id,
        name: asset.name,
        price: asset.price,
        isFavorite: asset.isFavorite,
        isInService: asset.isInService,
        icon: asset.icon,
        categoryName: null,
      );

      state.assets.add(testAsset);
      state.toggleAssetSelection('test-1');

      expect(state.hasSelectedAssets, isTrue);
      expect(state.selectedCount, equals(1));
    });
  });
}
