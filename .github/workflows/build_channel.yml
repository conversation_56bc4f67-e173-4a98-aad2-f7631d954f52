#name: Flutter Build on Version
#
#on:
#  push:
#    branches: [ feature/1.8.3 ]
#    if: "contains(github.event.head_commit.message, 'version:')"
#
#jobs:
#  build:
#    name: Flutter Build
#    runs-on: ubuntu-latest
#    environment: production  # Optional: Add environment protection
#
#    steps:
#      - uses: actions/checkout@v4
#      - uses: actions/setup-java@v4
#        with:
#          distribution: 'zulu'
#          java-version: 21
#      - uses: subosito/flutter-action@v2
#        with:
#          flutter-version: '3.29.2'
#      - name: Install jq
#        run: sudo apt-get update && sudo apt-get install -y jq
#      - name: Setup Android SDK
#        run: |
#          # 接受Android SDK许可证
#          yes | flutter doctor --android-licenses || true
#
#          # 安装必要的Android SDK组件
#          echo "Installing Android SDK Platform 33..."
#          yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "platforms;android-33" || true
#
#          echo "Installing NDK 27.0.12077973..."
#          yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "ndk;27.0.12077973" || true
#
#          echo "Installing Build Tools 33.0.2..."
#          yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "build-tools;33.0.2" || true
#
#          # 验证Flutter doctor
#          flutter doctor -v
#      - name: Build multi-channel APKs
#        run: ./scripts/build_channels.sh
#      - name: Upload channel APKs
#        uses: actions/upload-artifact@v4
#        with:
#          name: channel-apks
#          path: build/channels/
